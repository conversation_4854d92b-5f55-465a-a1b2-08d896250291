#!/usr/bin/env python3
"""
Comprehensive Integration Validation for Standardized API Endpoints

This script validates that all system components work together properly
after the API standardization changes.
"""

import asyncio
import json
import logging
import os
import sys
import tempfile
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")
logger = logging.getLogger(__name__)


async def test_component_imports():
    """Test that all components can be imported successfully."""
    print("🔍 Testing Component Imports...")
    
    try:
        # Test auth imports
        from src.auth import get_simple_auth_manager, extract_user_id_from_api_key
        print("✅ Auth components imported")
        
        # Test cache imports
        from src.cache import get_hybrid_cache, initialize_cache_system
        print("✅ Cache components imported")
        
        # Test job manager imports
        from src.processing.job_completion import get_job_manager
        print("✅ Job manager imported")
        
        # Test API endpoint imports
        from src.api.endpoints import register_simple_api_endpoints
        print("✅ API endpoints imported")
        
        # Test VSCode integration imports
        from src.integrations.vscode import add_vscode_integration
        print("✅ VSCode integration imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False


async def test_auth_system():
    """Test authentication system functionality."""
    print("\n🔐 Testing Authentication System...")
    
    try:
        from src.auth import get_simple_auth_manager, extract_user_id_from_api_key
        
        # Use temporary database
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            db_path = tmp.name
        
        try:
            auth_manager = get_simple_auth_manager(db_path)
            print("✅ Auth manager initialized")
            
            # Test user creation
            user = auth_manager.create_user_with_key('<EMAIL>', 'Test User')
            if user and user.api_key and user.api_key.startswith('mcp_'):
                print("✅ User creation successful")
                print(f"   User ID: {user.id}")
                print(f"   API Key: {user.api_key[:12]}...")
                
                # Test API key validation
                user_id = extract_user_id_from_api_key(user.api_key)
                if user_id == user.id:
                    print("✅ API key validation working")
                else:
                    print(f"❌ API key validation failed: {user_id} != {user.id}")
                    return False
                    
                # Test user retrieval
                retrieved_user = auth_manager.validate_api_key(user.api_key)
                if retrieved_user and retrieved_user.id == user.id:
                    print("✅ User retrieval working")
                else:
                    print("❌ User retrieval failed")
                    return False
                    
                return True
            else:
                print("❌ User creation failed")
                return False
                
        finally:
            # Cleanup
            try:
                os.unlink(db_path)
            except:
                pass
                
    except Exception as e:
        print(f"❌ Auth system test failed: {e}")
        return False


async def test_cache_system():
    """Test cache system initialization."""
    print("\n💾 Testing Cache System...")
    
    try:
        from src.cache import initialize_cache_system, get_hybrid_cache
        
        # Test cache initialization
        cache_init = await initialize_cache_system()
        print(f"✅ Cache system initialized: {cache_init}")
        
        # Test cache retrieval
        cache = await get_hybrid_cache()
        if cache:
            print("✅ Cache instance retrieved")
            
            # Test health check
            health = await cache.health_check()
            print(f"✅ Cache health check: {health.get('hybrid_system', 'unknown')}")
            
            return True
        else:
            print("❌ Cache instance retrieval failed")
            return False
            
    except Exception as e:
        print(f"❌ Cache system test failed: {e}")
        return False


async def test_job_manager():
    """Test job manager functionality."""
    print("\n⚙️ Testing Job Manager...")
    
    try:
        from src.processing.job_completion import get_job_manager, JobType
        
        job_manager = get_job_manager()
        print("✅ Job manager initialized")
        
        # Test job creation
        job_id = job_manager.create_job(
            job_type=JobType.CHUNK_PROCESSING,
            user_id="test_user_123",
            metadata={"test": "data"}
        )
        
        if job_id:
            print(f"✅ Job created: {job_id}")
            
            # Test job retrieval
            job = job_manager.get_job(job_id)
            if job and job.job_id == job_id:
                print("✅ Job retrieval working")
                return True
            else:
                print("❌ Job retrieval failed")
                return False
        else:
            print("❌ Job creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Job manager test failed: {e}")
        return False


async def test_response_format():
    """Test standardized response format."""
    print("\n📋 Testing Response Format...")
    
    try:
        from src.api.endpoints import create_api_response
        
        # Test success response
        success_response = create_api_response({"test": "data"})
        response_data = json.loads(success_response.body.decode())
        
        if (response_data.get("success") is True and 
            "data" in response_data and 
            "meta" in response_data and
            "timestamp" in response_data["meta"] and
            "version" in response_data["meta"] and
            "request_id" in response_data["meta"]):
            print("✅ Success response format correct")
        else:
            print(f"❌ Success response format incorrect: {response_data}")
            return False
        
        # Test error response
        error_response = create_api_response(
            {"code": "TEST_ERROR", "message": "Test error"}, 
            success=False, 
            status_code=400
        )
        error_data = json.loads(error_response.body.decode())
        
        if (error_data.get("success") is False and 
            "error" in error_data and 
            "meta" in error_data and
            error_response.status_code == 400):
            print("✅ Error response format correct")
        else:
            print(f"❌ Error response format incorrect: {error_data}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Response format test failed: {e}")
        return False


async def test_mcp_server_creation():
    """Test MCP server creation and configuration."""
    print("\n🚀 Testing MCP Server Creation...")
    
    try:
        from mcp.server.fastmcp import FastMCP
        
        # Test server creation (similar to server.py)
        mcp = FastMCP(
            "Test String MCP Server",
            stateless_http=True,
            json_response=True
        )
        
        if mcp:
            print("✅ FastMCP server created")
            print(f"   Name: {mcp.name}")
            print(f"   Stateless HTTP: {mcp.settings.stateless_http}")
            return True
        else:
            print("❌ FastMCP server creation failed")
            return False
            
    except Exception as e:
        print(f"❌ MCP server creation test failed: {e}")
        return False


async def test_data_flow_simulation():
    """Simulate a complete data flow through the system."""
    print("\n🔄 Testing Data Flow Simulation...")
    
    try:
        from src.auth import get_simple_auth_manager
        from src.processing.job_completion import get_job_manager, JobType
        
        # Use temporary database
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            db_path = tmp.name
        
        try:
            # Step 1: Create user and API key
            auth_manager = get_simple_auth_manager(db_path)
            user = auth_manager.create_user_with_key('<EMAIL>', 'Flow Test')
            
            if not user:
                print("❌ User creation failed in data flow")
                return False
            
            print(f"✅ Step 1: User created with API key")
            
            # Step 2: Create a job for the user
            job_manager = get_job_manager()
            job_id = job_manager.create_job(
                job_type=JobType.CHUNK_PROCESSING,
                user_id=user.id,
                metadata={
                    "file_path": "/test/file.py",
                    "chunk_index": 0,
                    "source": "integration_test"
                }
            )
            
            if not job_id:
                print("❌ Job creation failed in data flow")
                return False
                
            print(f"✅ Step 2: Job created ({job_id})")
            
            # Step 3: Start the job
            success = job_manager.start_job(job_id, total_steps=3)
            if not success:
                print("❌ Job start failed in data flow")
                return False
                
            print("✅ Step 3: Job started")
            
            # Step 4: Update job progress
            job_manager.update_progress(job_id, 1, "Processing chunk")
            job_manager.update_progress(job_id, 2, "Generating embedding")
            job_manager.update_progress(job_id, 3, "Storing result")
            
            print("✅ Step 4: Job progress updated")
            
            # Step 5: Complete the job
            job_manager.complete_job(job_id, success=True, result_data={
                "chunks_processed": 1,
                "embeddings_generated": 1,
                "processing_time_ms": 1500
            })
            
            print("✅ Step 5: Job completed")
            
            # Step 6: Verify job state
            final_job = job_manager.get_job(job_id)
            if final_job:
                # Check if job is completed (handle both enum and string values)
                status_value = final_job.status.value if hasattr(final_job.status, 'value') else str(final_job.status)
                if status_value == "COMPLETED" or str(final_job.status) == "JobStatus.COMPLETED":
                    print("✅ Step 6: Job state verified")
                    return True
                else:
                    print(f"❌ Job state verification failed: {status_value}")
                    return False
            else:
                print("❌ Job state verification failed: Job not found")
                return False
                
        finally:
            # Cleanup
            try:
                os.unlink(db_path)
            except:
                pass
                
    except Exception as e:
        print(f"❌ Data flow simulation failed: {e}")
        return False


async def main():
    """Run all integration validation tests."""
    print("🚀 Starting Comprehensive Integration Validation")
    print("=" * 60)
    
    tests = [
        ("Component Imports", test_component_imports),
        ("Authentication System", test_auth_system),
        ("Cache System", test_cache_system),
        ("Job Manager", test_job_manager),
        ("Response Format", test_response_format),
        ("MCP Server Creation", test_mcp_server_creation),
        ("Data Flow Simulation", test_data_flow_simulation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 Integration Validation Summary")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All integration tests PASSED! System is ready.")
        return True
    else:
        print("⚠️  Some integration tests FAILED. Review issues above.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

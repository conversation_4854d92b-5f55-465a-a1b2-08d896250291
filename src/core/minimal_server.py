"""
Minimal MCP Server Implementation

Lightweight, core MCP server focused purely on MCP protocol compliance
and essential tools. No HTTP API, caching, or VSCode integration.

Features:
- Pure MCP protocol (stdio transport only)
- Essential code analysis tools
- Minimal dependencies
- Fast startup time
- Low resource usage
"""

import logging
import sys
from datetime import datetime
from typing import Optional

from mcp.server.fastmcp import FastMCP
from .tools_minimal import register_minimal_tools

logger = logging.getLogger(__name__)


def create_minimal_mcp_server() -> FastMCP:
    """
    Create a minimal MCP server with only essential functionality.
    
    Returns:
        Configured FastMCP server instance
    """
    # Create minimal FastMCP server
    # No lifespan management, caching, or HTTP features
    mcp = FastMCP(
        "Minimal MCP Server",
        # Minimal configuration - stdio only
        stateless_http=False,  # No HTTP support
        json_response=False    # Use standard MCP responses
    )
    
    logger.info("Created minimal MCP server instance")
    return mcp


def setup_minimal_resources(mcp: FastMCP) -> None:
    """
    Setup minimal MCP resources.
    
    Args:
        mcp: FastMCP server instance
    """
    @mcp.resource("health://minimal")
    def get_minimal_health() -> str:
        """Get minimal server health status."""
        try:
            status = {
                "status": "healthy",
                "server_type": "minimal_mcp",
                "timestamp": datetime.utcnow().isoformat(),
                "transport": "stdio"
            }
            return str(status)
        except Exception as e:
            return f"Error: {str(e)}"
    
    @mcp.resource("info://server")
    def get_server_info() -> str:
        """Get minimal server information."""
        try:
            info = {
                "name": "Minimal MCP Server",
                "version": "1.0.0",
                "type": "minimal",
                "capabilities": ["tools", "resources"],
                "transport": "stdio",
                "description": "Lightweight MCP server for basic code analysis"
            }
            return str(info)
        except Exception as e:
            return f"Error: {str(e)}"
    
    logger.info("✅ Minimal MCP resources registered")


def main() -> None:
    """
    Main entry point for the minimal MCP server.
    
    Provides a lightweight alternative to the full-featured server
    with only essential MCP functionality.
    """
    # Setup minimal logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    
    logger.info("🚀 Starting Minimal MCP Server...")
    logger.info("Mode: Lightweight, stdio transport only")
    
    try:
        # Create minimal server
        mcp = create_minimal_mcp_server()
        
        # Register minimal tools
        logger.info("Registering minimal tools...")
        register_minimal_tools(mcp)
        logger.info("Minimal tools registration completed")
        
        # Setup minimal resources
        logger.info("Setting up minimal resources...")
        setup_minimal_resources(mcp)
        logger.info("Minimal resources setup completed")
        
        # Log server capabilities
        logger.info("Minimal MCP Server ready:")
        logger.info("  Transport: stdio (MCP protocol)")
        logger.info("  Tools: analyze_code, search_code, server_info")
        logger.info("  Resources: health://minimal, info://server")
        logger.info("  Features: Core MCP functionality only")
        logger.info("  Dependencies: Minimal (no Redis, no HTTP API)")
        
        # Start server with stdio transport only
        logger.info("Starting minimal MCP server with stdio transport...")
        mcp.run()  # Always stdio for minimal server
        
    except KeyboardInterrupt:
        logger.info("Minimal MCP server stopped by user")
    except Exception as e:
        logger.error(f"Failed to start minimal MCP server: {e}")
        logger.error(f"Exception details: {type(e).__name__}: {str(e)}")
        
        # Minimal error diagnostics
        if "Permission denied" in str(e):
            logger.error("DIAGNOSIS: Permission denied - check file permissions")
        elif "No module named" in str(e):
            logger.error("DIAGNOSIS: Missing Python module - check dependencies")
        
        sys.exit(1)


if __name__ == "__main__":
    main()

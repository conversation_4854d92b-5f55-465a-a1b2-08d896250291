"""
Minimal tools for core MCP server.

Contains only essential tools for basic code analysis functionality,
without heavy dependencies or optional features.
"""

import json
import logging
from typing import Optional, TYPE_CHECKING
from pydantic import Field

if TYPE_CHECKING:
    from mcp.server.fastmcp import FastMCP

logger = logging.getLogger(__name__)


def analyze_code_structure(code_content: str, file_path: Optional[str] = None) -> str:
    """
    Basic code structure analysis without heavy dependencies.
    
    Args:
        code_content: The code content to analyze
        file_path: Optional file path for context
        
    Returns:
        JSON string with basic analysis results
    """
    try:
        lines = code_content.split('\n')
        
        # Basic analysis
        analysis = {
            "file_path": file_path or "unknown",
            "total_lines": len(lines),
            "non_empty_lines": len([line for line in lines if line.strip()]),
            "functions": [],
            "classes": [],
            "imports": [],
            "comments": 0,
            "complexity_estimate": 0
        }
        
        # Simple pattern matching for Python
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            
            if stripped.startswith('#'):
                analysis["comments"] += 1
            elif stripped.startswith('def '):
                func_name = stripped.split('(')[0].replace('def ', '').strip()
                analysis["functions"].append({
                    "name": func_name,
                    "line": i,
                    "type": "function"
                })
            elif stripped.startswith('class '):
                class_name = stripped.split('(')[0].split(':')[0].replace('class ', '').strip()
                analysis["classes"].append({
                    "name": class_name,
                    "line": i,
                    "type": "class"
                })
            elif stripped.startswith(('import ', 'from ')):
                analysis["imports"].append({
                    "statement": stripped,
                    "line": i
                })
            
            # Simple complexity estimation
            if any(keyword in stripped for keyword in ['if ', 'for ', 'while ', 'try:', 'except', 'with ']):
                analysis["complexity_estimate"] += 1
        
        analysis["status"] = "success"
        return json.dumps(analysis, indent=2)
        
    except Exception as e:
        logger.error(f"Error analyzing code structure: {e}")
        return json.dumps({
            "status": "error",
            "error": str(e),
            "file_path": file_path or "unknown"
        })


def search_code_content(code_content: str, search_term: str, file_path: Optional[str] = None) -> str:
    """
    Search for a term in code content.
    
    Args:
        code_content: The code content to search
        search_term: Term to search for
        file_path: Optional file path for context
        
    Returns:
        JSON string with search results
    """
    try:
        lines = code_content.split('\n')
        matches = []
        
        for i, line in enumerate(lines, 1):
            if search_term.lower() in line.lower():
                matches.append({
                    "line_number": i,
                    "content": line.strip(),
                    "match_type": "content"
                })
        
        result = {
            "status": "success",
            "search_term": search_term,
            "file_path": file_path or "unknown",
            "total_matches": len(matches),
            "matches": matches[:50]  # Limit to first 50 matches
        }
        
        if len(matches) > 50:
            result["note"] = f"Showing first 50 of {len(matches)} matches"
        
        return json.dumps(result, indent=2)
        
    except Exception as e:
        logger.error(f"Error searching code content: {e}")
        return json.dumps({
            "status": "error",
            "error": str(e),
            "search_term": search_term,
            "file_path": file_path or "unknown"
        })


def register_minimal_tools(mcp_server: "FastMCP") -> None:
    """
    Register minimal tools with the MCP server.
    
    These tools provide basic functionality without heavy dependencies:
    - Code structure analysis
    - Content searching
    - Basic health checks
    
    Args:
        mcp_server: The FastMCP server instance to register tools with
    """
    logger.info("Registering minimal MCP tools...")
    
    @mcp_server.tool()
    def analyze_code_tool(code_content: str, 
                         file_path: Optional[str] = Field(default=None, 
                                                        description="Optional file path for context")) -> str:
        """
        Analyze code structure and extract basic information.
        
        This tool provides basic code analysis including:
        - Line counts and statistics
        - Function and class detection
        - Import statements
        - Complexity estimation
        
        Args:
            code_content: The code content to analyze
            file_path: Optional file path for context
            
        Returns:
            JSON string with analysis results
        """
        return analyze_code_structure(code_content, file_path)
    
    @mcp_server.tool()
    def search_code_tool(code_content: str,
                        search_term: str,
                        file_path: Optional[str] = Field(default=None,
                                                       description="Optional file path for context")) -> str:
        """
        Search for a term within code content.
        
        Performs case-insensitive search and returns matching lines
        with line numbers and context.
        
        Args:
            code_content: The code content to search
            search_term: Term to search for
            file_path: Optional file path for context
            
        Returns:
            JSON string with search results
        """
        return search_code_content(code_content, search_term, file_path)
    
    @mcp_server.tool()
    def get_server_info_tool() -> str:
        """
        Get minimal server information and capabilities.

        Returns basic information about the minimal MCP server
        including available tools and capabilities.

        Returns:
            JSON string with server information
        """
        info = {
            "server_type": "minimal_mcp",
            "version": "1.0.0",
            "capabilities": ["tools", "resources"],
            "transport": "stdio",
            "tools": [
                "analyze_code_tool",
                "search_code_tool",
                "get_server_info_tool"
            ],
            "description": "Lightweight MCP server for basic code analysis"
        }
        return json.dumps(info, indent=2)

    logger.info("✅ Minimal MCP tools registered successfully")
    logger.info("Available tools:")
    logger.info("  - analyze_code_tool: Basic code structure analysis")
    logger.info("  - search_code_tool: Content searching")
    logger.info("  - get_server_info_tool: Server information")

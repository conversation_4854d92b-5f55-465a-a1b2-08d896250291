"""
Base utilities and shared state management for MCP tools.
Contains common functionality used across all tool modules.
API key-based authentication support.
"""

from pathlib import Path
from typing import Dict, Optional, Tuple

try:
    from ..processing.metadata_extractor import MetadataExtractor, ProjectMetadata
except ImportError:
    from processing.metadata_extractor import MetadataExtractor, ProjectMetadata


# Global shared state
metadata_extractor: Optional[MetadataExtractor] = None
# User-isolated project cache: api_key_prefix -> project_cache
user_project_cache: Dict[str, Dict[str, ProjectMetadata]] = {}


def get_metadata_extractor() -> MetadataExtractor:
    """Get or create the metadata extractor instance."""
    global metadata_extractor
    if metadata_extractor is None:
        metadata_extractor = MetadataExtractor()
    return metadata_extractor


def get_current_user_id() -> str:
    """
    Get current user ID from API key or environment.
    Uses API key prefix as user identifier for isolation.
    """
    import os
    import logging

    logger = logging.getLogger(__name__)

    # Try to get from MCP server auth context (mcp_ keys only)
    try:
        from ..mcp_server import auth_manager, _current_api_key
        from ..auth import extract_user_id_from_api_key

        # Check if we have a current API key in context
        if _current_api_key and _current_api_key.startswith('mcp_'):
            user_id = extract_user_id_from_api_key(_current_api_key)
            if user_id:
                logger.debug(f"Using API key context for user ID: {user_id}")
                return user_id

        # Try auth manager if available
        if auth_manager and hasattr(auth_manager, '_current_api_key'):
            api_key = auth_manager._current_api_key
            if api_key and api_key.startswith('mcp_'):
                user_id = extract_user_id_from_api_key(api_key)
                if user_id:
                    logger.debug(f"Using auth manager for user ID: {user_id}")
                    return user_id
    except (ImportError, AttributeError) as e:
        logger.debug(f"Auth context not available: {e}")

    # Fallback to environment variable (for direct mcp_ API key usage)
    api_key = os.environ.get('MCP_API_KEY', '')
    if api_key and api_key.startswith('mcp_'):
        try:
            from ..auth import extract_user_id_from_api_key
            user_id = extract_user_id_from_api_key(api_key)
            if user_id:
                logger.debug(f"Using environment API key for user ID: {user_id}")
                return user_id
        except ImportError:
            pass

    # Default user for development/testing
    default_user = os.environ.get('MCP_USER_ID', 'default_user')
    logger.debug(f"Using default user ID: {default_user}")
    return default_user


def get_user_project_cache(user_id: str) -> Dict[str, ProjectMetadata]:
    """Get or create user-specific project cache."""
    global user_project_cache
    if user_id not in user_project_cache:
        user_project_cache[user_id] = {}
    return user_project_cache[user_id]


def get_project_cache() -> Dict[str, ProjectMetadata]:
    """Get the current user's project cache."""
    user_id = get_current_user_id()
    return get_user_project_cache(user_id)


# Backward compatibility - make project_cache a function that returns current user's cache
def project_cache() -> Dict[str, ProjectMetadata]:
    """Get the current user's project cache (backward compatibility)."""
    return get_project_cache()


# Removed workspace detection functions - violates MCP remote operation principles
# In MCP remote mode:
# - Code is streamed from IDE/client, not discovered on server
# - No automatic filesystem scanning
# - User must explicitly provide code content


def _get_or_auto_index_project(
    project_path: Optional[str] = None
) -> Tuple[Optional[ProjectMetadata], str]:
    """
    Get project metadata from cache or require explicit project path.
    
    MCP Remote Operation Principle:
    - No automatic workspace detection
    - Code must be streamed from IDE
    - Explicit project paths only

    Returns:
        Tuple of (project_metadata, status_message)
    """
    import os

    # Get current user ID for cache isolation
    user_id = get_current_user_id()
    user_cache = get_user_project_cache(user_id)

    if project_path:
        project_path = str(Path(project_path).resolve())
        if project_path in user_cache:
            return user_cache[project_path], ""
        else:
            # Require explicit indexing for MCP remote operation
            msg = f"❌ Project not indexed. Please use index_project() first with code streamed from your IDE."
            return None, msg
    else:
        # Use cached project if available
        if user_cache:
            return next(iter(user_cache.values())), ""
        else:
            # No automatic workspace detection for MCP remote operation
            msg = f"❌ No projects indexed. Please specify a project_path and stream code from your IDE."
            return None, msg


def _is_server_directory(path: str) -> bool:
    """
    Check if a path is a server directory that should not be analyzed.
    
    Args:
        path: Path to check
        
    Returns:
        True if the path is a server directory (should be blocked)
    """
    import os
    
    # Convert to absolute path
    abs_path = Path(path).resolve()
    
    # Block server directories
    blocked_paths = [
        "/app",
        "/opt",
        "/usr",
        "/var",
        "/etc",
        "/root",
        "/home/<USER>",
        "/railway",
        "/tmp/railway"
    ]
    
    # Also block if path contains server indicators
    server_indicators = [
        "railway",
        "docker", 
        "container",
        "server.py",
        "main.py",
        "requirements.txt"
    ]
    
    # Check if path is in blocked directories
    for blocked in blocked_paths:
        if str(abs_path).startswith(blocked):
            return True
    
    # Check if directory contains server files (indicates it's the server itself)
    try:
        if (abs_path / "src" / "server.py").exists():
            return True
        if (abs_path / "pyproject.toml").exists():
            return True  
        if (abs_path / "railway.toml").exists():
            return True
    except:
        pass
        
    return False
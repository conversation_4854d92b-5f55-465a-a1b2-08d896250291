"""
Base classes for metadata extraction system.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, Any, List, Optional


@dataclass
class MetadataExtractionResult:
    """Result of metadata extraction from a code chunk with Python 3.13 support."""

    # Primary identifiers
    function_name: str = ""
    class_name: str = ""

    # All found items
    functions_found: List[str] = None
    classes_found: List[str] = None

    # Chunk classification
    chunk_type: str = "module"  # module, function, class, import, type_alias, etc.
    language: str = ""

    # Code analysis
    has_code: bool = False
    line_count: int = 0
    complexity_score: int = 0

    # Additional metadata
    imports: List[str] = None
    dependencies: List[str] = None
    docstring: str = ""

    # Python 3.13 specific features
    type_aliases: List[str] = None
    python313_features: List[str] = None

    # Extraction metadata
    extraction_method: str = ""  # ast, regex, heuristic, ast_enhanced, regex_enhanced
    confidence: float = 1.0  # 0.0 to 1.0
    errors: List[str] = None
    
    def __post_init__(self):
        """Initialize default values for mutable fields."""
        if self.functions_found is None:
            self.functions_found = []
        if self.classes_found is None:
            self.classes_found = []
        if self.imports is None:
            self.imports = []
        if self.dependencies is None:
            self.dependencies = []
        if self.errors is None:
            self.errors = []
        if self.type_aliases is None:
            self.type_aliases = []
        if self.python313_features is None:
            self.python313_features = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage in vector database."""
        return {
            'function_name': self.function_name,
            'class_name': self.class_name,
            'functions_found': self.functions_found,
            'classes_found': self.classes_found,
            'chunk_type': self.chunk_type,
            'language': self.language,
            'has_code': self.has_code,
            'line_count': self.line_count,
            'complexity_score': self.complexity_score,
            'imports': self.imports,
            'dependencies': self.dependencies,
            'docstring': self.docstring,
            'type_aliases': self.type_aliases,
            'python313_features': self.python313_features,
            'extraction_method': self.extraction_method,
            'confidence': self.confidence
        }


class BaseMetadataExtractor(ABC):
    """Base class for language-specific metadata extractors."""
    
    @property
    @abstractmethod
    def language(self) -> str:
        """Return the language this extractor handles."""
        pass
    
    @property
    @abstractmethod
    def file_extensions(self) -> List[str]:
        """Return file extensions this extractor handles."""
        pass
    
    @abstractmethod
    def extract_metadata(self, content: str, file_path: str = "") -> MetadataExtractionResult:
        """
        Extract metadata from code content.
        
        Args:
            content: Code content to analyze
            file_path: Optional file path for context
            
        Returns:
            MetadataExtractionResult with extracted information
        """
        pass
    
    def can_handle_file(self, file_path: str) -> bool:
        """Check if this extractor can handle the given file."""
        if not file_path:
            return False
        
        file_extension = file_path.split('.')[-1].lower()
        return f".{file_extension}" in self.file_extensions
    
    def _calculate_complexity(self, content: str) -> int:
        """Calculate a basic complexity score for the code."""
        if not content:
            return 0
        
        # Simple complexity heuristics
        complexity = 0
        
        # Count control flow statements
        control_keywords = ['if', 'elif', 'else', 'for', 'while', 'try', 'except', 'finally', 'with']
        for keyword in control_keywords:
            complexity += content.count(f' {keyword} ') + content.count(f'\n{keyword} ')
        
        # Count function definitions
        complexity += content.count('def ')
        
        # Count class definitions  
        complexity += content.count('class ')
        
        return complexity
    
    def _count_lines(self, content: str) -> int:
        """Count non-empty lines in content."""
        if not content:
            return 0
        
        lines = content.split('\n')
        non_empty_lines = [line for line in lines if line.strip()]
        return len(non_empty_lines)

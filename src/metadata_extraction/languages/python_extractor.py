"""
Python-specific metadata extractor with Python 3.13 grammar support.

Enhanced to support Python 3.13 features including:
- Type parameter defaults
- Enhanced type aliases (type statement)
- Improved pattern matching
- Exception groups (except*)
- Better async support
- Enhanced f-string parsing
"""

import ast
import re
import logging
import sys
from typing import List, Dict, Any, Optional, Set, Tuple

from ..base import BaseMetadataExtractor, MetadataExtractionResult

logger = logging.getLogger(__name__)


class PythonMetadataExtractor(BaseMetadataExtractor):
    """Extract metadata from Python code chunks with Python 3.13 support."""

    def __init__(self):
        # Python version detection
        self.python_version = sys.version_info
        self.supports_python_313 = self.python_version >= (3, 13)

        # Enhanced regex patterns for fallback parsing (Python 3.13 compatible)
        self.function_pattern = re.compile(r'^\s*(?:async\s+)?def\s+(\w+)\s*(?:\[.*?\])?\s*\(', re.MULTILINE)
        self.class_pattern = re.compile(r'^\s*class\s+(\w+)(?:\s*\[.*?\])?(?:\s*\([^)]*\))?\s*:', re.MULTILINE)
        self.import_pattern = re.compile(r'^\s*(?:from\s+[\w.]+\s+)?import\s+([\w.,\s*]+)', re.MULTILINE)
        self.docstring_pattern = re.compile(r'^\s*(?:"""(.*?)"""|\'\'\'(.*?)\'\'\')', re.DOTALL | re.MULTILINE)

        # Python 3.13 specific patterns
        self.type_alias_pattern = re.compile(r'^\s*type\s+(\w+)(?:\s*\[.*?\])?\s*=', re.MULTILINE)
        self.match_pattern = re.compile(r'^\s*match\s+', re.MULTILINE)
        self.except_star_pattern = re.compile(r'^\s*except\s*\*', re.MULTILINE)
        self.fstring_pattern = re.compile(r'f["\'].*?["\']', re.DOTALL)

        # Track Python 3.13 features found
        self.python313_features: Set[str] = set()
    
    @property
    def language(self) -> str:
        return "python"
    
    @property
    def file_extensions(self) -> List[str]:
        return [".py", ".pyw"]
    
    def extract_metadata(self, content: str, file_path: str = "") -> MetadataExtractionResult:
        """
        Extract metadata from Python code content.
        
        Args:
            content: Python code content
            file_path: Optional file path for context
            
        Returns:
            MetadataExtractionResult with extracted Python metadata
        """
        result = MetadataExtractionResult(
            language="python",
            has_code=bool(content.strip()),
            line_count=self._count_lines(content),
            complexity_score=self._calculate_complexity(content)
        )
        
        if not content.strip():
            result.extraction_method = "empty"
            return result
        
        try:
            # Try AST parsing first (most accurate for valid Python)
            return self._extract_with_ast(content, result)
        except SyntaxError as e:
            # Fallback to regex parsing for incomplete/invalid Python code
            logger.debug(f"AST parsing failed for Python chunk: {e}, using regex fallback")
            return self._extract_with_regex(content, result)
        except Exception as e:
            logger.warning(f"Failed to extract Python metadata: {e}")
            result.errors.append(f"Extraction failed: {str(e)}")
            result.confidence = 0.0
            return result
    
    def _extract_with_ast(self, content: str, result: MetadataExtractionResult) -> MetadataExtractionResult:
        """Extract metadata using AST parsing with Python 3.13 support."""
        tree = ast.parse(content)

        functions = []
        classes = []
        imports = []
        type_aliases = []
        docstring = ""

        # Reset Python 3.13 features tracking
        self.python313_features.clear()

        # Walk through all nodes in the AST
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                func_info = self._extract_function_info(node)
                functions.append(func_info['name'])

            elif isinstance(node, ast.ClassDef):
                class_info = self._extract_class_info(node)
                classes.append(class_info['name'])

            elif isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)

            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    for alias in node.names:
                        imports.append(f"{node.module}.{alias.name}")

            # Python 3.12+ type alias support
            elif hasattr(ast, 'TypeAlias') and isinstance(node, ast.TypeAlias):
                if hasattr(node.name, 'id'):
                    type_aliases.append(node.name.id)
                    self.python313_features.add('type_alias')

            # Pattern matching (Python 3.10+)
            elif hasattr(ast, 'Match') and isinstance(node, ast.Match):
                self.python313_features.add('pattern_matching')

            # Exception groups (Python 3.11+)
            elif isinstance(node, ast.ExceptHandler) and getattr(node, 'type', None):
                # Check for except* syntax in source if available
                if hasattr(node, 'lineno') and 'except*' in content:
                    self.python313_features.add('exception_groups')

        # Extract module-level docstring
        docstring = self._extract_docstring(tree)

        # Update result with enhanced metadata
        result.functions_found = functions
        result.classes_found = classes
        result.imports = imports
        result.docstring = docstring
        result.extraction_method = "ast_enhanced"
        result.confidence = 1.0

        # Add Python 3.13 features to metadata
        if self.python313_features:
            if not hasattr(result, 'python313_features'):
                result.python313_features = list(self.python313_features)

        # Add type aliases if found
        if type_aliases:
            if not hasattr(result, 'type_aliases'):
                result.type_aliases = type_aliases

        # Set primary identifiers and chunk type
        self._set_primary_identifiers(result, functions, classes, type_aliases)

        return result

    def _extract_function_info(self, node: ast.FunctionDef) -> Dict[str, Any]:
        """Extract detailed function information from AST node."""
        info = {
            'name': node.name,
            'is_async': isinstance(node, ast.AsyncFunctionDef),
            'has_type_params': False,
            'has_return_annotation': node.returns is not None,
            'decorator_count': len(node.decorator_list) if node.decorator_list else 0
        }

        # Check for type parameters (Python 3.12+)
        if hasattr(node, 'type_params') and node.type_params:
            info['has_type_params'] = True
            self.python313_features.add('type_parameters')

            # Check for type parameter defaults (Python 3.13)
            for param in node.type_params:
                if hasattr(param, 'default') and param.default is not None:
                    self.python313_features.add('type_parameter_defaults')
                    break

        return info

    def _extract_class_info(self, node: ast.ClassDef) -> Dict[str, Any]:
        """Extract detailed class information from AST node."""
        info = {
            'name': node.name,
            'has_bases': len(node.bases) > 0,
            'has_type_params': False,
            'decorator_count': len(node.decorator_list) if node.decorator_list else 0
        }

        # Check for type parameters (Python 3.12+)
        if hasattr(node, 'type_params') and node.type_params:
            info['has_type_params'] = True
            self.python313_features.add('type_parameters')

            # Check for type parameter defaults (Python 3.13)
            for param in node.type_params:
                if hasattr(param, 'default') and param.default is not None:
                    self.python313_features.add('type_parameter_defaults')
                    break

        return info

    def _extract_docstring(self, tree: ast.AST) -> str:
        """Extract module-level docstring with enhanced support."""
        if not tree.body:
            return ""

        first_node = tree.body[0]
        if (isinstance(first_node, ast.Expr) and
            isinstance(first_node.value, ast.Constant) and
            isinstance(first_node.value.value, str)):
            return first_node.value.value.strip()

        # Fallback for older Python versions
        if (isinstance(first_node, ast.Expr) and
            isinstance(first_node.value, ast.Str)):
            return first_node.value.s.strip()

        return ""

    def _set_primary_identifiers(self, result: MetadataExtractionResult,
                                functions: List[str], classes: List[str],
                                type_aliases: List[str]):
        """Set primary identifiers and determine chunk type."""
        # Set primary identifiers
        if functions:
            result.function_name = functions[0]
            result.chunk_type = "function"

        if classes:
            result.class_name = classes[0]
            result.chunk_type = "class"

        # Type aliases take precedence for Python 3.12+
        if type_aliases:
            result.chunk_type = "type_alias"

        # If both class and function, prioritize class
        if classes and functions:
            result.chunk_type = "class"

        # Determine chunk type more precisely
        if not classes and not functions and not type_aliases:
            if result.imports:
                result.chunk_type = "import"
            else:
                result.chunk_type = "module"

    def _extract_with_regex(self, content: str, result: MetadataExtractionResult) -> MetadataExtractionResult:
        """Extract metadata using regex (fallback for incomplete code)."""
        # Find function definitions
        function_matches = self.function_pattern.findall(content)
        if function_matches:
            result.functions_found = function_matches
            result.function_name = function_matches[0]
            result.chunk_type = "function"
        
        # Find class definitions
        class_matches = self.class_pattern.findall(content)
        if class_matches:
            result.classes_found = class_matches
            result.class_name = class_matches[0]
            result.chunk_type = "class"
        
        # Find imports
        import_matches = self.import_pattern.findall(content)
        if import_matches:
            # Clean up import matches
            imports = []
            for match in import_matches:
                # Split by comma and clean
                for imp in match.split(','):
                    clean_imp = imp.strip().replace(' as ', ' ').split()[0]
                    if clean_imp and clean_imp != '*':
                        imports.append(clean_imp)
            result.imports = imports
        
        # Find docstrings
        docstring_matches = self.docstring_pattern.findall(content)
        if docstring_matches:
            result.docstring = docstring_matches[0].strip()
        
        # If both class and function, prioritize class
        if class_matches and function_matches:
            result.chunk_type = "class"
        
        # Determine chunk type
        if not class_matches and not function_matches:
            if import_matches:
                result.chunk_type = "import"
            else:
                result.chunk_type = "module"
        
        result.extraction_method = "regex"
        result.confidence = 0.8  # Lower confidence for regex
        
        return result
    
    def _calculate_complexity(self, content: str) -> int:
        """Calculate Python-specific complexity score."""
        if not content:
            return 0
        
        complexity = 0
        
        # Python-specific control flow
        python_keywords = [
            'if', 'elif', 'else', 'for', 'while', 'try', 'except', 'finally', 
            'with', 'async', 'await', 'yield', 'lambda'
        ]
        
        for keyword in python_keywords:
            # Count keyword followed by space or colon
            complexity += len(re.findall(rf'\b{keyword}\b\s*[:(\s]', content))
        
        # Count function and class definitions
        complexity += len(re.findall(r'\bdef\s+\w+', content))
        complexity += len(re.findall(r'\bclass\s+\w+', content))
        
        # Count decorators
        complexity += len(re.findall(r'@\w+', content))
        
        # Count list/dict comprehensions
        complexity += content.count('[') + content.count('{')
        
        return complexity
    
    def extract_dependencies(self, content: str) -> List[str]:
        """Extract Python dependencies from imports."""
        dependencies = []
        
        try:
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        # Get top-level package name
                        package = alias.name.split('.')[0]
                        if package not in dependencies:
                            dependencies.append(package)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        # Get top-level package name
                        package = node.module.split('.')[0]
                        if package not in dependencies:
                            dependencies.append(package)
        
        except SyntaxError:
            # Fallback to regex
            import_matches = self.import_pattern.findall(content)
            for match in import_matches:
                for imp in match.split(','):
                    package = imp.strip().split('.')[0].split()[0]
                    if package and package not in dependencies and package != '*':
                        dependencies.append(package)
        
        return dependencies

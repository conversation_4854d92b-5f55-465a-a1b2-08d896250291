"""
Hybrid Cache System - Best of Both Worlds!

Redis = Traffic Cop (fast, concurrent, lightweight)
Server = Heavy Lifter (persistent, reliable, big data)

This is the magic integration layer that makes it all work together seamlessly!
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from .smart_redis_manager import SmartRedisManager, get_smart_redis, JobStatus
from .server_cache_manager import ServerCacheManager, get_server_cache

logger = logging.getLogger(__name__)


@dataclass
class CacheResult:
    """Result from cache operations."""
    success: bool
    data: Optional[Any] = None
    source: str = "unknown"  # "redis", "server", "both", "none"
    processing_time_ms: float = 0.0
    from_cache: bool = False


class HybridCacheSystem:
    """
    Hybrid cache system that intelligently uses Redis and Server caching.
    
    Strategy:
    - Redis: Session management, job coordination, rate limiting, deduplication flags
    - Server: Embeddings, chunks, processing results, heavy data
    - Automatic fallback: If Redis fails, server continues working
    - Smart routing: Right data goes to right storage
    """
    
    def __init__(self):
        self.redis_manager: Optional[SmartRedisManager] = None
        self.server_cache: Optional[ServerCacheManager] = None
        self.initialized = False
        
        # Performance tracking
        self.stats = {
            'redis_operations': 0,
            'server_operations': 0,
            'hybrid_operations': 0,
            'fallback_operations': 0,
            'total_time_saved_ms': 0
        }
        
        logger.info("Hybrid cache system created")
    
    async def initialize(self) -> bool:
        """Initialize both Redis and Server cache systems."""
        try:
            # Initialize Redis (coordination)
            self.redis_manager = await get_smart_redis()
            redis_ok = self.redis_manager.connected
            
            # Initialize Server cache (heavy data)
            self.server_cache = await get_server_cache()
            server_ok = True  # Server cache should always work
            
            self.initialized = True
            
            logger.info(f"Hybrid cache initialized - Redis: {redis_ok}, Server: {server_ok}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize hybrid cache: {e}")
            return False
    
    # ==================== USER SESSION MANAGEMENT ====================
    
    async def track_user_session(self, user_id: str, session_data: Dict[str, Any]) -> bool:
        """Track user session (Redis handles this - lightweight!)."""
        if not self.redis_manager:
            return True  # Graceful fallback
        
        start_time = time.time()
        result = await self.redis_manager.track_user_session(user_id, session_data)
        
        self.stats['redis_operations'] += 1
        self.stats['total_time_saved_ms'] += (time.time() - start_time) * 1000
        
        return result
    
    async def get_active_users(self) -> List[str]:
        """Get active users (Redis coordination)."""
        if not self.redis_manager:
            return []
        
        return await self.redis_manager.get_active_users()
    
    # ==================== JOB COORDINATION ====================
    
    async def start_job(self, user_id: str, job_type: str, 
                       job_metadata: Dict[str, Any]) -> Optional[str]:
        """Start job coordination (Redis) + prepare server storage."""
        start_time = time.time()
        
        # Check rate limiting first (Redis)
        if self.redis_manager:
            rate_ok = await self.redis_manager.check_rate_limit(
                user_id, job_type, limit=5, window_seconds=60
            )
            if not rate_ok:
                logger.warning(f"Rate limit exceeded for user {user_id}")
                return None
        
        # Queue job coordination (Redis)
        job_id = None
        if self.redis_manager:
            job_id = await self.redis_manager.queue_job_coordination(
                user_id, job_type, job_metadata
            )
        
        # If Redis failed, generate job ID anyway
        if not job_id:
            job_id = f"{job_type}_{user_id}_{int(time.time() * 1000)}"
            self.stats['fallback_operations'] += 1
        
        self.stats['hybrid_operations'] += 1
        self.stats['total_time_saved_ms'] += (time.time() - start_time) * 1000
        
        logger.info(f"Job started: {job_id}")
        return job_id
    
    async def update_job_status(self, job_id: str, status: JobStatus, 
                               result_summary: Optional[Dict] = None) -> bool:
        """Update job status (Redis coordination)."""
        if not self.redis_manager:
            return True  # Graceful fallback
        
        return await self.redis_manager.update_job_status(job_id, status, result_summary)
    
    async def get_user_jobs(self, user_id: str) -> List[Dict[str, Any]]:
        """Get user jobs (Redis coordination)."""
        if not self.redis_manager:
            return []
        
        return await self.redis_manager.get_user_jobs(user_id)
    
    # ==================== EMBEDDING CACHING ====================
    
    async def cache_embedding(self, content: str, embedding: List[float],
                            user_id: str, metadata: Optional[Dict] = None) -> CacheResult:
        """Cache embedding (Server handles heavy data, Redis handles deduplication)."""
        start_time = time.time()

        # Check for duplicates (Redis - lightweight flag with user isolation)
        content_hash = self._generate_hash(content)
        is_duplicate = False

        if self.redis_manager:
            is_duplicate = await self.redis_manager.check_duplicate(content_hash, user_id)
        
        if is_duplicate:
            # Get from server cache instead
            cached_embedding = await self.get_cached_embedding(content)
            processing_time = (time.time() - start_time) * 1000
            
            return CacheResult(
                success=True,
                data=cached_embedding.data if cached_embedding else None,
                source="duplicate_detected",
                processing_time_ms=processing_time,
                from_cache=True
            )
        
        # Store in server cache (heavy data)
        success = False
        if self.server_cache:
            success = await self.server_cache.cache_embedding(content, embedding, metadata)
        
        processing_time = (time.time() - start_time) * 1000
        self.stats['server_operations'] += 1
        self.stats['total_time_saved_ms'] += processing_time
        
        return CacheResult(
            success=success,
            source="server",
            processing_time_ms=processing_time,
            from_cache=False
        )
    
    async def get_cached_embedding(self, content: str) -> CacheResult:
        """Get cached embedding (Server storage)."""
        start_time = time.time()
        
        embedding = None
        if self.server_cache:
            embedding = await self.server_cache.get_cached_embedding(content)
        
        processing_time = (time.time() - start_time) * 1000
        self.stats['server_operations'] += 1
        
        return CacheResult(
            success=embedding is not None,
            data=embedding,
            source="server",
            processing_time_ms=processing_time,
            from_cache=True
        )
    
    async def batch_cache_embeddings(self, 
                                   content_embedding_pairs: List[Tuple[str, List[float]]],
                                   user_id: str) -> CacheResult:
        """Batch cache embeddings efficiently."""
        start_time = time.time()
        
        # Filter out duplicates using Redis (user-isolated)
        filtered_pairs = []
        if self.redis_manager:
            for content, embedding in content_embedding_pairs:
                content_hash = self._generate_hash(content)
                is_duplicate = await self.redis_manager.check_duplicate(content_hash, user_id)
                if not is_duplicate:
                    filtered_pairs.append((content, embedding))
        else:
            filtered_pairs = content_embedding_pairs
        
        # Batch store in server cache
        cached_count = 0
        if self.server_cache and filtered_pairs:
            cached_count = await self.server_cache.batch_cache_embeddings(filtered_pairs)
        
        processing_time = (time.time() - start_time) * 1000
        self.stats['hybrid_operations'] += 1
        self.stats['total_time_saved_ms'] += processing_time
        
        return CacheResult(
            success=cached_count > 0,
            data={'cached_count': cached_count, 'duplicates_filtered': len(content_embedding_pairs) - len(filtered_pairs)},
            source="hybrid",
            processing_time_ms=processing_time,
            from_cache=False
        )
    
    # ==================== CHUNK CACHING ====================
    
    async def cache_chunk(self, file_path: str, chunk_index: int, 
                         content: str, metadata: Dict[str, Any], 
                         user_id: str) -> CacheResult:
        """Cache chunk (Server storage + Redis deduplication)."""
        start_time = time.time()
        
        # Check for duplicates (Redis with user isolation)
        chunk_key = f"{file_path}:{chunk_index}"
        content_hash = self._generate_hash(content)
        is_duplicate = False

        if self.redis_manager:
            is_duplicate = await self.redis_manager.check_duplicate(
                f"chunk:{content_hash}", user_id
            )
        
        if is_duplicate:
            # Try to get from server cache
            cached_chunk = await self.get_cached_chunk(file_path, chunk_index)
            processing_time = (time.time() - start_time) * 1000
            
            return CacheResult(
                success=cached_chunk.success,
                data=cached_chunk.data,
                source="duplicate_detected",
                processing_time_ms=processing_time,
                from_cache=True
            )
        
        # Store in server cache
        success = False
        if self.server_cache:
            success = await self.server_cache.cache_chunk(
                file_path, chunk_index, content, metadata
            )
        
        processing_time = (time.time() - start_time) * 1000
        self.stats['server_operations'] += 1
        
        return CacheResult(
            success=success,
            source="server",
            processing_time_ms=processing_time,
            from_cache=False
        )
    
    async def get_cached_chunk(self, file_path: str, chunk_index: int) -> CacheResult:
        """Get cached chunk (Server storage)."""
        start_time = time.time()
        
        chunk_data = None
        if self.server_cache:
            chunk_data = await self.server_cache.get_cached_chunk(file_path, chunk_index)
        
        processing_time = (time.time() - start_time) * 1000
        self.stats['server_operations'] += 1
        
        return CacheResult(
            success=chunk_data is not None,
            data=chunk_data,
            source="server",
            processing_time_ms=processing_time,
            from_cache=True
        )
    
    # ==================== PROCESSING LOCKS ====================
    
    async def acquire_processing_lock(self, resource_id: str, 
                                    timeout_seconds: int = 300) -> bool:
        """Acquire processing lock (Redis coordination)."""
        if not self.redis_manager:
            return True  # Allow if Redis unavailable
        
        return await self.redis_manager.acquire_processing_lock(resource_id, timeout_seconds)
    
    async def release_processing_lock(self, resource_id: str) -> bool:
        """Release processing lock (Redis coordination)."""
        if not self.redis_manager:
            return True
        
        return await self.redis_manager.release_processing_lock(resource_id)
    
    # ==================== MONITORING & STATS ====================
    
    async def get_comprehensive_stats(self) -> Dict[str, Any]:
        """Get comprehensive statistics from both systems."""
        stats = dict(self.stats)
        
        # Redis coordination stats
        if self.redis_manager:
            redis_stats = await self.redis_manager.get_coordination_stats()
            stats['redis'] = redis_stats
        else:
            stats['redis'] = {'connected': False}
        
        # Server cache stats
        if self.server_cache:
            server_stats = await self.server_cache.get_cache_stats()
            stats['server'] = server_stats
        else:
            stats['server'] = {'initialized': False}
        
        # Hybrid performance metrics
        total_ops = (self.stats['redis_operations'] + 
                    self.stats['server_operations'] + 
                    self.stats['hybrid_operations'])
        
        stats['hybrid_performance'] = {
            'total_operations': total_ops,
            'redis_percentage': (self.stats['redis_operations'] / max(1, total_ops)) * 100,
            'server_percentage': (self.stats['server_operations'] / max(1, total_ops)) * 100,
            'hybrid_percentage': (self.stats['hybrid_operations'] / max(1, total_ops)) * 100,
            'fallback_percentage': (self.stats['fallback_operations'] / max(1, total_ops)) * 100,
            'total_time_saved_ms': self.stats['total_time_saved_ms'],
            'avg_operation_time_ms': self.stats['total_time_saved_ms'] / max(1, total_ops)
        }
        
        return stats
    
    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive health check."""
        health = {
            'hybrid_system': 'healthy',
            'redis_coordination': 'unknown',
            'server_cache': 'unknown',
            'fallback_ready': True
        }
        
        # Check Redis coordination
        if self.redis_manager:
            redis_health = await self.redis_manager.get_coordination_stats()
            health['redis_coordination'] = 'healthy' if redis_health.get('connected') else 'degraded'
        
        # Check server cache
        if self.server_cache:
            try:
                server_stats = await self.server_cache.get_cache_stats()
                health['server_cache'] = 'healthy'
            except Exception:
                health['server_cache'] = 'degraded'
        
        # Overall system health
        if health['server_cache'] == 'healthy':
            if health['redis_coordination'] == 'healthy':
                health['hybrid_system'] = 'optimal'
            else:
                health['hybrid_system'] = 'functional'  # Server can work without Redis
        else:
            health['hybrid_system'] = 'degraded'
        
        return health
    
    def _generate_hash(self, content: str) -> str:
        """Generate hash for content."""
        import hashlib
        return hashlib.sha256(content.encode('utf-8')).hexdigest()


# Global instance
_hybrid_cache: Optional[HybridCacheSystem] = None


async def get_hybrid_cache() -> HybridCacheSystem:
    """Get the global hybrid cache system."""
    global _hybrid_cache
    if _hybrid_cache is None:
        _hybrid_cache = HybridCacheSystem()
        await _hybrid_cache.initialize()
    return _hybrid_cache

"""
🚀 Clean & Straightforward Cache System

Smart Hybrid Approach:
- Redis = Traffic Cop (sessions, coordination, deduplication)
- Server = Heavy Lifter (embeddings, chunks, persistent data)

Key Benefits:
- ✅ Free tier Redis friendly (lightweight coordination only)
- ✅ Unlimited server storage (SQLite + file system)
- ✅ Automatic fallback (works even if Red<PERSON> fails)
- ✅ Smart deduplication (prevents reprocessing)
- ✅ Excellent performance (right tool for right job)
"""

from .hybrid_cache_system import (
    HybridCacheSystem,
    CacheResult,
    get_hybrid_cache
)

from .smart_redis_manager import (
    SmartRedisManager,
    SmartRedisConfig,
    JobStatus,
    get_smart_redis
)

from .server_cache_manager import (
    ServerCacheManager,
    ServerCacheConfig,
    get_server_cache
)

__all__ = [
    # Main Hybrid System (use this!)
    'HybridCacheSystem',
    'CacheResult',
    'get_hybrid_cache',
    
    # Redis Coordination (lightweight)
    'SmartRedisManager',
    'SmartRedisConfig',
    'JobStatus',
    'get_smart_redis',
    
    # Server Storage (heavy data)
    'ServerCacheManager',
    'ServerCacheConfig',
    'get_server_cache'
]

# Simple API for easy integration
async def initialize_cache_system():
    """Initialize the hybrid cache system."""
    cache = await get_hybrid_cache()
    return cache.initialized

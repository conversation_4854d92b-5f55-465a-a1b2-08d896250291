"""
Qdrant vector database client for code chunk storage and retrieval using OpenAI embeddings.
Provides user-specific collections and rich metadata per code chunk.
"""

import os
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from qdrant_client import QdrantClient as QdrantClientSDK
from qdrant_client.http import models
from qdrant_client.http.models import Distance, VectorParams, PointStruct, PayloadSchemaType

from .openai_embeddings import OpenAIEmbeddingService

# Removed SimpleConnectionPool dependency - using direct client approach

logger = logging.getLogger(__name__)


@dataclass
class OpenAIQdrantConfig:
    """Configuration for Qdrant client with OpenAI embeddings."""
    url: str = "https://localhost:6333"
    api_key: Optional[str] = None
    timeout: int = 30
    openai_api_key: Optional[str] = None
    embedding_model: str = "text-embedding-3-small"
    vector_size: int = 1536
    distance_metric: Distance = Distance.COSINE


class OpenAIQdrantClient:
    """
    Qdrant client for vector storage and retrieval of code chunks using OpenAI.
    Each user gets their own collection with rich metadata per chunk.
    Uses connection pooling for better multi-user performance.
    """

    def __init__(self, config: Optional[OpenAIQdrantConfig] = None):
        """Initialize Qdrant client with OpenAI embedding configuration."""
        self.config = config or OpenAIQdrantConfig()
        
        # Initialize direct Qdrant client
        self.client = self._create_qdrant_connection()
        
        # Initialize OpenAI embedding service
        self.embedding_service = OpenAIEmbeddingService(
            api_key=self.config.openai_api_key,
            model_name=self.config.embedding_model
        )
        
        logger.info(f"Initialized OpenAI Qdrant client with URL: {self.config.url}")
        logger.info(f"Using OpenAI model: {self.config.embedding_model}")
    
    def _create_qdrant_connection(self) -> QdrantClientSDK:
        """Create a new Qdrant client connection."""
        if self.config.api_key:
            return QdrantClientSDK(
                url=self.config.url,
                api_key=self.config.api_key,
                timeout=self.config.timeout
            )
        else:
            return QdrantClientSDK(
                url=self.config.url,
                timeout=self.config.timeout
            )
    

    def _get_collection_name(self, user_id: str) -> str:
        """
        Generate collision-resistant collection name for a user.

        Uses both sanitization and hashing to prevent naming collisions
        while maintaining some readability for debugging.
        """
        import hashlib

        # Generate deterministic hash for collision resistance
        user_hash = hashlib.sha256(user_id.encode()).hexdigest()[:12]

        # Sanitize user_id for readability (alphanumeric only)
        sanitized_id = "".join(c for c in user_id if c.isalnum())[:20]

        # Combine sanitized ID with hash for uniqueness
        if sanitized_id:
            return f"user_{sanitized_id}_{user_hash}_code"
        else:
            # Fallback for non-alphanumeric user IDs
            return f"user_{user_hash}_code"

    def create_user_collection(self, user_id: str) -> bool:
        """
        Create a collection for a specific user.
        
        Args:
            user_id: Unique identifier for the user
            
        Returns:
            True if collection was created or already exists
        """
        import time
        collection_name = self._get_collection_name(user_id)
        
        try:
            # Check if collection already exists
            collections = self.client.get_collections()
            existing_names = [c.name for c in collections.collections]
            
            if collection_name in existing_names:
                logger.info(f"Collection {collection_name} already exists")
                # Add small delay to ensure collection is fully ready
                time.sleep(0.5)
                return True
            
            # Create new collection with OpenAI embedding dimensions
            self.client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(
                    size=self.config.vector_size,
                    distance=self.config.distance_metric
                )
            )

            # Create indexes for commonly filtered fields
            try:
                # Index for chunk_type field (for filtering by function, class, module, etc.)
                self.client.create_payload_index(
                    collection_name=collection_name,
                    field_name="chunk_type",
                    field_schema=PayloadSchemaType.KEYWORD
                )

                # Index for language field (for filtering by programming language)
                self.client.create_payload_index(
                    collection_name=collection_name,
                    field_name="language",
                    field_schema=PayloadSchemaType.KEYWORD
                )

                # Index for file_path field (for filtering by file)
                self.client.create_payload_index(
                    collection_name=collection_name,
                    field_name="file_path",
                    field_schema=PayloadSchemaType.KEYWORD
                )

                logger.info(f"Created indexes for collection {collection_name}")
            except Exception as e:
                logger.warning(f"Failed to create indexes for {collection_name}: {e}")

            # Wait for collection to be fully created
            time.sleep(1.0)
            
            logger.info(f"Created collection: {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create collection {collection_name}: {e}")
            return False

    def store_code_chunks(
        self, 
        user_id: str, 
        chunks: List[Dict[str, Any]]
    ) -> bool:
        """
        Store code chunks with OpenAI embeddings and metadata for a user.
        
        Args:
            user_id: User identifier
            chunks: List of code chunks with metadata
            
        Returns:
            True if chunks were stored successfully
        """
        collection_name = self._get_collection_name(user_id)
        
        try:
            # Ensure collection exists
            if not self.create_user_collection(user_id):
                return False
            
            points = []
            for i, chunk in enumerate(chunks):
                # Generate OpenAI embedding for the code content with metadata
                embedding = self.embedding_service.embed_code_chunk(
                    chunk['content'], 
                    chunk
                )
                
                # Create point with rich metadata
                point = PointStruct(
                    id=chunk.get('id', i),
                    vector=embedding,
                    payload={
                        'content': chunk['content'],
                        'file_path': chunk.get('file_path', ''),
                        'function_name': chunk.get('function_name', ''),
                        'class_name': chunk.get('class_name', ''),
                        'start_line': chunk.get('start_line', 0),
                        'end_line': chunk.get('end_line', 0),
                        'imports': chunk.get('imports', []),
                        'dependencies': chunk.get('dependencies', []),
                        'complexity': chunk.get('complexity', 0),
                        'chunk_type': chunk.get('chunk_type', 'code'),
                        'language': chunk.get('language', 'python'),
                        'timestamp': chunk.get('timestamp', ''),
                        'project_name': chunk.get('project_name', ''),
                        'tags': chunk.get('tags', [])
                    }
                )
                points.append(point)
            
            # Batch upsert points
            self.client.upsert(
                collection_name=collection_name,
                points=points
            )
            
            logger.info(
                f"Stored {len(chunks)} chunks for user {user_id} "
                f"in collection {collection_name} using OpenAI embeddings"
            )
            return True
            
        except Exception as e:
            logger.error(f"Failed to store chunks for user {user_id}: {e}")
            return False

    def search_code(
        self,
        user_id: str,
        query: str,
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for code chunks using OpenAI vector similarity.
        
        Args:
            user_id: User identifier
            query: Search query
            limit: Maximum number of results
            filters: Optional metadata filters
            
        Returns:
            List of matching code chunks with scores
        """
        collection_name = self._get_collection_name(user_id)
        
        try:
            # Generate OpenAI query embedding
            query_embedding = self.embedding_service.embed_text(query)
            
            # Build filter conditions
            filter_conditions = None
            if filters:
                conditions = []
                for key, value in filters.items():
                    if isinstance(value, list):
                        conditions.append(
                            models.FieldCondition(
                                key=key,
                                match=models.MatchAny(any=value)
                            )
                        )
                    else:
                        conditions.append(
                            models.FieldCondition(
                                key=key,
                                match=models.MatchValue(value=value)
                            )
                        )
                
                if conditions:
                    filter_conditions = models.Filter(must=conditions)
            
            # Perform search
            search_results = self.client.search(
                collection_name=collection_name,
                query_vector=query_embedding,
                query_filter=filter_conditions,
                limit=limit,
                with_payload=True
            )
            
            # Format results
            results = []
            for result in search_results:
                results.append({
                    'id': result.id,
                    'score': result.score,
                    'content': result.payload.get('content', ''),
                    'file_path': result.payload.get('file_path', ''),
                    'function_name': result.payload.get('function_name', ''),
                    'class_name': result.payload.get('class_name', ''),
                    'start_line': result.payload.get('start_line', 0),
                    'end_line': result.payload.get('end_line', 0),
                    'chunk_type': result.payload.get('chunk_type', ''),
                    'language': result.payload.get('language', ''),
                    'metadata': result.payload
                })
            
            logger.info(
                f"Found {len(results)} results for query '{query}' "
                f"in collection {collection_name} using OpenAI embeddings"
            )
            return results
            
        except Exception as e:
            logger.error(f"Failed to search for user {user_id}: {e}")
            return []

    def get_user_stats(self, user_id: str) -> Dict[str, Any]:
        """
        Get statistics for a user's collection.
        
        Args:
            user_id: User identifier
            
        Returns:
            Dictionary with collection statistics
        """
        collection_name = self._get_collection_name(user_id)
        
        try:
            collection_info = self.client.get_collection(collection_name)
            
            return {
                'collection_name': collection_name,
                'total_chunks': collection_info.points_count,
                'vector_size': collection_info.config.params.vectors.size,
                'distance_metric': collection_info.config.params.vectors.distance,
                'status': collection_info.status,
                'embedding_model': self.config.embedding_model
            }
            
        except Exception as e:
            logger.error(f"Failed to get stats for user {user_id}: {e}")
            return {}

    def delete_user_collection(self, user_id: str) -> bool:
        """
        Delete a user's collection and all their data.
        
        Args:
            user_id: User identifier
            
        Returns:
            True if collection was deleted successfully
        """
        collection_name = self._get_collection_name(user_id)
        
        try:
            self.client.delete_collection(collection_name)
            logger.info(f"Deleted collection: {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete collection {collection_name}: {e}")
            return False

    def health_check(self) -> bool:
        """
        Check if Qdrant service is healthy.
        
        Returns:
            True if service is healthy
        """
        try:
            collections = self.client.get_collections()
            return True
        except Exception as e:
            logger.error(f"Qdrant health check failed: {e}")
            return False


def create_openai_qdrant_client() -> OpenAIQdrantClient:
    """
    Factory function to create OpenAI Qdrant client from environment variables.

    Returns:
        Configured OpenAIQdrantClient instance
    """
    config = OpenAIQdrantConfig(
        url=os.getenv('QDRANT_URL', 'https://localhost:6333'),
        api_key=os.getenv('QDRANT_API_KEY'),
        timeout=int(os.getenv('QDRANT_TIMEOUT', '30')),
        openai_api_key=os.getenv('OPENAI_API_KEY'),
        embedding_model=os.getenv('OPENAI_EMBEDDING_MODEL', 'text-embedding-3-small'),
        vector_size=int(os.getenv('OPENAI_VECTOR_SIZE', '1536'))
    )

    return OpenAIQdrantClient(config)
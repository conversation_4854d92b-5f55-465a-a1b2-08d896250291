#!/usr/bin/env python3
"""
Railway-compatible startup script for MCP Server.

This script provides a robust entry point for Railway deployment,
handling various execution contexts and import scenarios.
"""

import sys
import os
import logging

def setup_environment():
    """Setup environment for Railway deployment."""
    # Get the directory containing this script (project root)
    project_root = os.path.dirname(os.path.abspath(__file__))
    src_dir = os.path.join(project_root, 'src')
    
    # Add directories to Python path
    for path in [project_root, src_dir]:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    # Set working directory to project root
    os.chdir(project_root)
    
    # Railway-specific environment setup
    if os.getenv('RAILWAY_ENVIRONMENT'):
        logging.info("🚂 Railway deployment detected")
        
        # Ensure Railway-specific paths are available
        railway_paths = [
            '/app',
            '/app/src',
            os.path.join(os.getcwd(), 'src')
        ]
        
        for path in railway_paths:
            if os.path.exists(path) and path not in sys.path:
                sys.path.insert(0, path)

def main():
    """Main entry point with comprehensive error handling."""
    # Setup logging early
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 Starting MCP Server...")
    
    # Setup environment
    setup_environment()
    
    # Log environment information
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Working directory: {os.getcwd()}")
    logger.info(f"Project root: {os.path.dirname(os.path.abspath(__file__))}")
    
    if os.getenv('RAILWAY_ENVIRONMENT'):
        logger.info(f"Railway environment: {os.getenv('RAILWAY_ENVIRONMENT')}")
        logger.info(f"Railway service: {os.getenv('RAILWAY_SERVICE_NAME', 'unknown')}")
    
    # Try multiple import strategies
    import_strategies = [
        ("Package-style import", lambda: __import_package_style()),
        ("Module import", lambda: __import_module_style()),
        ("Direct import", lambda: __import_direct_style()),
    ]
    
    for strategy_name, import_func in import_strategies:
        try:
            logger.info(f"Attempting {strategy_name}...")
            server_main = import_func()
            logger.info(f"✅ {strategy_name} successful")
            
            # Run the server
            server_main()
            return  # Success!
            
        except ImportError as e:
            logger.warning(f"❌ {strategy_name} failed: {e}")
            continue
        except Exception as e:
            logger.error(f"❌ {strategy_name} failed with unexpected error: {e}")
            continue
    
    # If all strategies failed
    logger.error("❌ All import strategies failed")
    logger.error("Available files in current directory:")
    for item in os.listdir('.'):
        logger.error(f"  - {item}")
    
    if os.path.exists('src'):
        logger.error("Available files in src directory:")
        for item in os.listdir('src'):
            logger.error(f"  - src/{item}")
    
    logger.error("Python path:")
    for i, path in enumerate(sys.path[:5]):  # Show first 5 entries
        logger.error(f"  {i}: {path}")
    
    sys.exit(1)

def __import_package_style():
    """Try package-style import."""
    from src.server import main
    return main

def __import_module_style():
    """Try module-style import."""
    import src.server
    return src.server.main

def __import_direct_style():
    """Try direct import."""
    from server import main
    return main

if __name__ == "__main__":
    main()

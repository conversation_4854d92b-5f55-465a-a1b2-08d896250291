#!/usr/bin/env python3
"""
Test Python Metadata Extraction System

This script tests the new pluggable metadata extraction system
specifically for Python code chunks.
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from metadata_extraction import extract_chunk_metadata, get_metadata_extractor_factory


def test_python_function_extraction():
    """Test extraction of Python function metadata."""
    print("🔧 Testing Python Function Extraction")
    
    test_cases = [
        {
            'name': 'Simple function',
            'content': '''def hello_world():
    """Say hello to the world."""
    print("Hello from extension test!")
    return True''',
            'expected_function': 'hello_world',
            'expected_type': 'function'
        },
        {
            'name': 'Async function',
            'content': '''async def fetch_data(url: str) -> dict:
    """Fetch data from URL."""
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            return await response.json()''',
            'expected_function': 'fetch_data',
            'expected_type': 'function'
        },
        {
            'name': 'Multiple functions',
            'content': '''def process_data(data):
    return data.strip()

def validate_data(data):
    return len(data) > 0

def save_data(data, filename):
    with open(filename, 'w') as f:
        f.write(data)''',
            'expected_function': 'process_data',  # First function
            'expected_type': 'function',
            'expected_count': 3
        }
    ]
    
    for test_case in test_cases:
        print(f"\n  Testing: {test_case['name']}")
        
        result = extract_chunk_metadata(test_case['content'], 'test.py')
        
        print(f"    Function name: {result.function_name}")
        print(f"    Functions found: {result.functions_found}")
        print(f"    Chunk type: {result.chunk_type}")
        print(f"    Extraction method: {result.extraction_method}")
        print(f"    Confidence: {result.confidence}")
        print(f"    Complexity: {result.complexity_score}")
        
        # Verify expectations
        if result.function_name == test_case['expected_function']:
            print(f"    ✅ Function name correct")
        else:
            print(f"    ❌ Expected '{test_case['expected_function']}', got '{result.function_name}'")
        
        if result.chunk_type == test_case['expected_type']:
            print(f"    ✅ Chunk type correct")
        else:
            print(f"    ❌ Expected '{test_case['expected_type']}', got '{result.chunk_type}'")
        
        if 'expected_count' in test_case:
            if len(result.functions_found) == test_case['expected_count']:
                print(f"    ✅ Function count correct")
            else:
                print(f"    ❌ Expected {test_case['expected_count']} functions, got {len(result.functions_found)}")


def test_python_class_extraction():
    """Test extraction of Python class metadata."""
    print("\n🏗️ Testing Python Class Extraction")
    
    test_cases = [
        {
            'name': 'Simple class',
            'content': '''class DataProcessor:
    """Process and analyze data files."""
    
    def __init__(self, config):
        self.config = config
    
    def process(self):
        return "processed"''',
            'expected_class': 'DataProcessor',
            'expected_function': '__init__',  # First function found
            'expected_type': 'class'
        },
        {
            'name': 'Class with inheritance',
            'content': '''class UserModel(BaseModel):
    """User model with validation."""
    
    def __init__(self, user_id: str, email: str):
        super().__init__()
        self.user_id = user_id
        self.email = email
    
    def to_dict(self) -> dict:
        return {"user_id": self.user_id, "email": self.email}''',
            'expected_class': 'UserModel',
            'expected_type': 'class'
        },
        {
            'name': 'Multiple classes',
            'content': '''class User:
    def __init__(self, name):
        self.name = name

class Admin(User):
    def __init__(self, name, permissions):
        super().__init__(name)
        self.permissions = permissions''',
            'expected_class': 'User',  # First class
            'expected_type': 'class',
            'expected_count': 2
        }
    ]
    
    for test_case in test_cases:
        print(f"\n  Testing: {test_case['name']}")
        
        result = extract_chunk_metadata(test_case['content'], 'test.py')
        
        print(f"    Class name: {result.class_name}")
        print(f"    Classes found: {result.classes_found}")
        print(f"    Function name: {result.function_name}")
        print(f"    Functions found: {result.functions_found}")
        print(f"    Chunk type: {result.chunk_type}")
        print(f"    Docstring: {result.docstring[:50]}..." if result.docstring else "    Docstring: None")
        
        # Verify expectations
        if result.class_name == test_case['expected_class']:
            print(f"    ✅ Class name correct")
        else:
            print(f"    ❌ Expected '{test_case['expected_class']}', got '{result.class_name}'")
        
        if result.chunk_type == test_case['expected_type']:
            print(f"    ✅ Chunk type correct")
        else:
            print(f"    ❌ Expected '{test_case['expected_type']}', got '{result.chunk_type}'")


def test_python_imports_extraction():
    """Test extraction of Python import metadata."""
    print("\n📦 Testing Python Import Extraction")
    
    test_cases = [
        {
            'name': 'Standard imports',
            'content': '''import os
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List''',
            'expected_type': 'import',
            'expected_imports': ['os', 'json', 'datetime.datetime', 'pathlib.Path']
        },
        {
            'name': 'Mixed content with imports',
            'content': '''import logging
from dataclasses import dataclass

@dataclass
class Config:
    debug: bool = False
    
logger = logging.getLogger(__name__)''',
            'expected_type': 'class',  # Class takes priority
            'expected_class': 'Config'
        }
    ]
    
    for test_case in test_cases:
        print(f"\n  Testing: {test_case['name']}")
        
        result = extract_chunk_metadata(test_case['content'], 'test.py')
        
        print(f"    Imports: {result.imports}")
        print(f"    Chunk type: {result.chunk_type}")
        print(f"    Class name: {result.class_name}")
        print(f"    Function name: {result.function_name}")
        
        if result.chunk_type == test_case['expected_type']:
            print(f"    ✅ Chunk type correct")
        else:
            print(f"    ❌ Expected '{test_case['expected_type']}', got '{result.chunk_type}'")


def test_incomplete_python_code():
    """Test extraction from incomplete Python code (regex fallback)."""
    print("\n🔧 Testing Incomplete Python Code (Regex Fallback)")
    
    test_cases = [
        {
            'name': 'Incomplete function',
            'content': '''def process_data(data):
    # This function is incomplete
    result = data.strip(''',  # Missing closing parenthesis
            'expected_function': 'process_data',
            'expected_method': 'regex_enhanced'
        },
        {
            'name': 'Incomplete class',
            'content': '''class DataProcessor:
    def __init__(self, config
        # Missing closing parenthesis and colon''',
            'expected_class': 'DataProcessor',
            'expected_method': 'regex_enhanced'
        }
    ]
    
    for test_case in test_cases:
        print(f"\n  Testing: {test_case['name']}")
        
        result = extract_chunk_metadata(test_case['content'], 'test.py')
        
        print(f"    Function name: {result.function_name}")
        print(f"    Class name: {result.class_name}")
        print(f"    Extraction method: {result.extraction_method}")
        print(f"    Confidence: {result.confidence}")
        print(f"    Errors: {result.errors}")
        
        if result.extraction_method == test_case['expected_method']:
            print(f"    ✅ Extraction method correct")
        else:
            print(f"    ❌ Expected '{test_case['expected_method']}', got '{result.extraction_method}'")


def test_language_detection():
    """Test language detection."""
    print("\n🔍 Testing Language Detection")
    
    factory = get_metadata_extractor_factory()
    
    test_cases = [
        ('test.py', 'python'),
        ('script.pyw', 'python'),
        ('app.js', 'javascript'),
        ('component.tsx', 'typescript'),
        ('Main.java', 'java'),
        ('unknown.xyz', 'unknown')
    ]
    
    for file_path, expected_language in test_cases:
        detected = factory.language_detector.detect_language(file_path)
        status = "✅" if detected == expected_language else "❌"
        print(f"  {file_path} → {detected} {status}")


def main():
    """Run all tests."""
    print("🧪 Testing Python Metadata Extraction System")
    print("=" * 50)
    
    try:
        test_language_detection()
        test_python_function_extraction()
        test_python_class_extraction()
        test_python_imports_extraction()
        test_incomplete_python_code()
        
        print("\n" + "=" * 50)
        print("✅ All Python metadata extraction tests completed!")
        print("🚀 The system is ready for integration with VSCode chunks")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)

#!/usr/bin/env python3
"""
Test script for webhook-based job completion notifications.

This script tests the complete workflow:
1. Start a simple webhook server
2. Submit chunks with webhook URL
3. Verify webhook notifications are received

Usage:
    # Option 1: Let script create test API key automatically
    python scripts/test_webhook_integration.py

    # Option 2: Provide your own API key
    export MCP_TEST_API_KEY="your-api-key-here"
    python scripts/test_webhook_integration.py

The script will automatically create a test user and API key if none is provided.
"""

import asyncio
import aiohttp
import json
import logging
import time
from datetime import datetime
from typing import Dict, Any, List
from aiohttp import web
import threading
import uuid

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class WebhookTestServer:
    """Simple webhook server for testing job completion notifications."""
    
    def __init__(self, port: int = 8080):
        self.port = port
        self.received_notifications: List[Dict[str, Any]] = []
        self.app = web.Application()
        self.runner = None
        self.site = None
        
        # Setup routes
        self.app.router.add_post('/webhook/job-complete', self.handle_webhook)
        self.app.router.add_get('/webhook/status', self.get_status)
    
    async def handle_webhook(self, request: web.Request) -> web.Response:
        """Handle incoming webhook notifications."""
        try:
            notification = await request.json()
            
            logger.info(f"📥 Received webhook notification for job {notification.get('job_id')}")
            logger.info(f"   Status: {notification.get('status')}")
            logger.info(f"   Success: {notification.get('success')}")
            logger.info(f"   Duration: {notification.get('duration_seconds')}s")
            
            # Store notification for verification
            notification['received_at'] = datetime.utcnow().isoformat()
            self.received_notifications.append(notification)
            
            # Log detailed result data
            if notification.get('success'):
                result_data = notification.get('result_data', {})
                logger.info(f"   Chunks processed: {result_data.get('chunks_processed', 'N/A')}")
                logger.info(f"   File path: {result_data.get('file_path', 'N/A')}")
                
                vector_storage = result_data.get('vector_storage', {})
                if vector_storage:
                    logger.info(f"   Vector storage: {vector_storage.get('storage_success', False)}")
                    logger.info(f"   Collection: {vector_storage.get('collection_name', 'N/A')}")
            else:
                logger.error(f"   Error: {notification.get('error_message', 'Unknown error')}")
                warnings = notification.get('warnings', [])
                if warnings:
                    logger.warning(f"   Warnings: {warnings}")
            
            return web.json_response({"received": True, "timestamp": datetime.utcnow().isoformat()})
            
        except Exception as e:
            logger.error(f"Error handling webhook: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def get_status(self, request: web.Request) -> web.Response:
        """Get webhook server status and received notifications."""
        return web.json_response({
            "status": "running",
            "port": self.port,
            "notifications_received": len(self.received_notifications),
            "latest_notifications": self.received_notifications[-5:]  # Last 5
        })
    
    async def start(self):
        """Start the webhook server."""
        self.runner = web.AppRunner(self.app)
        await self.runner.setup()
        self.site = web.TCPSite(self.runner, 'localhost', self.port)
        await self.site.start()
        logger.info(f"🚀 Webhook server started on http://localhost:{self.port}")
    
    async def stop(self):
        """Stop the webhook server."""
        if self.site:
            await self.site.stop()
        if self.runner:
            await self.runner.cleanup()
        logger.info("🛑 Webhook server stopped")


async def _get_or_create_test_api_key() -> str:
        """Get or create a test API key for webhook testing."""
        import os

        # Check if API key is provided via environment variable
        api_key = os.getenv('MCP_TEST_API_KEY')
        if api_key:
            logger.info("✅ Using API key from MCP_TEST_API_KEY environment variable")
            return api_key

        # Try to create a test user and get API key
        try:
            async with aiohttp.ClientSession() as session:
                # Create test user
                user_data = {
                    "user_identifier": f"webhook-test-{int(time.time())}",
                    "user_name": "Webhook Test User"
                }

                # Try different API endpoint paths
                endpoints_to_try = [
                    "https://mcp.rabtune.com/api/v1/connections"
                ]

                user_created = False
                for endpoint in endpoints_to_try:
                    async with session.post(endpoint, json=user_data) as response:
                        if response.status in [200, 201, 409]:
                            if response.status == 409:
                                logger.info("ℹ️ Test user already exists")
                            else:
                                user_result = await response.json()
                                logger.info(f"✅ Created test user via {endpoint}")
                            user_created = True
                            break
                        else:
                            logger.debug(f"Endpoint {endpoint} failed: {response.status}")

                if not user_created:
                    logger.warning("⚠️ Could not create user via any endpoint")

                # Try to generate API key
                key_endpoints_to_try = [
                    "https://mcp.rabtune.com/api/v1/connections"
                ]

                for endpoint in key_endpoints_to_try:
                    async with session.post(endpoint, json=user_data) as response:
                        if response.status == 201:  # New endpoints return 201 for creation
                            key_result = await response.json()
                            if key_result.get("success"):
                                data = key_result.get("data", {})
                                connection = data.get("connection", {})
                                api_key = connection.get('api_key')
                                if api_key:
                                    logger.info(f"✅ Generated test API key via {endpoint}: {api_key[:8]}...")
                                    return api_key
                        else:
                            logger.debug(f"Key endpoint {endpoint} failed: {response.status}")

                logger.error("❌ Could not generate API key via any endpoint")
                return None

        except Exception as e:
            logger.error(f"❌ Error creating test API key: {e}")
            return None


class MCPWebhookTester:
    """Test client for MCP webhook integration."""
    
    def __init__(self, mcp_base_url: str = "https://mcp.rabtune.com", api_key: str = None):
        self.mcp_base_url = mcp_base_url
        self.api_key = api_key
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def submit_chunk_with_webhook(self, file_path: str, chunk_idx: int, content: str, webhook_url: str) -> Dict[str, Any]:
        """Submit a code chunk with webhook URL for completion notification."""
        url = f"{self.mcp_base_url}/api/v1/vscode/chunks"
        
        payload = {
            "metadata": {
                "file_path": file_path,
                "chunk_index": chunk_idx,
                "webhook_url": webhook_url,
                "language": "python",
                "test_run": True
            },
            "content": content
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
        
        try:
            async with self.session.post(url, json=payload, headers=headers) as response:
                if response.status == 201:  # New endpoints return 201 for creation
                    result = await response.json()
                    if result.get("success"):
                        data = result.get("data", {})
                        logger.info(f"✅ Chunk submitted successfully")
                        logger.info(f"   Job ID: {data.get('job_id')}")
                        logger.info(f"   Chunk ID: {data.get('chunk_id')}")
                        logger.info(f"   Full response: {result}")
                        return data
                    else:
                        error = result.get("error", {})
                        logger.error(f"❌ API returned error: {error.get('message')}")
                        return {"error": error.get("message"), "code": error.get("code")}
                else:
                    error_text = await response.text()
                    logger.error(f"❌ Failed to submit chunk: {response.status}")
                    logger.error(f"   Response: {error_text}")
                    return {"error": error_text, "status": response.status}
                    
        except Exception as e:
            logger.error(f"❌ Error submitting chunk: {e}")
            return {"error": str(e)}
    
    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get job status for comparison with webhook notification."""
        url = f"{self.mcp_base_url}/api/v1/jobs/{job_id}"
        
        headers = {}
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
        
        try:
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("success"):
                        return result.get("data", {})
                    else:
                        error = result.get("error", {})
                        return {"error": error.get("message"), "code": error.get("code")}
                else:
                    error_text = await response.text()
                    return {"error": error_text, "status": response.status}
                    
        except Exception as e:
            return {"error": str(e)}


async def run_webhook_test():
    """Run the complete webhook integration test."""
    logger.info("🧪 Starting MCP Webhook Integration Test")
    logger.info("=" * 50)
    
    # Start webhook server
    webhook_server = WebhookTestServer(port=8080)
    await webhook_server.start()
    
    try:
        # Wait a moment for server to be ready
        await asyncio.sleep(1)
        
        webhook_url = f"http://localhost:{webhook_server.port}/webhook/job-complete"
        
        # Test with MCP server - try to generate API key first
        api_key = await _get_or_create_test_api_key()
        if not api_key:
            logger.error("❌ Could not obtain valid API key for testing")
            logger.info("💡 To test with your own API key, run:")
            logger.info("   export MCP_TEST_API_KEY='your-api-key-here'")
            logger.info("   python scripts/test_webhook_integration.py")
            logger.info("")
            logger.info("📋 To generate an API key manually:")
            logger.info("   curl -X POST https://mcp.rabtune.com/api/v1/connections \\")
            logger.info("     -H 'Content-Type: application/json' \\")
            logger.info("     -d '{\"identifier\": \"test-user\", \"name\": \"Test User\"}'")
            return False

        async with MCPWebhookTester(api_key=api_key) as tester:
            # Test case 1: Single chunk
            logger.info("\n📝 Test Case 1: Single chunk processing")
            
            test_content = '''import os
import sys

def hello_world():
    """A simple hello world function."""
    print("Hello, World!")
    return "success"

if __name__ == "__main__":
    hello_world()
'''
            
            result = await tester.submit_chunk_with_webhook(
                file_path="/test/hello_world.py",
                chunk_idx=0,
                content=test_content,
                webhook_url=webhook_url
            )
            
            if "error" in result:
                logger.error(f"❌ Test failed: {result['error']}")
                return False
            
            job_id = result.get("job_id")
            logger.info(f"📋 Job created: {job_id}")
            
            # Wait for webhook notification
            logger.info("⏳ Waiting for webhook notification...")
            
            # Wait up to 30 seconds for notification
            for i in range(30):
                await asyncio.sleep(1)
                if webhook_server.received_notifications:
                    break
                if i % 5 == 0:
                    logger.info(f"   Still waiting... ({i+1}s)")
            
            # Check results
            if not webhook_server.received_notifications:
                logger.error("❌ No webhook notification received within 30 seconds")
                
                # Try polling job status as fallback
                logger.info("🔄 Trying to poll job status...")
                job_status = await tester.get_job_status(job_id)
                logger.info(f"Job status: {job_status}")
                return False
            
            # Verify notification
            notification = webhook_server.received_notifications[-1]
            logger.info("✅ Webhook notification received!")
            
            # Validate notification content
            if notification.get("job_id") != job_id:
                logger.error(f"❌ Job ID mismatch: expected {job_id}, got {notification.get('job_id')}")
                return False
            
            if not notification.get("success"):
                logger.error(f"❌ Job failed: {notification.get('error_message')}")
                return False
            
            logger.info("✅ Webhook integration test passed!")
            
            # Display summary
            logger.info("\n📊 Test Summary:")
            logger.info(f"   Job ID: {notification.get('job_id')}")
            logger.info(f"   Status: {notification.get('status')}")
            logger.info(f"   Duration: {notification.get('duration_seconds')}s")
            logger.info(f"   Chunks processed: {notification.get('result_data', {}).get('chunks_processed', 'N/A')}")
            
            return True
    
    finally:
        await webhook_server.stop()


async def main():
    """Main test runner."""
    try:
        success = await run_webhook_test()
        if success:
            logger.info("\n🎉 All tests passed!")
            return 0
        else:
            logger.error("\n💥 Tests failed!")
            return 1
    except KeyboardInterrupt:
        logger.info("\n🛑 Test interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"\n💥 Test error: {e}")
        return 1


if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

"""
Configuration for API Key Management Server

Centralized configuration management for the API key server with
environment variable support and validation.
"""

import os
from typing import Optional, Dict, Any
from dataclasses import dataclass, field


@dataclass
class DatabaseConfig:
    """Database configuration settings."""
    path: str = "api_keys.db"
    backup_enabled: bool = True
    backup_interval_hours: int = 24
    max_connections: int = 10
    connection_timeout: int = 30
    
    @classmethod
    def from_env(cls) -> 'DatabaseConfig':
        """Create database config from environment variables."""
        return cls(
            path=os.getenv('API_KEY_DB_PATH', 'api_keys.db'),
            backup_enabled=(
                os.getenv('DB_BACKUP_ENABLED', 'true').lower() == 'true'
            ),
            backup_interval_hours=int(
                os.getenv('DB_BACKUP_INTERVAL_HOURS', '24')
            ),
            max_connections=int(os.getenv('DB_MAX_CONNECTIONS', '10')),
            connection_timeout=int(os.getenv('DB_CONNECTION_TIMEOUT', '30'))
        )


@dataclass
class ServerConfig:
    """Server configuration settings."""
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    reload: bool = False
    workers: int = 1
    log_level: str = "info"
    enable_cors: bool = True
    cors_origins: list = field(default_factory=lambda: ["*"])
    
    @classmethod
    def from_env(cls) -> 'ServerConfig':
        """Create server config from environment variables."""
        cors_origins = os.getenv('CORS_ORIGINS', '*').split(',')
        if cors_origins == ['*']:
            cors_origins = ["*"]
        
        return cls(
            host=os.getenv('API_KEY_HOST', '0.0.0.0'),
            port=int(os.getenv('API_KEY_PORT', '8000')),
            debug=os.getenv('DEBUG', 'false').lower() == 'true',
            reload=os.getenv('RELOAD', 'false').lower() == 'true',
            workers=int(os.getenv('WORKERS', '1')),
            log_level=os.getenv('LOG_LEVEL', 'info').lower(),
            enable_cors=os.getenv('ENABLE_CORS', 'true').lower() == 'true',
            cors_origins=cors_origins
        )


@dataclass
class SecurityConfig:
    """Security configuration settings."""
    api_key_length: int = 32
    key_prefix: str = "mcp_"  # Changed to mcp_ as single source of truth
    hash_algorithm: str = "sha256"
    salt_rounds: int = 12
    default_expiry_days: Optional[int] = None
    max_expiry_days: int = 365
    
    @classmethod
    def from_env(cls) -> 'SecurityConfig':
        """Create security config from environment variables."""
        default_expiry = os.getenv('DEFAULT_EXPIRY_DAYS')
        return cls(
            api_key_length=int(os.getenv('API_KEY_LENGTH', '32')),
            key_prefix=os.getenv('API_KEY_PREFIX', 'mcp_'),  # Changed default to mcp_
            hash_algorithm=os.getenv('HASH_ALGORITHM', 'sha256'),
            salt_rounds=int(os.getenv('SALT_ROUNDS', '12')),
            default_expiry_days=(
                int(default_expiry) if default_expiry else None
            ),
            max_expiry_days=int(os.getenv('MAX_EXPIRY_DAYS', '365'))
        )


@dataclass
class RateLimitConfig:
    """Rate limiting configuration settings."""
    enabled: bool = False
    requests_per_window: int = 100
    window_seconds: int = 3600
    burst_limit: int = 10
    burst_window_seconds: int = 60
    
    @classmethod
    def from_env(cls) -> 'RateLimitConfig':
        """Create rate limit config from environment variables."""
        return cls(
            enabled=(
                os.getenv('ENABLE_RATE_LIMITING', 'false').lower() == 'true'
            ),
            requests_per_window=int(os.getenv('RATE_LIMIT_REQUESTS', '100')),
            window_seconds=int(os.getenv('RATE_LIMIT_WINDOW', '3600')),
            burst_limit=int(os.getenv('BURST_LIMIT', '10')),
            burst_window_seconds=int(os.getenv('BURST_WINDOW', '60'))
        )


@dataclass
class LoggingConfig:
    """Logging configuration settings."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = None
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    enable_audit_log: bool = True
    
    @classmethod
    def from_env(cls) -> 'LoggingConfig':
        """Create logging config from environment variables."""
        return cls(
            level=os.getenv('LOG_LEVEL', 'INFO').upper(),
            format=os.getenv(
                'LOG_FORMAT',
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            ),
            file_path=os.getenv('LOG_FILE_PATH'),
            max_file_size=int(
                os.getenv('LOG_MAX_FILE_SIZE', str(10 * 1024 * 1024))
            ),
            backup_count=int(os.getenv('LOG_BACKUP_COUNT', '5')),
            enable_audit_log=(
                os.getenv('ENABLE_AUDIT_LOG', 'true').lower() == 'true'
            )
        )


@dataclass
class ApiKeyServerConfig:
    """Complete API key server configuration."""
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    server: ServerConfig = field(default_factory=ServerConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    rate_limit: RateLimitConfig = field(default_factory=RateLimitConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    
    @classmethod
    def from_env(cls) -> 'ApiKeyServerConfig':
        """Create complete config from environment variables."""
        return cls(
            database=DatabaseConfig.from_env(),
            server=ServerConfig.from_env(),
            security=SecurityConfig.from_env(),
            rate_limit=RateLimitConfig.from_env(),
            logging=LoggingConfig.from_env()
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return {
            'database': {
                'path': self.database.path,
                'backup_enabled': self.database.backup_enabled,
                'backup_interval_hours': self.database.backup_interval_hours,
                'max_connections': self.database.max_connections,
                'connection_timeout': self.database.connection_timeout
            },
            'server': {
                'host': self.server.host,
                'port': self.server.port,
                'debug': self.server.debug,
                'reload': self.server.reload,
                'workers': self.server.workers,
                'log_level': self.server.log_level,
                'enable_cors': self.server.enable_cors,
                'cors_origins': self.server.cors_origins
            },
            'security': {
                'api_key_length': self.security.api_key_length,
                'key_prefix': self.security.key_prefix,
                'hash_algorithm': self.security.hash_algorithm,
                'salt_rounds': self.security.salt_rounds,
                'default_expiry_days': self.security.default_expiry_days,
                'max_expiry_days': self.security.max_expiry_days
            },
            'rate_limit': {
                'enabled': self.rate_limit.enabled,
                'requests_per_window': self.rate_limit.requests_per_window,
                'window_seconds': self.rate_limit.window_seconds,
                'burst_limit': self.rate_limit.burst_limit,
                'burst_window_seconds': self.rate_limit.burst_window_seconds
            },
            'logging': {
                'level': self.logging.level,
                'format': self.logging.format,
                'file_path': self.logging.file_path,
                'max_file_size': self.logging.max_file_size,
                'backup_count': self.logging.backup_count,
                'enable_audit_log': self.logging.enable_audit_log
            }
        }
    
    def validate(self) -> None:
        """Validate configuration settings."""
        # Validate server config
        if not (1 <= self.server.port <= 65535):
            raise ValueError(f"Invalid port: {self.server.port}")
        
        if self.server.workers < 1:
            raise ValueError(f"Workers must be >= 1: {self.server.workers}")
        
        # Validate security config
        if self.security.api_key_length < 16:
            raise ValueError(
                f"API key length too short: {self.security.api_key_length}"
            )
        
        if self.security.salt_rounds < 4:
            raise ValueError(
                f"Salt rounds too low: {self.security.salt_rounds}"
            )
        
        if (self.security.default_expiry_days and
                self.security.default_expiry_days >
                self.security.max_expiry_days):
            raise ValueError("Default expiry exceeds maximum expiry")
        
        # Validate rate limit config
        if self.rate_limit.enabled:
            if self.rate_limit.requests_per_window < 1:
                raise ValueError("Rate limit requests must be >= 1")
            
            if self.rate_limit.window_seconds < 1:
                raise ValueError("Rate limit window must be >= 1 second")
        
        # Validate database config
        if self.database.max_connections < 1:
            raise ValueError("Max connections must be >= 1")
        
        if self.database.connection_timeout < 1:
            raise ValueError("Connection timeout must be >= 1 second")


def load_config() -> ApiKeyServerConfig:
    """
    Load and validate configuration from environment variables.
    
    Returns:
        Validated ApiKeyServerConfig instance
        
    Raises:
        ValueError: If configuration is invalid
    """
    config = ApiKeyServerConfig.from_env()
    config.validate()
    return config


def get_uvicorn_config(config: ApiKeyServerConfig) -> Dict[str, Any]:
    """
    Get uvicorn server configuration from ApiKeyServerConfig.
    
    Args:
        config: API key server configuration
        
    Returns:
        Dictionary of uvicorn configuration options
    """
    return {
        'host': config.server.host,
        'port': config.server.port,
        'log_level': config.server.log_level,
        'reload': config.server.reload,
        'workers': config.server.workers if not config.server.reload else 1,
        'access_log': True,
        'use_colors': True
    }


# Environment variable documentation
ENV_VARS_HELP = """
API Key Server Environment Variables:

Database Configuration:
  API_KEY_DB_PATH          - Database file path (default: api_keys.db)
  DB_BACKUP_ENABLED        - Enable database backups (default: true)
  DB_BACKUP_INTERVAL_HOURS - Backup interval in hours (default: 24)
  DB_MAX_CONNECTIONS       - Maximum database connections (default: 10)
  DB_CONNECTION_TIMEOUT    - Connection timeout in seconds (default: 30)

Server Configuration:
  API_KEY_HOST            - Server host address (default: 0.0.0.0)
  API_KEY_PORT            - Server port (default: 8000)
  DEBUG                   - Enable debug mode (default: false)
  RELOAD                  - Enable auto-reload (default: false)
  WORKERS                 - Number of worker processes (default: 1)
  LOG_LEVEL               - Logging level (default: info)
  ENABLE_CORS             - Enable CORS (default: true)
  CORS_ORIGINS            - Allowed CORS origins (default: *)

Security Configuration:
  API_KEY_LENGTH          - Length of generated API keys (default: 32)
  API_KEY_PREFIX          - Prefix for API keys (default: sk-)
  HASH_ALGORITHM          - Hash algorithm for keys (default: sha256)
  SALT_ROUNDS             - Bcrypt salt rounds (default: 12)
  DEFAULT_EXPIRY_DAYS     - Default key expiry in days (default: none)
  MAX_EXPIRY_DAYS         - Maximum key expiry in days (default: 365)

Rate Limiting Configuration:
  ENABLE_RATE_LIMITING    - Enable rate limiting (default: false)
  RATE_LIMIT_REQUESTS     - Requests per window (default: 100)
  RATE_LIMIT_WINDOW       - Window size in seconds (default: 3600)
  BURST_LIMIT             - Burst request limit (default: 10)
  BURST_WINDOW            - Burst window in seconds (default: 60)

Logging Configuration:
  LOG_LEVEL               - Logging level (default: INFO)
  LOG_FORMAT              - Log message format
  LOG_FILE_PATH           - Log file path (default: console only)
  LOG_MAX_FILE_SIZE       - Max log file size in bytes (default: 10MB)
  LOG_BACKUP_COUNT        - Number of log file backups (default: 5)
  ENABLE_AUDIT_LOG        - Enable audit logging (default: true)
"""


if __name__ == '__main__':
    # Print configuration help
    print(ENV_VARS_HELP)
    
    # Load and display current configuration
    try:
        config = load_config()
        print("\nCurrent Configuration:")
        print("=" * 50)
        
        import json
        print(json.dumps(config.to_dict(), indent=2))
        
    except Exception as e:
        print(f"\nConfiguration Error: {e}")
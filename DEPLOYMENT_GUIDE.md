# 🚀 Complete Deployment Guide: Microservices Architecture

This guide provides step-by-step instructions for deploying the new microservices architecture with two independent servers.

## 📋 Architecture Overview

```
MCP Client (<PERSON> Desktop, VSCode, etc.)
    ↓ (stdio/MCP protocol)
MCP Gateway Server (Local)
    ↓ (HTTP API calls)
Main Server (Railway) → [SQLite, Qdrant, Redis, OpenAI]
```

## 🏗️ Pre-Deployment Setup

### 1. Environment Variables

Create environment files for both servers:

**Main Server** (`main-server/.env`):
```bash
# Server Configuration
HOST=0.0.0.0
PORT=8000

# Database
DATABASE_URL=sqlite:///./data/cache.db

# Vector Store (Qdrant)
QDRANT_URL=your_qdrant_url
QDRANT_API_KEY=your_qdrant_api_key

# OpenAI
OPENAI_API_KEY=your_openai_api_key

# Redis Cache
REDIS_URL=redis://default:<EMAIL>:10798
REDIS_SSL=true

# Railway specific
RAILWAY_VOLUME_MOUNT_PATH=/data

# Logging
LOG_LEVEL=INFO
```

**MCP Gateway** (`mcp-server/.env`):
```bash
# MCP Gateway Configuration
MCP_GATEWAY_MAIN_SERVER_URL=https://mcp.rabtune.com
MCP_GATEWAY_REQUEST_TIMEOUT=30
MCP_GATEWAY_MAX_RETRIES=3
MCP_GATEWAY_LOG_LEVEL=INFO

# Authentication
MCP_API_KEY=mcp_your_api_key_here

# Development settings
MCP_GATEWAY_DEBUG=false
```

## 🚀 Deployment Steps

### Step 1: Deploy Main Server to Railway

```bash
# 1. Navigate to main server directory
cd main-server

# 2. Install dependencies
uv install

# 3. Test locally first
uv run main-server

# 4. Deploy to Railway
railway login
railway link your-project-id
railway up
```

### Step 2: Configure Railway Environment

In Railway dashboard, set these environment variables:
- `HOST=0.0.0.0`
- `PORT=8000`
- `DATABASE_URL=sqlite:///./data/cache.db`
- `QDRANT_URL=your_qdrant_url`
- `QDRANT_API_KEY=your_qdrant_api_key`
- `OPENAI_API_KEY=your_openai_api_key`
- `REDIS_URL=redis://default:<EMAIL>:10798`
- `REDIS_SSL=true`

### Step 3: Setup MCP Gateway Locally

```bash
# 1. Navigate to gateway directory
cd mcp-server

# 2. Install dependencies
uv install

# 3. Configure environment
cp .env.example .env
# Edit .env with your main server URL and API key

# 4. Test connection
uv run mcp-gateway
```

### Step 4: Update MCP Client Configurations

#### Claude Desktop (`~/.claude_desktop_config.json`):
```json
{
  "mcpServers": {
    "string-mcp": {
      "command": "uv",
      "args": ["run", "mcp-gateway"],
      "cwd": "/path/to/mcp-server",
      "env": {
        "MCP_API_KEY": "mcp_your_api_key_here",
        "MCP_GATEWAY_MAIN_SERVER_URL": "https://mcp.rabtune.com"
      }
    }
  }
}
```

#### Cursor IDE (`@mcp-config.json`):
```json
{
  "mcp": {
    "servers": [
      {
        "name": "string-mcp",
        "command": ["uv", "run", "mcp-gateway"],
        "cwd": "/path/to/mcp-server",
        "env": {
          "MCP_API_KEY": "mcp_your_api_key_here",
          "MCP_GATEWAY_MAIN_SERVER_URL": "https://mcp.rabtune.com"
        }
      }
    ]
  }
}
```

## 🧪 Testing Deployment

### 1. Test Main Server

```bash
# Health check
curl https://mcp.rabtune.com/api/v1/health

# Test authentication
curl -X POST https://mcp.rabtune.com/api/v1/connections/validate \
  -H "Content-Type: application/json" \
  -d '{"api_key": "mcp_your_api_key"}'

# Test tool execution
curl -X POST https://mcp.rabtune.com/api/v1/tools/analyze_code_structure/execute \
  -H "Authorization: Bearer mcp_your_api_key" \
  -H "Content-Type: application/json" \
  -d '{"arguments": {"code": "def hello(): return \"world\"", "language": "python"}}'
```

### 2. Test MCP Gateway

```bash
# Start gateway
cd mcp-server
uv run mcp-gateway

# Test with MCP client (Claude Desktop, Cursor, etc.)
# Try these tools:
# - search_code
# - find_function_tool
# - analyze_code_structure
# - process_vscode_chunks
```

## 📊 Monitoring & Maintenance

### Health Checks

**Main Server**:
- Health: `GET https://mcp.rabtune.com/api/v1/health`
- Status: `GET https://mcp.rabtune.com/api/v1/status`
- Connections: `GET https://mcp.rabtune.com/api/v1/connections/stats`

**MCP Gateway**:
- Check logs for connection issues
- Monitor retry attempts and failures
- Track request/response times

### Logs

**Main Server** (Railway):
- View logs in Railway dashboard
- Check for database connection issues
- Monitor vector store operations
- Watch for authentication failures

**MCP Gateway** (Local):
- Check terminal output for errors
- Monitor HTTP request failures
- Watch for timeout issues

## 🔧 Troubleshooting

### Common Issues

1. **Gateway Can't Connect to Main Server**
   ```bash
   # Check URL configuration
   echo $MCP_GATEWAY_MAIN_SERVER_URL
   
   # Test connectivity
   curl https://mcp.rabtune.com/api/v1/health
   ```

2. **Authentication Failures**
   ```bash
   # Verify API key format
   echo $MCP_API_KEY | grep "^mcp_"
   
   # Test key validation
   curl -X POST https://mcp.rabtune.com/api/v1/connections/validate \
     -H "Content-Type: application/json" \
     -d "{\"api_key\": \"$MCP_API_KEY\"}"
   ```

3. **Tool Execution Errors**
   ```bash
   # Check main server logs in Railway
   railway logs
   
   # Test specific tool
   curl -X POST https://mcp.rabtune.com/api/v1/tools/search_code/execute \
     -H "Authorization: Bearer $MCP_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{"arguments": {"query": "test", "limit": 5}}'
   ```

### Performance Optimization

1. **Increase Gateway Timeout**:
   ```bash
   export MCP_GATEWAY_REQUEST_TIMEOUT=60
   ```

2. **Optimize Main Server**:
   - Monitor Railway resource usage
   - Check database performance
   - Optimize vector store queries

3. **Network Optimization**:
   - Use CDN for static assets
   - Enable compression
   - Optimize API response sizes

## 🔄 Rollback Plan

If issues occur:

1. **Keep Current Server Running**: Don't shut down the existing monolithic server
2. **Switch MCP Clients**: Update client configs to point back to original server
3. **Gradual Migration**: Migrate clients one by one to test stability

## 📈 Benefits After Migration

- **Performance**: Faster MCP responses with dedicated stdio transport
- **Scalability**: Independent scaling of components
- **Deployment**: Deploy gateway and main server separately
- **Development**: Work on components in isolation
- **Maintenance**: Cleaner architecture and easier debugging

## 🆘 Support

For deployment issues:
1. Check logs in both gateway and main server
2. Test each component independently
3. Verify all environment variables are set correctly
4. Use health check endpoints to verify service status
5. Contact support with specific error messages and logs

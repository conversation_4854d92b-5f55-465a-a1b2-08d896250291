"""
Server Cache Manager - "Heavy Lifter" Approach

Server handles all the heavy data:
- Embeddings storage (SQLite with WAL mode)
- Chunk content (file system + database)
- Processing results (persistent storage)
- Vector data coordination (with Qdrant)

This is where the real data lives - Redis just coordinates!
"""

import asyncio
import hashlib
import json
import logging
import os
import pickle
import sqlite3
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


@dataclass
class ServerCacheConfig:
    """Configuration for server-side heavy data caching."""
    
    # Database settings
    cache_db_path: str = "data/cache.db"
    enable_wal_mode: bool = True
    
    # File system cache
    cache_dir: str = "data/cache"
    chunk_cache_dir: str = "data/cache/chunks"
    embedding_cache_dir: str = "data/cache/embeddings"
    
    # Performance settings
    max_memory_cache_size: int = 10000  # In-memory cache items
    batch_size: int = 100
    cleanup_interval_hours: int = 24
    
    # TTL settings (longer since we have more space)
    embedding_ttl_hours: int = 168  # 1 week
    chunk_ttl_hours: int = 72       # 3 days
    result_ttl_hours: int = 48      # 2 days


class ServerCacheManager:
    """
    Server-side cache manager for heavy data.
    
    Handles:
    - Embedding storage and retrieval
    - Chunk content caching
    - Processing result storage
    - File-based and database caching
    - In-memory hot cache
    """
    
    def __init__(self, config: Optional[ServerCacheConfig] = None):
        self.config = config or ServerCacheConfig()
        self.db_path = self.config.cache_db_path
        self.cache_dir = Path(self.config.cache_dir)
        
        # In-memory hot cache for frequently accessed items
        self.hot_cache: Dict[str, Any] = {}
        self.hot_cache_access: Dict[str, float] = {}
        
        # Statistics
        self.stats = {
            'embeddings_cached': 0,
            'embeddings_retrieved': 0,
            'chunks_cached': 0,
            'chunks_retrieved': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'hot_cache_hits': 0,
            'db_operations': 0,
            'file_operations': 0
        }
        
        logger.info("Server cache manager initialized")
    
    async def initialize(self) -> bool:
        """Initialize server cache system."""
        try:
            # Create cache directories
            self.cache_dir.mkdir(parents=True, exist_ok=True)
            Path(self.config.chunk_cache_dir).mkdir(parents=True, exist_ok=True)
            Path(self.config.embedding_cache_dir).mkdir(parents=True, exist_ok=True)
            
            # Initialize database
            await self._init_database()
            
            logger.info("Server cache system initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize server cache: {e}")
            return False
    
    async def _init_database(self):
        """Initialize SQLite database for caching."""
        # Ensure directory exists
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        # Create database with WAL mode for better concurrency
        conn = sqlite3.connect(self.db_path)
        
        if self.config.enable_wal_mode:
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=10000")
            conn.execute("PRAGMA temp_store=memory")
        
        # Create tables
        conn.executescript("""
            CREATE TABLE IF NOT EXISTS embeddings_cache (
                content_hash TEXT PRIMARY KEY,
                embedding_data BLOB NOT NULL,
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                access_count INTEGER DEFAULT 1
            );
            
            CREATE TABLE IF NOT EXISTS chunks_cache (
                chunk_hash TEXT PRIMARY KEY,
                file_path TEXT NOT NULL,
                chunk_index INTEGER NOT NULL,
                content_data BLOB NOT NULL,
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            CREATE TABLE IF NOT EXISTS processing_results (
                result_hash TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                job_type TEXT NOT NULL,
                result_data BLOB NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NOT NULL
            );
            
            CREATE INDEX IF NOT EXISTS idx_embeddings_accessed ON embeddings_cache(last_accessed);
            CREATE INDEX IF NOT EXISTS idx_chunks_file ON chunks_cache(file_path, chunk_index);
            CREATE INDEX IF NOT EXISTS idx_results_user ON processing_results(user_id, job_type);
            CREATE INDEX IF NOT EXISTS idx_results_expires ON processing_results(expires_at);
        """)
        
        conn.commit()
        conn.close()
        
        logger.info("Server cache database initialized")
    
    # ==================== EMBEDDING CACHING ====================
    
    async def cache_embedding(self, content: str, embedding: List[float], 
                            metadata: Optional[Dict] = None) -> bool:
        """Cache embedding in server storage."""
        try:
            content_hash = self._generate_hash(content)
            
            # Check hot cache first
            hot_key = f"emb:{content_hash}"
            self.hot_cache[hot_key] = {
                'embedding': embedding,
                'metadata': metadata,
                'cached_at': time.time()
            }
            self.hot_cache_access[hot_key] = time.time()
            
            # Store in database for persistence
            conn = sqlite3.connect(self.db_path)
            embedding_blob = pickle.dumps(embedding)
            metadata_json = json.dumps(metadata or {})
            
            conn.execute("""
                INSERT OR REPLACE INTO embeddings_cache 
                (content_hash, embedding_data, metadata, last_accessed)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            """, (content_hash, embedding_blob, metadata_json))
            
            conn.commit()
            conn.close()
            
            self.stats['embeddings_cached'] += 1
            self.stats['db_operations'] += 1
            
            # Manage hot cache size
            await self._manage_hot_cache()
            
            logger.debug(f"Cached embedding: {content_hash}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cache embedding: {e}")
            return False
    
    async def get_cached_embedding(self, content: str) -> Optional[List[float]]:
        """Retrieve cached embedding."""
        try:
            content_hash = self._generate_hash(content)
            hot_key = f"emb:{content_hash}"
            
            # Check hot cache first
            if hot_key in self.hot_cache:
                self.hot_cache_access[hot_key] = time.time()
                self.stats['hot_cache_hits'] += 1
                self.stats['cache_hits'] += 1
                return self.hot_cache[hot_key]['embedding']
            
            # Check database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.execute("""
                SELECT embedding_data, metadata FROM embeddings_cache 
                WHERE content_hash = ?
            """, (content_hash,))
            
            row = cursor.fetchone()
            if row:
                # Update access time
                conn.execute("""
                    UPDATE embeddings_cache 
                    SET last_accessed = CURRENT_TIMESTAMP, access_count = access_count + 1
                    WHERE content_hash = ?
                """, (content_hash,))
                conn.commit()
                
                embedding = pickle.loads(row[0])
                metadata = json.loads(row[1])
                
                # Add to hot cache
                self.hot_cache[hot_key] = {
                    'embedding': embedding,
                    'metadata': metadata,
                    'cached_at': time.time()
                }
                self.hot_cache_access[hot_key] = time.time()
                
                conn.close()
                
                self.stats['embeddings_retrieved'] += 1
                self.stats['cache_hits'] += 1
                self.stats['db_operations'] += 1
                
                return embedding
            
            conn.close()
            self.stats['cache_misses'] += 1
            return None
            
        except Exception as e:
            logger.error(f"Failed to get cached embedding: {e}")
            self.stats['cache_misses'] += 1
            return None
    
    # ==================== CHUNK CACHING ====================
    
    async def cache_chunk(self, file_path: str, chunk_index: int, 
                         content: str, metadata: Dict[str, Any]) -> bool:
        """Cache chunk content and metadata."""
        try:
            chunk_hash = self._generate_hash(f"{file_path}:{chunk_index}:{content}")
            
            # Store in database
            conn = sqlite3.connect(self.db_path)
            content_blob = content.encode('utf-8')
            metadata_json = json.dumps(metadata)
            
            conn.execute("""
                INSERT OR REPLACE INTO chunks_cache 
                (chunk_hash, file_path, chunk_index, content_data, metadata, last_accessed)
                VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (chunk_hash, file_path, chunk_index, content_blob, metadata_json))
            
            conn.commit()
            conn.close()
            
            self.stats['chunks_cached'] += 1
            self.stats['db_operations'] += 1
            
            logger.debug(f"Cached chunk: {file_path}:{chunk_index}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cache chunk: {e}")
            return False
    
    async def get_cached_chunk(self, file_path: str, 
                              chunk_index: int) -> Optional[Dict[str, Any]]:
        """Retrieve cached chunk."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.execute("""
                SELECT content_data, metadata FROM chunks_cache 
                WHERE file_path = ? AND chunk_index = ?
            """, (file_path, chunk_index))
            
            row = cursor.fetchone()
            if row:
                # Update access time
                conn.execute("""
                    UPDATE chunks_cache 
                    SET last_accessed = CURRENT_TIMESTAMP
                    WHERE file_path = ? AND chunk_index = ?
                """, (file_path, chunk_index))
                conn.commit()
                
                content = row[0].decode('utf-8')
                metadata = json.loads(row[1])
                
                conn.close()
                
                self.stats['chunks_retrieved'] += 1
                self.stats['cache_hits'] += 1
                self.stats['db_operations'] += 1
                
                return {
                    'content': content,
                    'metadata': metadata,
                    'file_path': file_path,
                    'chunk_index': chunk_index
                }
            
            conn.close()
            self.stats['cache_misses'] += 1
            return None
            
        except Exception as e:
            logger.error(f"Failed to get cached chunk: {e}")
            self.stats['cache_misses'] += 1
            return None
    
    # ==================== BATCH OPERATIONS ====================
    
    async def batch_cache_embeddings(self, 
                                   content_embedding_pairs: List[Tuple[str, List[float]]]) -> int:
        """Batch cache multiple embeddings efficiently."""
        if not content_embedding_pairs:
            return 0
        
        try:
            conn = sqlite3.connect(self.db_path)
            cached_count = 0
            
            for content, embedding in content_embedding_pairs:
                content_hash = self._generate_hash(content)
                embedding_blob = pickle.dumps(embedding)
                
                conn.execute("""
                    INSERT OR REPLACE INTO embeddings_cache 
                    (content_hash, embedding_data, last_accessed)
                    VALUES (?, ?, CURRENT_TIMESTAMP)
                """, (content_hash, embedding_blob))
                
                cached_count += 1
            
            conn.commit()
            conn.close()
            
            self.stats['embeddings_cached'] += cached_count
            self.stats['db_operations'] += 1
            
            logger.info(f"Batch cached {cached_count} embeddings")
            return cached_count
            
        except Exception as e:
            logger.error(f"Failed to batch cache embeddings: {e}")
            return 0
    
    # ==================== UTILITIES ====================
    
    def _generate_hash(self, content: str) -> str:
        """Generate hash for content."""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    async def _manage_hot_cache(self):
        """Manage hot cache size using LRU eviction."""
        if len(self.hot_cache) <= self.config.max_memory_cache_size:
            return
        
        # Remove least recently used items
        sorted_items = sorted(
            self.hot_cache_access.items(),
            key=lambda x: x[1]
        )
        
        # Remove oldest 20% of items
        items_to_remove = len(sorted_items) // 5
        for key, _ in sorted_items[:items_to_remove]:
            self.hot_cache.pop(key, None)
            self.hot_cache_access.pop(key, None)
    
    async def cleanup_expired_data(self):
        """Clean up expired data from server cache."""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Clean up old embeddings
            embedding_cutoff = datetime.utcnow() - timedelta(hours=self.config.embedding_ttl_hours)
            conn.execute("""
                DELETE FROM embeddings_cache 
                WHERE last_accessed < ? AND access_count < 5
            """, (embedding_cutoff,))
            
            # Clean up old chunks
            chunk_cutoff = datetime.utcnow() - timedelta(hours=self.config.chunk_ttl_hours)
            conn.execute("""
                DELETE FROM chunks_cache 
                WHERE last_accessed < ?
            """, (chunk_cutoff,))
            
            # Clean up expired results
            conn.execute("""
                DELETE FROM processing_results 
                WHERE expires_at < CURRENT_TIMESTAMP
            """)
            
            conn.commit()
            conn.close()
            
            logger.info("Server cache cleanup completed")
            
        except Exception as e:
            logger.error(f"Failed to cleanup server cache: {e}")
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics."""
        stats = dict(self.stats)
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Count cached items
            cursor = conn.execute("SELECT COUNT(*) FROM embeddings_cache")
            stats['total_embeddings'] = cursor.fetchone()[0]
            
            cursor = conn.execute("SELECT COUNT(*) FROM chunks_cache")
            stats['total_chunks'] = cursor.fetchone()[0]
            
            cursor = conn.execute("SELECT COUNT(*) FROM processing_results")
            stats['total_results'] = cursor.fetchone()[0]
            
            conn.close()
            
            # Hot cache stats
            stats['hot_cache_size'] = len(self.hot_cache)
            stats['hot_cache_hit_rate'] = (
                self.stats['hot_cache_hits'] / max(1, self.stats['cache_hits'])
            )
            
            # Overall hit rate
            total_requests = self.stats['cache_hits'] + self.stats['cache_misses']
            stats['overall_hit_rate'] = (
                self.stats['cache_hits'] / max(1, total_requests)
            )
            
        except Exception as e:
            stats['error'] = str(e)
        
        return stats


# Global instance
_server_cache: Optional[ServerCacheManager] = None


async def get_server_cache() -> ServerCacheManager:
    """Get the global server cache manager."""
    global _server_cache
    if _server_cache is None:
        _server_cache = ServerCacheManager()
        await _server_cache.initialize()
    return _server_cache

"""
Smart Redis Manager - "Light & Fast" Approach

Redis handles:
- Session management (user states, connections)
- Job coordination (lightweight queues)
- Rate limiting (API protection)
- Cache keys/flags (not actual data!)
- Deduplication tracking

Server handles:
- Heavy data (embeddings, chunks, vectors)
- Persistent storage (database, files)
- Complex processing (AI, analysis)
"""

import asyncio
import hashlib
import json
import logging
import os
import time
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum

import redis.asyncio as redis
from redis.asyncio import Redis
from redis.exceptions import RedisError

logger = logging.getLogger(__name__)


class JobStatus(Enum):
    QUEUED = "queued"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class SmartRedisConfig:
    """Lightweight Redis config optimized for coordination, not storage."""

    # Connection (reads from environment variables)
    host: str = os.getenv('REDIS_HOST', 'redis-10798.c292.ap-southeast-1-1.ec2.redns.redis-cloud.com')
    port: int = int(os.getenv('REDIS_PORT', '10798'))
    password: str = os.getenv('REDIS_PASSWORD', 'DOZjOYjJLoBNir3mIrRSE94WzgGyCFQ5')
    username: str = os.getenv('REDIS_USERNAME', 'default')
    ssl: bool = os.getenv('REDIS_SSL', 'false').lower() == 'true'  # This Redis Cloud instance doesn't use SSL
    
    # Optimized for coordination
    max_connections: int = 15  # Reasonable for free tier
    socket_timeout: float = 5.0
    socket_connect_timeout: float = 5.0
    
    # Lightweight TTLs (we're not storing heavy data!)
    session_ttl: int = 3600      # 1 hour user sessions
    job_ttl: int = 1800          # 30 minutes job tracking
    rate_limit_ttl: int = 300    # 5 minutes rate limiting
    dedup_ttl: int = 7200        # 2 hours deduplication flags
    
    # Small memory footprint
    max_memory_usage_mb: int = 20  # Leave 5MB buffer from 25MB limit


class SmartRedisManager:
    """
    Smart Redis Manager - Handles coordination, not heavy lifting!
    
    What Redis DOES handle:
    - User session tracking
    - Job queue coordination  
    - Rate limiting
    - Deduplication flags
    - Processing locks
    
    What Redis DOESN'T handle:
    - Actual embeddings (too big!)
    - Chunk content (server handles this)
    - Vector data (Qdrant handles this)
    - Heavy processing results
    """
    
    def __init__(self, config: Optional[SmartRedisConfig] = None):
        self.config = config or SmartRedisConfig()
        self.redis: Optional[Redis] = None
        self.connected = False

        # Log configuration (without password)
        logger.info(f"Redis config: {self.config.host}:{self.config.port} (user: {self.config.username}, ssl: {self.config.ssl})")
        
        # Lightweight prefixes (user-isolated)
        self.SESSION_PREFIX = "sess:"      # User sessions
        self.JOB_PREFIX = "job:"          # Job coordination
        self.RATE_PREFIX = "rate:"        # Rate limiting
        self.DEDUP_PREFIX = "dedup:"      # Deduplication flags
        self.LOCK_PREFIX = "lock:"        # Processing locks
        
        # Simple stats
        self.stats = {
            'sessions_active': 0,
            'jobs_queued': 0,
            'rate_limits_hit': 0,
            'duplicates_prevented': 0,
            'locks_acquired': 0
        }
        
        logger.info("Smart Redis manager initialized (lightweight mode)")

    def _get_user_key(self, prefix: str, user_id: str, identifier: str) -> str:
        """
        Generate user-isolated cache key.

        Args:
            prefix: Key prefix (e.g., "sess:", "job:")
            user_id: User identifier for isolation
            identifier: Specific identifier for the key

        Returns:
            User-isolated cache key
        """
        return f"{prefix}user:{user_id}:{identifier}"
    
    async def connect(self) -> bool:
        """Connect to Redis for coordination tasks."""
        try:
            # Redis Cloud connection parameters
            connection_params = {
                'host': self.config.host,
                'port': self.config.port,
                'password': self.config.password,
                'username': self.config.username,
                'max_connections': self.config.max_connections,
                'socket_timeout': self.config.socket_timeout,
                'socket_connect_timeout': self.config.socket_connect_timeout,
                'decode_responses': True  # We only store lightweight text data
            }

            # Add SSL configuration for Redis Cloud
            if self.config.ssl:
                import ssl
                connection_params.update({
                    'ssl': True,
                    'ssl_check_hostname': False,  # Redis Cloud uses different hostnames
                    'ssl_cert_reqs': ssl.CERT_NONE,  # Don't require client certificates
                    'ssl_ca_certs': None,         # Don't verify CA certificates
                })

            self.redis = redis.Redis(**connection_params)
            
            await self.redis.ping()
            self.connected = True
            logger.info("Smart Redis connected - ready for coordination!")
            return True
            
        except Exception as e:
            self.connected = False
            logger.warning(f"Redis coordination unavailable: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from Redis."""
        if self.redis:
            await self.redis.close()
            self.connected = False
    
    # ==================== USER SESSION MANAGEMENT ====================
    
    async def track_user_session(self, user_id: str, session_data: Dict[str, Any]) -> bool:
        """Track active user session (lightweight!)."""
        if not self.connected:
            return False
        
        try:
            session_key = self._get_user_key(self.SESSION_PREFIX, user_id, "session")
            session_info = {
                'user_id': user_id,
                'last_active': datetime.utcnow().isoformat(),
                'active_jobs': session_data.get('active_jobs', 0),
                'connection_count': session_data.get('connections', 1)
            }
            
            await self.redis.setex(
                session_key, 
                self.config.session_ttl, 
                json.dumps(session_info)
            )
            
            self.stats['sessions_active'] += 1
            return True
            
        except Exception as e:
            logger.error(f"Failed to track session: {e}")
            return False
    
    async def get_active_users(self) -> List[str]:
        """Get list of currently active users."""
        if not self.connected:
            return []
        
        try:
            session_keys = await self.redis.keys(f"{self.SESSION_PREFIX}*")
            return [key.replace(self.SESSION_PREFIX, '') for key in session_keys]
        except Exception:
            return []
    
    # ==================== JOB COORDINATION ====================
    
    async def queue_job_coordination(self, user_id: str, job_type: str, 
                                   job_metadata: Dict[str, Any]) -> Optional[str]:
        """Queue job coordination info (not the actual job data!)."""
        if not self.connected:
            return None
        
        try:
            job_id = f"{job_type}_{user_id}_{int(time.time() * 1000)}"
            job_key = self._get_user_key(self.JOB_PREFIX, user_id, job_id)
            
            # Only store coordination metadata, not heavy data!
            coordination_data = {
                'job_id': job_id,
                'user_id': user_id,
                'job_type': job_type,
                'status': JobStatus.QUEUED.value,
                'queued_at': datetime.utcnow().isoformat(),
                'chunk_count': job_metadata.get('chunk_count', 0),
                'file_path': job_metadata.get('file_path', ''),
                'priority': job_metadata.get('priority', 0)
            }
            
            await self.redis.setex(
                job_key,
                self.config.job_ttl,
                json.dumps(coordination_data)
            )
            
            # Add to user's job queue
            user_jobs_key = f"{self.JOB_PREFIX}user:{user_id}"
            await self.redis.lpush(user_jobs_key, job_id)
            await self.redis.expire(user_jobs_key, self.config.job_ttl)
            
            self.stats['jobs_queued'] += 1
            logger.debug(f"Job coordination queued: {job_id}")
            return job_id
            
        except Exception as e:
            logger.error(f"Failed to queue job coordination: {e}")
            return None
    
    async def update_job_status(self, job_id: str, status: JobStatus, 
                               result_summary: Optional[Dict] = None) -> bool:
        """Update job status (lightweight summary only!)."""
        if not self.connected:
            return False
        
        try:
            job_key = f"{self.JOB_PREFIX}{job_id}"
            job_data = await self.redis.get(job_key)
            
            if job_data:
                job_info = json.loads(job_data)
                job_info['status'] = status.value
                job_info['updated_at'] = datetime.utcnow().isoformat()
                
                if result_summary:
                    # Only store lightweight summary, not actual results!
                    job_info['result_summary'] = {
                        'chunks_processed': result_summary.get('chunks_processed', 0),
                        'embeddings_generated': result_summary.get('embeddings_generated', 0),
                        'processing_time_ms': result_summary.get('processing_time_ms', 0),
                        'success': result_summary.get('success', False)
                    }
                
                await self.redis.setex(job_key, self.config.job_ttl, json.dumps(job_info))
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to update job status: {e}")
            return False
    
    async def get_user_jobs(self, user_id: str) -> List[Dict[str, Any]]:
        """Get user's job coordination info."""
        if not self.connected:
            return []
        
        try:
            user_jobs_key = f"{self.JOB_PREFIX}user:{user_id}"
            job_ids = await self.redis.lrange(user_jobs_key, 0, -1)
            
            jobs = []
            for job_id in job_ids:
                job_key = f"{self.JOB_PREFIX}{job_id}"
                job_data = await self.redis.get(job_key)
                if job_data:
                    jobs.append(json.loads(job_data))
            
            return jobs
            
        except Exception:
            return []
    
    # ==================== RATE LIMITING ====================
    
    async def check_rate_limit(self, user_id: str, action: str, 
                              limit: int = 10, window_seconds: int = 60) -> bool:
        """Check if user is within rate limits."""
        if not self.connected:
            return True  # Allow if Redis unavailable
        
        try:
            rate_key = self._get_user_key(self.RATE_PREFIX, user_id, action)
            current_count = await self.redis.get(rate_key)
            
            if current_count is None:
                # First request in window
                await self.redis.setex(rate_key, window_seconds, 1)
                return True
            
            if int(current_count) >= limit:
                self.stats['rate_limits_hit'] += 1
                return False
            
            # Increment counter
            await self.redis.incr(rate_key)
            return True
            
        except Exception as e:
            logger.error(f"Rate limit check failed: {e}")
            return True  # Allow on error

    # ==================== DEDUPLICATION ====================

    async def check_duplicate(self, content_hash: str, user_id: str) -> bool:
        """
        Check if content hash is a duplicate for a specific user.

        Args:
            content_hash: Hash of the content to check
            user_id: User identifier for isolation

        Returns:
            True if duplicate, False if new content
        """
        if not self.connected:
            return False  # Assume not duplicate if Redis unavailable

        try:
            dedup_key = self._get_user_key(self.DEDUP_PREFIX, user_id, content_hash)
            exists = await self.redis.exists(dedup_key)

            if exists:
                self.stats['duplicates_prevented'] += 1
                return True

            # Mark as seen for this user
            await self.redis.setex(dedup_key, self.config.dedup_ttl, "1")
            return False

        except Exception as e:
            logger.error(f"Duplicate check failed: {e}")
            return False  # Assume not duplicate on error
    
    # ==================== DEDUPLICATION FLAGS ====================
    # Note: check_duplicate method is already implemented above in DEDUPLICATION section
    
    # ==================== PROCESSING LOCKS ====================
    
    async def acquire_processing_lock(self, resource_id: str, 
                                    timeout_seconds: int = 300) -> bool:
        """Acquire processing lock to prevent concurrent processing."""
        if not self.connected:
            return True  # Allow if Redis unavailable
        
        try:
            lock_key = f"{self.LOCK_PREFIX}{resource_id}"
            acquired = await self.redis.set(
                lock_key, 
                "locked", 
                ex=timeout_seconds, 
                nx=True
            )
            
            if acquired:
                self.stats['locks_acquired'] += 1
                return True
            
            return False
            
        except Exception:
            return True  # Allow on error
    
    async def release_processing_lock(self, resource_id: str) -> bool:
        """Release processing lock."""
        if not self.connected:
            return True
        
        try:
            lock_key = f"{self.LOCK_PREFIX}{resource_id}"
            await self.redis.delete(lock_key)
            return True
        except Exception:
            return False
    
    # ==================== MONITORING ====================
    
    async def get_coordination_stats(self) -> Dict[str, Any]:
        """Get lightweight coordination statistics."""
        stats = dict(self.stats)
        
        if self.connected:
            try:
                # Count active items
                session_count = len(await self.redis.keys(f"{self.SESSION_PREFIX}*"))
                job_count = len(await self.redis.keys(f"{self.JOB_PREFIX}*"))
                lock_count = len(await self.redis.keys(f"{self.LOCK_PREFIX}*"))
                
                stats.update({
                    'connected': True,
                    'active_sessions': session_count,
                    'active_jobs': job_count,
                    'active_locks': lock_count,
                    'memory_efficient': True
                })
                
            except Exception as e:
                stats['error'] = str(e)
        else:
            stats['connected'] = False
        
        return stats


# Global instance
_smart_redis: Optional[SmartRedisManager] = None


async def get_smart_redis() -> SmartRedisManager:
    """Get the global smart Redis coordination manager."""
    global _smart_redis
    if _smart_redis is None:
        _smart_redis = SmartRedisManager()
        await _smart_redis.connect()
    return _smart_redis

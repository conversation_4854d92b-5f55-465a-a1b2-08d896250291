#!/usr/bin/env python3
"""
MCP Client for Remote String MCP Server

This client connects to the remote Railway-deployed MCP server.
For now, it simply runs the server locally since FastMCP handles stdio transport automatically.
"""

import asyncio
import logging
import os
import sys
import subprocess


async def main():
    """Main entry point - just run the remote server locally."""
    try:
        # Get configuration
        api_key = os.getenv('API_KEY')
        server_url = os.getenv('MCP_SERVER_URL', 'https://mcp.rabtune.com')

        if not api_key:
            print("Error: API_KEY environment variable is required", file=sys.stderr)
            sys.exit(1)

        # For now, we'll run the server directly since FastMCP handles stdio
        # In the future, this could be enhanced to proxy to the remote server

        # Set environment variables for the server
        env = os.environ.copy()
        env['API_KEY'] = api_key
        env['MCP_SERVER_URL'] = server_url

        # Run the MCP server directly
        cmd = [sys.executable, '-m', 'src.server']

        # Execute the server
        process = subprocess.Popen(
            cmd,
            env=env,
            stdin=sys.stdin,
            stdout=sys.stdout,
            stderr=sys.stderr
        )

        # Wait for the process to complete
        return_code = process.wait()
        sys.exit(return_code)

    except KeyboardInterrupt:
        pass
    except Exception as e:
        logging.error(f"Failed to start MCP client: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())

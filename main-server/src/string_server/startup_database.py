"""
Database Startup Integration Script

Initializes thread-safe database operations for the MCP server
with Railway container optimization and concurrent access safety.

This script should be called during application startup to ensure
all database operations are properly configured for production deployment.
"""

import asyncio
import logging
import os
import sys
from typing import Dict, Any

# Import database components
from .database import (
    initialize_database_integration,
    patch_existing_components,
    verify_database_safety,
    health_check,
    get_database_integration
)

logger = logging.getLogger(__name__)


async def initialize_database_system() -> Dict[str, Any]:
    """
    Initialize the complete database system for the MCP server.
    
    Returns:
        Dictionary with initialization results and status
    """
    initialization_result = {
        "status": "unknown",
        "environment": "unknown",
        "components_initialized": [],
        "safety_verification": {},
        "health_check": {},
        "errors": [],
        "warnings": []
    }
    
    try:
        logger.info("Starting database system initialization...")
        
        # Step 1: Detect environment
        environment = detect_environment()
        initialization_result["environment"] = environment
        logger.info(f"Detected environment: {environment}")
        
        # Step 2: Initialize database integration
        try:
            integration = initialize_database_integration(environment)
            initialization_result["components_initialized"].append("database_integration")
            logger.info("Database integration initialized successfully")
        except Exception as e:
            error_msg = f"Failed to initialize database integration: {e}"
            initialization_result["errors"].append(error_msg)
            logger.error(error_msg)
            raise
        
        # Step 3: Patch existing components
        try:
            patch_existing_components()
            initialization_result["components_initialized"].append("component_patching")
            logger.info("Existing components patched successfully")
        except Exception as e:
            error_msg = f"Failed to patch existing components: {e}"
            initialization_result["errors"].append(error_msg)
            logger.error(error_msg)
            # Continue - this is not critical
        
        # Step 4: Verify database safety
        try:
            safety_verification = verify_database_safety()
            initialization_result["safety_verification"] = safety_verification
            
            # Check for safety issues
            safety_issues = []
            if not safety_verification.get("thread_safety", False):
                safety_issues.append("Thread safety not verified")
            if not safety_verification.get("wal_mode", False):
                safety_issues.append("WAL mode not enabled")
            if not safety_verification.get("deadlock_prevention", False):
                safety_issues.append("Deadlock prevention not verified")
            
            if safety_issues:
                for issue in safety_issues:
                    initialization_result["warnings"].append(f"Safety concern: {issue}")
                    logger.warning(f"Database safety concern: {issue}")
            else:
                logger.info("Database safety verification passed")
                
        except Exception as e:
            error_msg = f"Database safety verification failed: {e}"
            initialization_result["errors"].append(error_msg)
            logger.error(error_msg)
        
        # Step 5: Perform health check
        try:
            health_result = await health_check()
            initialization_result["health_check"] = health_result
            
            if health_result["status"] == "healthy":
                logger.info("Database health check passed")
            elif health_result["status"] == "degraded":
                logger.warning("Database health check shows degraded performance")
                initialization_result["warnings"].append("Database performance degraded")
            else:
                logger.error("Database health check failed")
                initialization_result["errors"].append("Database health check failed")
                
        except Exception as e:
            error_msg = f"Database health check failed: {e}"
            initialization_result["errors"].append(error_msg)
            logger.error(error_msg)
        
        # Step 6: Log database statistics
        try:
            integration = get_database_integration()
            stats = integration.get_stats()
            
            logger.info("Database system statistics:")
            for component, component_stats in stats.items():
                if isinstance(component_stats, dict) and "pool_stats" in component_stats:
                    pool_stats = component_stats["pool_stats"]
                    logger.info(f"  {component}: {pool_stats.get('total_connections', 0)} connections, "
                              f"{pool_stats.get('pool_hits', 0)} pool hits")
                    
        except Exception as e:
            logger.warning(f"Could not retrieve database statistics: {e}")
        
        # Determine overall status
        if initialization_result["errors"]:
            initialization_result["status"] = "failed"
            logger.error("Database system initialization failed")
        elif initialization_result["warnings"]:
            initialization_result["status"] = "warning"
            logger.warning("Database system initialization completed with warnings")
        else:
            initialization_result["status"] = "success"
            logger.info("Database system initialization completed successfully")
        
        return initialization_result
        
    except Exception as e:
        initialization_result["status"] = "error"
        initialization_result["errors"].append(f"Critical initialization error: {e}")
        logger.error(f"Critical database initialization error: {e}")
        return initialization_result


def detect_environment() -> str:
    """
    Detect the deployment environment.
    
    Returns:
        Environment name (railway, development, testing)
    """
    if os.getenv('RAILWAY_ENVIRONMENT'):
        return "railway"
    elif os.getenv('PYTEST_CURRENT_TEST'):
        return "testing"
    elif os.getenv('ENVIRONMENT') == 'development':
        return "development"
    elif os.getenv('DEBUG') == 'true':
        return "development"
    else:
        # Default to development for safety
        return "development"


def setup_logging_for_database():
    """Setup appropriate logging for database operations."""
    # Get database logger
    db_logger = logging.getLogger('src.database')
    
    # Set appropriate level based on environment
    environment = detect_environment()
    if environment == "railway":
        # Production logging - less verbose
        db_logger.setLevel(logging.INFO)
    elif environment == "development":
        # Development logging - more verbose
        db_logger.setLevel(logging.DEBUG)
    else:
        # Testing - minimal logging
        db_logger.setLevel(logging.WARNING)
    
    # Add handler if none exists
    if not db_logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        db_logger.addHandler(handler)
    
    logger.info(f"Database logging configured for {environment} environment")


async def startup_database_system():
    """
    Main startup function for the database system.
    
    This function should be called during MCP server startup to ensure
    all database operations are properly initialized and configured.
    """
    try:
        # Setup logging
        setup_logging_for_database()
        
        # Initialize database system
        result = await initialize_database_system()
        
        # Handle initialization results
        if result["status"] == "failed":
            logger.error("Database system initialization failed - server cannot start safely")
            logger.error("Errors encountered:")
            for error in result["errors"]:
                logger.error(f"  - {error}")
            
            # In production, we might want to exit
            if result["environment"] == "railway":
                logger.critical("Exiting due to database initialization failure in production")
                sys.exit(1)
            else:
                logger.warning("Continuing despite database errors in non-production environment")
        
        elif result["status"] == "warning":
            logger.warning("Database system initialized with warnings:")
            for warning in result["warnings"]:
                logger.warning(f"  - {warning}")
            logger.info("Server can start but may have reduced functionality")
        
        else:
            logger.info("Database system initialized successfully - server ready to start")
        
        # Log summary
        logger.info(f"Database initialization summary:")
        logger.info(f"  Environment: {result['environment']}")
        logger.info(f"  Components: {', '.join(result['components_initialized'])}")
        logger.info(f"  Status: {result['status']}")
        
        return result
        
    except Exception as e:
        logger.critical(f"Critical error during database startup: {e}")
        if detect_environment() == "railway":
            logger.critical("Exiting due to critical database error in production")
            sys.exit(1)
        raise


def validate_railway_deployment():
    """
    Validate that the Railway deployment is properly configured for database operations.
    
    Returns:
        Dictionary with validation results
    """
    validation = {
        "railway_environment": False,
        "volume_mounted": False,
        "environment_variables": False,
        "disk_space": False,
        "recommendations": []
    }
    
    try:
        # Check Railway environment
        if os.getenv('RAILWAY_ENVIRONMENT'):
            validation["railway_environment"] = True
        else:
            validation["recommendations"].append("Not running in Railway environment")
        
        # Check volume mounting
        if os.path.exists('/data') and os.access('/data', os.W_OK):
            validation["volume_mounted"] = True
        else:
            validation["recommendations"].append("Mount a persistent volume to /data")
        
        # Check required environment variables
        required_vars = ['AUTH_DB_PATH', 'JOBS_DB_PATH', 'DB_MAX_CONNECTIONS']
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if not missing_vars:
            validation["environment_variables"] = True
        else:
            validation["recommendations"].append(f"Missing environment variables: {', '.join(missing_vars)}")
        
        # Check disk space
        try:
            import shutil
            free_space = shutil.disk_usage('/data').free
            if free_space > 500 * 1024 * 1024:  # 500MB
                validation["disk_space"] = True
            else:
                validation["recommendations"].append(f"Low disk space: {free_space / 1024 / 1024:.1f}MB")
        except Exception:
            validation["recommendations"].append("Could not check disk space")
        
    except Exception as e:
        validation["error"] = str(e)
    
    return validation


if __name__ == "__main__":
    # Allow running this script directly for testing
    async def main():
        result = await startup_database_system()
        print(f"Database startup result: {result['status']}")
        
        if result["environment"] == "railway":
            validation = validate_railway_deployment()
            print(f"Railway validation: {validation}")
    
    asyncio.run(main())

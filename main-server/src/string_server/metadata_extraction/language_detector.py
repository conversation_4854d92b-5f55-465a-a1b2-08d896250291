"""
Language detection for code files.
"""

import re
from typing import Optional, Dict, List
from pathlib import Path


class LanguageDetector:
    """Detect programming language from file path and content."""
    
    def __init__(self):
        # File extension to language mapping
        self.extension_map = {
            '.py': 'python',
            '.pyw': 'python',
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.java': 'java',
            '.kt': 'kotlin',
            '.go': 'go',
            '.rs': 'rust',
            '.cpp': 'cpp',
            '.cc': 'cpp',
            '.cxx': 'cpp',
            '.c': 'c',
            '.h': 'c',
            '.cs': 'csharp',
            '.php': 'php',
            '.rb': 'ruby',
            '.swift': 'swift',
            '.scala': 'scala',
            '.r': 'r',
            '.R': 'r',
            '.m': 'matlab',
            '.sh': 'bash',
            '.bash': 'bash',
            '.zsh': 'zsh',
            '.fish': 'fish'
        }
        
        # Content-based detection patterns
        self.content_patterns = {
            'python': [
                re.compile(r'^\s*def\s+\w+\s*\(', re.MULTILINE),
                re.compile(r'^\s*class\s+\w+', re.MULTILINE),
                re.compile(r'^\s*import\s+\w+', re.MULTILINE),
                re.compile(r'^\s*from\s+\w+\s+import', re.MULTILINE),
                re.compile(r'if\s+__name__\s*==\s*["\']__main__["\']', re.MULTILINE)
            ],
            'javascript': [
                re.compile(r'function\s+\w+\s*\(', re.MULTILINE),
                re.compile(r'const\s+\w+\s*=', re.MULTILINE),
                re.compile(r'let\s+\w+\s*=', re.MULTILINE),
                re.compile(r'var\s+\w+\s*=', re.MULTILINE),
                re.compile(r'console\.log\s*\(', re.MULTILINE)
            ],
            'typescript': [
                re.compile(r'interface\s+\w+', re.MULTILINE),
                re.compile(r'type\s+\w+\s*=', re.MULTILINE),
                re.compile(r':\s*\w+\s*=', re.MULTILINE),  # Type annotations
                re.compile(r'export\s+(?:interface|type|class)', re.MULTILINE)
            ],
            'java': [
                re.compile(r'public\s+class\s+\w+', re.MULTILINE),
                re.compile(r'public\s+static\s+void\s+main', re.MULTILINE),
                re.compile(r'import\s+java\.', re.MULTILINE),
                re.compile(r'@\w+', re.MULTILINE)  # Annotations
            ]
        }
    
    def detect_language(self, file_path: str = "", content: str = "") -> str:
        """
        Detect programming language from file path and/or content.
        
        Args:
            file_path: File path to analyze
            content: File content to analyze
            
        Returns:
            Detected language name (lowercase)
        """
        # First try file extension
        if file_path:
            language = self._detect_from_extension(file_path)
            if language:
                return language
        
        # Fallback to content analysis
        if content:
            language = self._detect_from_content(content)
            if language:
                return language
        
        # Default fallback
        return "unknown"
    
    def _detect_from_extension(self, file_path: str) -> Optional[str]:
        """Detect language from file extension."""
        if not file_path:
            return None
        
        # Get file extension
        path = Path(file_path)
        extension = path.suffix.lower()
        
        return self.extension_map.get(extension)
    
    def _detect_from_content(self, content: str) -> Optional[str]:
        """Detect language from content patterns."""
        if not content:
            return None
        
        # Score each language based on pattern matches
        language_scores = {}
        
        for language, patterns in self.content_patterns.items():
            score = 0
            for pattern in patterns:
                matches = pattern.findall(content)
                score += len(matches)
            
            if score > 0:
                language_scores[language] = score
        
        # Return language with highest score
        if language_scores:
            return max(language_scores, key=language_scores.get)
        
        return None
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages."""
        languages = set(self.extension_map.values())
        languages.update(self.content_patterns.keys())
        return sorted(list(languages))
    
    def get_extensions_for_language(self, language: str) -> List[str]:
        """Get file extensions for a specific language."""
        extensions = []
        for ext, lang in self.extension_map.items():
            if lang == language.lower():
                extensions.append(ext)
        return extensions

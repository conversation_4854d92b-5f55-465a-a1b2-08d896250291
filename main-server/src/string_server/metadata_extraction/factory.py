"""
Factory for creating language-specific metadata extractors.
"""

import logging
from typing import Dict, Optional, List

from .base import BaseMetadataExtractor, MetadataExtractionResult
from .language_detector import LanguageDetector
from .languages.python_extractor import PythonMetadataExtractor

logger = logging.getLogger(__name__)


class MetadataExtractorFactory:
    """Factory for creating and managing language-specific metadata extractors."""
    
    def __init__(self):
        self.language_detector = LanguageDetector()
        self._extractors: Dict[str, BaseMetadataExtractor] = {}
        self._register_default_extractors()
    
    def _register_default_extractors(self):
        """Register default language extractors."""
        # Register Python extractor
        python_extractor = PythonMetadataExtractor()
        self._extractors[python_extractor.language] = python_extractor
        
        logger.info(f"Registered metadata extractors for: {list(self._extractors.keys())}")
    
    def register_extractor(self, extractor: BaseMetadataExtractor):
        """
        Register a new language extractor.
        
        Args:
            extractor: Language-specific metadata extractor
        """
        self._extractors[extractor.language] = extractor
        logger.info(f"Registered metadata extractor for {extractor.language}")
    
    def get_extractor(self, language: str) -> Optional[BaseMetadataExtractor]:
        """
        Get extractor for a specific language.
        
        Args:
            language: Programming language name
            
        Returns:
            Language-specific extractor or None if not supported
        """
        return self._extractors.get(language.lower())
    
    def extract_metadata(self, content: str, file_path: str = "") -> MetadataExtractionResult:
        """
        Extract metadata from code content using appropriate language extractor.
        
        Args:
            content: Code content to analyze
            file_path: Optional file path for language detection
            
        Returns:
            MetadataExtractionResult with extracted information
        """
        # Detect language
        language = self.language_detector.detect_language(file_path, content)
        
        # Get appropriate extractor
        extractor = self.get_extractor(language)
        
        if extractor:
            logger.debug(f"Using {language} extractor for {file_path or 'content'}")
            return extractor.extract_metadata(content, file_path)
        else:
            # Fallback to generic extraction
            logger.debug(f"No extractor for {language}, using generic extraction")
            return self._generic_extraction(content, file_path, language)
    
    def _generic_extraction(self, content: str, file_path: str, language: str) -> MetadataExtractionResult:
        """
        Generic metadata extraction for unsupported languages.
        
        Args:
            content: Code content
            file_path: File path
            language: Detected language
            
        Returns:
            Basic MetadataExtractionResult
        """
        result = MetadataExtractionResult(
            language=language,
            has_code=bool(content.strip()),
            line_count=len(content.split('\n')) if content else 0,
            chunk_type="module",
            extraction_method="generic",
            confidence=0.5
        )
        
        if content:
            # Basic complexity calculation
            result.complexity_score = content.count('{') + content.count('(') + content.count('[')
        
        return result
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages."""
        return list(self._extractors.keys())
    
    def can_handle_file(self, file_path: str) -> bool:
        """
        Check if any extractor can handle the given file.
        
        Args:
            file_path: File path to check
            
        Returns:
            True if file can be handled, False otherwise
        """
        language = self.language_detector.detect_language(file_path)
        return language in self._extractors
    
    def get_extractor_for_file(self, file_path: str) -> Optional[BaseMetadataExtractor]:
        """
        Get the appropriate extractor for a file.
        
        Args:
            file_path: File path
            
        Returns:
            Appropriate extractor or None
        """
        language = self.language_detector.detect_language(file_path)
        return self.get_extractor(language)


# Global factory instance
_factory_instance: Optional[MetadataExtractorFactory] = None


def get_metadata_extractor_factory() -> MetadataExtractorFactory:
    """Get the global metadata extractor factory instance."""
    global _factory_instance
    if _factory_instance is None:
        _factory_instance = MetadataExtractorFactory()
    return _factory_instance


def extract_chunk_metadata(content: str, file_path: str = "") -> MetadataExtractionResult:
    """
    Convenience function to extract metadata from a code chunk.
    
    Args:
        content: Code content
        file_path: Optional file path
        
    Returns:
        MetadataExtractionResult
    """
    factory = get_metadata_extractor_factory()
    return factory.extract_metadata(content, file_path)

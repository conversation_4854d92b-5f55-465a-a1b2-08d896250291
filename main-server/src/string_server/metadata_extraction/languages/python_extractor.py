"""
Python-specific metadata extractor with Python 3.13 grammar support.

Enhanced to support Python 3.13 features including:
- Type parameter defaults
- Enhanced type aliases (type statement)
- Improved pattern matching
- Exception groups (except*)
- Better async support
- Enhanced f-string parsing
"""

import ast
import re
import logging
import sys
from typing import List, Dict, Any, Optional, Set, Tuple

from ..base import BaseMetadataExtractor, MetadataExtractionResult

logger = logging.getLogger(__name__)


class PythonMetadataExtractor(BaseMetadataExtractor):
    """Extract metadata from Python code chunks with Python 3.13 support."""

    def __init__(self):
        # Python version detection
        self.python_version = sys.version_info
        self.supports_python_313 = self.python_version >= (3, 13)

        # Enhanced regex patterns for fallback parsing (Python 3.13 compatible)
        self.function_pattern = re.compile(r'^\s*(?:async\s+)?def\s+(\w+)\s*(?:\[.*?\])?\s*\(', re.MULTILINE)
        self.class_pattern = re.compile(r'^\s*class\s+(\w+)(?:\s*\[.*?\])?(?:\s*\([^)]*\))?\s*:', re.MULTILINE)
        self.import_pattern = re.compile(r'^\s*(?:from\s+[\w.]+\s+)?import\s+([\w.,\s*]+)', re.MULTILINE)
        self.docstring_pattern = re.compile(r'^\s*(?:"""(.*?)"""|\'\'\'(.*?)\'\'\')', re.DOTALL | re.MULTILINE)

        # Python 3.13 specific patterns
        self.type_alias_pattern = re.compile(r'^\s*type\s+(\w+)(?:\s*\[.*?\])?\s*=', re.MULTILINE)
        self.match_pattern = re.compile(r'^\s*match\s+', re.MULTILINE)
        self.except_star_pattern = re.compile(r'^\s*except\s*\*', re.MULTILINE)
        self.fstring_pattern = re.compile(r'f["\'].*?["\']', re.DOTALL)

        # Track Python 3.13 features found
        self.python313_features: Set[str] = set()
    
    @property
    def language(self) -> str:
        return "python"
    
    @property
    def file_extensions(self) -> List[str]:
        return [".py", ".pyw"]
    
    def extract_metadata(self, content: str, file_path: str = "") -> MetadataExtractionResult:
        """
        Extract metadata from Python code content.
        
        Args:
            content: Python code content
            file_path: Optional file path for context
            
        Returns:
            MetadataExtractionResult with extracted Python metadata
        """
        result = MetadataExtractionResult(
            language="python",
            has_code=bool(content.strip()),
            line_count=self._count_lines(content),
            complexity_score=self._calculate_complexity(content)
        )
        
        if not content.strip():
            result.extraction_method = "empty"
            return result
        
        try:
            # Try AST parsing first (most accurate for valid Python)
            return self._extract_with_ast(content, result)
        except SyntaxError as e:
            # Fallback to regex parsing for incomplete/invalid Python code
            logger.debug(f"AST parsing failed for Python chunk: {e}, using regex fallback")
            return self._extract_with_regex(content, result)
        except Exception as e:
            logger.warning(f"Failed to extract Python metadata: {e}")
            result.errors.append(f"Extraction failed: {str(e)}")
            result.confidence = 0.0
            return result
    
    def _extract_with_ast(self, content: str, result: MetadataExtractionResult) -> MetadataExtractionResult:
        """Extract metadata using AST parsing with Python 3.13 support."""
        tree = ast.parse(content)

        functions = []
        classes = []
        imports = []
        type_aliases = []
        docstring = ""

        # Reset Python 3.13 features tracking
        self.python313_features.clear()

        # Walk through all nodes in the AST
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                func_info = self._extract_function_info(node)
                functions.append(func_info['name'])

            elif isinstance(node, ast.ClassDef):
                class_info = self._extract_class_info(node)
                classes.append(class_info['name'])

            elif isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)

            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    for alias in node.names:
                        imports.append(f"{node.module}.{alias.name}")

            # Python 3.12+ type alias support
            elif hasattr(ast, 'TypeAlias') and isinstance(node, ast.TypeAlias):
                if hasattr(node.name, 'id'):
                    type_aliases.append(node.name.id)
                    self.python313_features.add('type_alias')

            # Fallback: detect type aliases from assignments (for older Python versions)
            elif isinstance(node, ast.Assign) and len(node.targets) == 1:
                target = node.targets[0]
                if isinstance(target, ast.Name) and target.id[0].isupper():
                    # Simple heuristic: uppercase names that look like type aliases
                    if isinstance(node.value, (ast.Subscript, ast.BinOp, ast.Name)):
                        # Check if this looks like a type alias pattern
                        if self._looks_like_type_alias(node):
                            type_aliases.append(target.id)
                            self.python313_features.add('type_alias')

            # Pattern matching (Python 3.10+)
            elif hasattr(ast, 'Match') and isinstance(node, ast.Match):
                self.python313_features.add('pattern_matching')

            # Exception groups (Python 3.11+)
            elif isinstance(node, ast.ExceptHandler) and getattr(node, 'type', None):
                # Check for except* syntax in source if available
                if hasattr(node, 'lineno') and 'except*' in content:
                    self.python313_features.add('exception_groups')

        # Extract module-level docstring
        docstring = self._extract_docstring(tree)

        # Update result with enhanced metadata
        result.functions_found = functions
        result.classes_found = classes
        result.imports = imports
        result.docstring = docstring
        result.extraction_method = "ast_enhanced"
        result.confidence = 1.0

        # Add Python 3.13 features to metadata
        if self.python313_features:
            if not hasattr(result, 'python313_features'):
                result.python313_features = list(self.python313_features)

        # Add type aliases if found
        if type_aliases:
            if not hasattr(result, 'type_aliases'):
                result.type_aliases = type_aliases

        # Set primary identifiers and chunk type
        self._set_primary_identifiers(result, functions, classes, type_aliases)

        return result

    def _extract_function_info(self, node: ast.FunctionDef) -> Dict[str, Any]:
        """Extract detailed function information from AST node."""
        info = {
            'name': node.name,
            'is_async': isinstance(node, ast.AsyncFunctionDef),
            'has_type_params': False,
            'has_return_annotation': node.returns is not None,
            'decorator_count': len(node.decorator_list) if node.decorator_list else 0
        }

        # Check for type parameters (Python 3.12+)
        if hasattr(node, 'type_params') and node.type_params:
            info['has_type_params'] = True
            self.python313_features.add('type_parameters')

            # Check for type parameter defaults (Python 3.13)
            for param in node.type_params:
                if hasattr(param, 'default') and param.default is not None:
                    self.python313_features.add('type_parameter_defaults')
                    break

        return info

    def _extract_class_info(self, node: ast.ClassDef) -> Dict[str, Any]:
        """Extract detailed class information from AST node."""
        info = {
            'name': node.name,
            'has_bases': len(node.bases) > 0,
            'has_type_params': False,
            'decorator_count': len(node.decorator_list) if node.decorator_list else 0
        }

        # Check for type parameters (Python 3.12+)
        if hasattr(node, 'type_params') and node.type_params:
            info['has_type_params'] = True
            self.python313_features.add('type_parameters')

            # Check for type parameter defaults (Python 3.13)
            for param in node.type_params:
                if hasattr(param, 'default') and param.default is not None:
                    self.python313_features.add('type_parameter_defaults')
                    break

        return info

    def _extract_docstring(self, tree: ast.AST) -> str:
        """Extract module-level docstring with enhanced support."""
        if not tree.body:
            return ""

        first_node = tree.body[0]
        if (isinstance(first_node, ast.Expr) and
            isinstance(first_node.value, ast.Constant) and
            isinstance(first_node.value.value, str)):
            return first_node.value.value.strip()

        # Fallback for older Python versions
        if (isinstance(first_node, ast.Expr) and
            isinstance(first_node.value, ast.Str)):
            return first_node.value.s.strip()

        return ""

    def _looks_like_type_alias(self, node: ast.Assign) -> bool:
        """Check if an assignment looks like a type alias."""
        if not isinstance(node.value, (ast.Subscript, ast.BinOp, ast.Name, ast.Constant)):
            return False

        # Check for common type alias patterns
        value = node.value
        if isinstance(value, ast.Subscript):
            # list[int], dict[str, int], etc.
            return True
        elif isinstance(value, ast.BinOp) and isinstance(value.op, ast.BitOr):
            # Union types: int | str
            return True
        elif isinstance(value, ast.Name):
            # Simple type aliases: MyType = SomeOtherType
            return value.id[0].isupper()  # Likely a type name

        return False

    def _set_primary_identifiers(self, result: MetadataExtractionResult,
                                functions: List[str], classes: List[str],
                                type_aliases: List[str]):
        """Set primary identifiers and determine chunk type."""
        # Set primary identifiers
        if functions:
            result.function_name = functions[0]
            result.chunk_type = "function"

        if classes:
            result.class_name = classes[0]
            result.chunk_type = "class"

        # Type aliases take precedence for Python 3.12+
        if type_aliases:
            result.chunk_type = "type_alias"

        # If both class and function, prioritize class
        if classes and functions:
            result.chunk_type = "class"

        # Determine chunk type more precisely
        if not classes and not functions and not type_aliases:
            if result.imports:
                result.chunk_type = "import"
            else:
                result.chunk_type = "module"

    def _extract_with_regex(self, content: str, result: MetadataExtractionResult) -> MetadataExtractionResult:
        """Extract metadata using enhanced regex with Python 3.13 support."""
        # Reset Python 3.13 features tracking
        self.python313_features.clear()

        # Find function definitions (enhanced for type parameters)
        function_matches = self.function_pattern.findall(content)
        if function_matches:
            result.functions_found = function_matches
            result.function_name = function_matches[0]
            result.chunk_type = "function"

        # Find class definitions (enhanced for type parameters)
        class_matches = self.class_pattern.findall(content)
        if class_matches:
            result.classes_found = class_matches
            result.class_name = class_matches[0]
            result.chunk_type = "class"

        # Find type aliases (Python 3.12+)
        type_alias_matches = self.type_alias_pattern.findall(content)
        if type_alias_matches:
            result.type_aliases = type_alias_matches
            result.chunk_type = "type_alias"
            self.python313_features.add('type_alias')

        # Find imports
        import_matches = self.import_pattern.findall(content)
        if import_matches:
            imports = self._clean_import_matches(import_matches)
            result.imports = imports

        # Find docstrings (enhanced for both """ and ''' styles)
        docstring_matches = self.docstring_pattern.findall(content)
        if docstring_matches:
            # Handle both triple quote styles
            for match in docstring_matches:
                if isinstance(match, tuple):
                    docstring = next((m for m in match if m), "")
                else:
                    docstring = match
                if docstring:
                    result.docstring = docstring.strip()
                    break

        # Detect Python 3.13 features
        self._detect_python313_features_regex(content)

        # Set chunk type priority
        self._set_regex_chunk_type(result, class_matches, function_matches,
                                  type_alias_matches, import_matches)

        # Add Python 3.13 features to result
        if self.python313_features:
            result.python313_features = list(self.python313_features)

        result.extraction_method = "regex_enhanced"
        result.confidence = 0.8  # Lower confidence for regex

        return result

    def _clean_import_matches(self, import_matches: List[str]) -> List[str]:
        """Clean up import matches from regex."""
        imports = []
        for match in import_matches:
            # Split by comma and clean
            for imp in match.split(','):
                clean_imp = imp.strip().replace(' as ', ' ').split()[0]
                if clean_imp and clean_imp != '*':
                    imports.append(clean_imp)
        return imports

    def _detect_python313_features_regex(self, content: str):
        """Detect Python 3.13 features using regex patterns."""
        # Pattern matching
        if self.match_pattern.search(content):
            self.python313_features.add('pattern_matching')

        # Exception groups
        if self.except_star_pattern.search(content):
            self.python313_features.add('exception_groups')

        # F-strings
        if self.fstring_pattern.search(content):
            self.python313_features.add('fstrings')

        # Type parameter defaults (basic detection)
        if re.search(r'\[.*?=.*?\]', content):
            self.python313_features.add('type_parameter_defaults')

    def _set_regex_chunk_type(self, result: MetadataExtractionResult,
                             class_matches: List[str], function_matches: List[str],
                             type_alias_matches: List[str], import_matches: List[str]):
        """Set chunk type based on regex matches with priority."""
        # Type aliases have highest priority for Python 3.12+
        if type_alias_matches:
            result.chunk_type = "type_alias"
        # Classes have higher priority than functions
        elif class_matches and function_matches:
            result.chunk_type = "class"
        # Determine chunk type if no classes/functions/type aliases
        elif not class_matches and not function_matches and not type_alias_matches:
            if import_matches:
                result.chunk_type = "import"
            else:
                result.chunk_type = "module"
    
    def _calculate_complexity(self, content: str) -> int:
        """Calculate Python-specific complexity score with Python 3.13 features."""
        if not content:
            return 0

        complexity = 0

        # Python-specific control flow
        python_keywords = [
            'if', 'elif', 'else', 'for', 'while', 'try', 'except', 'finally',
            'with', 'async', 'await', 'yield', 'lambda', 'match', 'case'
        ]

        for keyword in python_keywords:
            # Count keyword followed by space or colon
            complexity += len(re.findall(rf'\b{keyword}\b\s*[:(\s]', content))

        # Count function and class definitions
        complexity += len(re.findall(r'\bdef\s+\w+', content))
        complexity += len(re.findall(r'\bclass\s+\w+', content))

        # Count type aliases (Python 3.12+)
        complexity += len(re.findall(r'\btype\s+\w+', content))

        # Count decorators
        complexity += len(re.findall(r'@\w+', content))

        # Count list/dict comprehensions
        complexity += content.count('[') + content.count('{')

        # Python 3.13 specific complexity factors
        # Exception groups (except*)
        complexity += len(re.findall(r'\bexcept\s*\*', content))

        # Pattern matching complexity
        match_blocks = len(re.findall(r'\bmatch\s+', content))
        case_blocks = len(re.findall(r'\bcase\s+', content))
        complexity += match_blocks * 2 + case_blocks  # Match blocks are more complex

        # Type parameter complexity
        type_params = len(re.findall(r'\[.*?[A-Z]\w*.*?\]', content))
        complexity += type_params

        # F-string complexity (nested expressions)
        fstring_expressions = len(re.findall(r'f["\'].*?\{.*?\}.*?["\']', content))
        complexity += fstring_expressions

        return complexity
    
    def extract_dependencies(self, content: str) -> List[str]:
        """Extract Python dependencies from imports with enhanced analysis."""
        dependencies = []

        try:
            tree = ast.parse(content)

            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        # Get top-level package name
                        package = alias.name.split('.')[0]
                        if package not in dependencies:
                            dependencies.append(package)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        # Get top-level package name
                        package = node.module.split('.')[0]
                        if package not in dependencies:
                            dependencies.append(package)

        except SyntaxError:
            # Enhanced fallback to regex
            dependencies = self._extract_dependencies_regex(content)

        return dependencies

    def _extract_dependencies_regex(self, content: str) -> List[str]:
        """Extract dependencies using regex fallback."""
        dependencies = []
        import_matches = self.import_pattern.findall(content)

        for match in import_matches:
            for imp in match.split(','):
                package = imp.strip().split('.')[0].split()[0]
                if package and package not in dependencies and package != '*':
                    dependencies.append(package)

        return dependencies

    def get_python313_features(self) -> List[str]:
        """Get list of Python 3.13 features detected in the last extraction."""
        return list(self.python313_features)

    def supports_feature(self, feature: str) -> bool:
        """Check if the current Python version supports a specific feature."""
        feature_requirements = {
            'type_alias': (3, 12),
            'type_parameters': (3, 12),
            'type_parameter_defaults': (3, 13),
            'pattern_matching': (3, 10),
            'exception_groups': (3, 11),
            'fstrings': (3, 6),
            'async_functions': (3, 5),
            'walrus_operator': (3, 8),
        }

        required_version = feature_requirements.get(feature)
        if required_version:
            return self.python_version >= required_version
        return False

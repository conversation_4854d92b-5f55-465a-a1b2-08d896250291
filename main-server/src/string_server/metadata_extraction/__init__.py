"""
Metadata Extraction System

A pluggable system for extracting function and class metadata from code chunks
in different programming languages.
"""

from .base import BaseMetadataExtractor, MetadataExtractionResult
from .factory import MetadataExtractorFactory, get_metadata_extractor_factory, extract_chunk_metadata
from .language_detector import LanguageDetector

__all__ = [
    'BaseMetadataExtractor',
    'MetadataExtractionResult',
    'MetadataExtractorFactory',
    'get_metadata_extractor_factory',
    'extract_chunk_metadata',
    'LanguageDetector'
]

"""
Tools package for the MCP server.
Provides organized tool registration and management.
Optimized for MCP remote operation - no direct filesystem access.
"""

import os
import logging
import functools
from typing import TYPE_CHECKING, Callable, Any, Optional

if TYPE_CHECKING:
    from mcp.server.fastmcp import FastMC<PERSON>

# Removed workspace_tools - violates MCP remote operation principles
from .indexing_tools import register_indexing_tools
from .search_tools import register_search_tools
from .analysis_tools import register_analysis_tools
from .reference_tools import register_reference_tools

# Import sandbox processor for protective execution
try:
    from ..workers.sandbox_processor import SandboxProcessor, ResourceLimits, ProcessingStatus
except ImportError:
    from workers.sandbox_processor import SandboxProcessor, ResourceLimits, ProcessingStatus


# Global sandbox processor instance (initialized once)
_sandbox_processor: Optional[SandboxProcessor] = None
_sandbox_enabled = os.environ.get('ENABLE_SANDBOX', 'true').lower() == 'true'

def get_sandbox_processor() -> Optional[SandboxProcessor]:
    """Get or create the global sandbox processor instance."""
    global _sandbox_processor
    if _sandbox_enabled and _sandbox_processor is None:
        try:
            _sandbox_processor = SandboxProcessor()
            logging.getLogger(__name__).info("Sandbox processor initialized for tool protection")
        except Exception as e:
            logging.getLogger(__name__).warning(f"Failed to initialize sandbox: {e}")
            _sandbox_processor = None
    return _sandbox_processor


def sandbox_protect(func: Callable) -> Callable:
    """
    Decorator to wrap tool functions with sandbox protection.
    Acts like a protective 'glove' - same functionality, just safer.
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        sandbox = get_sandbox_processor()

        if sandbox is None:
            # No sandbox available - execute directly (fallback)
            return func(*args, **kwargs)

        try:
            # Execute in sandbox with protection
            result = sandbox.execute_safely(func, *args, **kwargs)

            if result.status == ProcessingStatus.COMPLETED:
                return result.result
            elif result.status == ProcessingStatus.TIMEOUT:
                logging.getLogger(__name__).warning(f"Tool {func.__name__} timed out")
                return f"Error: Tool execution timed out after {result.resource_usage.execution_time_seconds}s"
            elif result.status == ProcessingStatus.SECURITY_VIOLATION:
                logging.getLogger(__name__).warning(f"Security violation in {func.__name__}: {result.error_message}")
                return f"Error: Security violation detected - {result.error_message}"
            elif result.status == ProcessingStatus.RESOURCE_LIMIT_EXCEEDED:
                logging.getLogger(__name__).warning(f"Resource limit exceeded in {func.__name__}: {result.error_message}")
                return f"Error: Resource limit exceeded - {result.error_message}"
            else:
                logging.getLogger(__name__).error(f"Tool {func.__name__} failed: {result.error_message}")
                return f"Error: {result.error_message}"

        except Exception as e:
            # Sandbox failed - fallback to direct execution
            logging.getLogger(__name__).warning(f"Sandbox execution failed for {func.__name__}, falling back to direct execution: {e}")
            return func(*args, **kwargs)

    return wrapper


def register_all_tools(mcp_server: "FastMCP") -> None:
    """
    Register all tools with the MCP server.

    Optimized for MCP remote operation:
    - No direct filesystem scanning
    - Code streamed from IDE/client
    - Token-efficient processing
    - Sandbox protection for all tools (configurable)

    Args:
        mcp_server: The FastMCP server instance to register tools with
    """
    # Initialize sandbox if enabled
    sandbox = get_sandbox_processor()
    if sandbox:
        logging.getLogger(__name__).info("Tools will be protected with sandbox isolation")
    else:
        logging.getLogger(__name__).info("Tools will run directly (sandbox disabled or unavailable)")

    # Removed workspace tools - code should be streamed from IDE
    register_indexing_tools(mcp_server)
    register_search_tools(mcp_server)
    register_analysis_tools(mcp_server)
    register_reference_tools(mcp_server)
"""
Reference-related tools for the MCP server.
Handles finding references to symbols across the codebase.
"""

import json
import logging
from typing import Optional, TYPE_CHECKING
from pydantic import Field

from .base import get_metadata_extractor, _get_or_auto_index_project

if TYPE_CHECKING:
    from mcp.server.fastmcp import FastMC<PERSON>


def find_all_references(symbol_name: str, 
                       project_path: Optional[str] = None,
                       include_definitions: bool = True) -> str:
    """
    Find all references to a symbol (function, class, variable) in the codebase.
    Will auto-index the project if not already indexed.
    
    Args:
        symbol_name: Name of the symbol to find references for
        project_path: Optional project path to search in (auto-indexes if needed)
        include_definitions: Whether to include the definition location
    
    Returns:
        JSON with all references found, organized by file and type
    """
    try:
        # Get or auto-index project
        project_metadata, status_msg = _get_or_auto_index_project(project_path)
        if not project_metadata:
            return json.dumps({
                "error": status_msg.replace("Auto-indexing complete!\n", "")
            })
        
        # Get the metadata extractor and create reference tracker
        extractor = get_metadata_extractor()
        try:
            from ..reference_tracker import ReferenceTracker
        except ImportError:
            from reference_tracker import ReferenceTracker
        
        reference_tracker = ReferenceTracker(extractor.indexer)
        
        # Find all references
        result = reference_tracker.find_all_references(symbol_name, 
                                                      project_metadata)
        
        if result.total_references == 0:
            return json.dumps({
                "status": "success",
                "symbol_name": symbol_name,
                "total_references": 0,
                "message": f"No references found for '{symbol_name}'"
            })
        
        # Group references by file
        references_by_file = {}
        for ref in result.references:
            if ref.file_path not in references_by_file:
                references_by_file[ref.file_path] = []
            references_by_file[ref.file_path].append({
                "line_number": ref.line,
                "reference_type": ref.reference_type,
                "context": ref.context_line.strip(),
                "column": getattr(ref, 'column', None)
            })
        
        # Sort references within each file by line number
        for file_refs in references_by_file.values():
            file_refs.sort(key=lambda x: x["line_number"])
        
        # Group by reference type
        references_by_type = {}
        for ref in result.references:
            ref_type = ref.reference_type
            if ref_type not in references_by_type:
                references_by_type[ref_type] = 0
            references_by_type[ref_type] += 1
        
        # Find the definition if available
        definition_info = None
        if include_definitions:
            # Try to find the symbol definition
            function_info = project_metadata.find_function(symbol_name)
            if function_info:
                # Find which file contains this function
                for file_path, file_metadata in project_metadata.files.items():
                    if any(f.name == symbol_name or f.full_name == symbol_name 
                           for f in file_metadata.functions + 
                           [m for c in file_metadata.classes for m in c.methods]):
                        definition_info = {
                            "type": "function",
                            "file_path": file_path,
                            "start_line": function_info.start_line,
                            "end_line": function_info.end_line,
                            "full_name": function_info.full_name,
                            "is_method": function_info.is_method,
                            "class_name": function_info.class_name
                        }
                        break
            else:
                # Try to find as a class
                all_classes = project_metadata.get_all_classes()
                for cls in all_classes:
                    if cls.name == symbol_name:
                        # Find which file contains this class
                        for file_path, file_metadata in project_metadata.files.items():
                            if any(c.name == symbol_name for c in file_metadata.classes):
                                definition_info = {
                                    "type": "class",
                                    "file_path": file_path,
                                    "start_line": cls.start_line,
                                    "end_line": cls.end_line,
                                    "base_classes": cls.base_classes
                                }
                                break
                        break
        
        # Calculate some statistics
        files_with_references = len(references_by_file)
        most_referenced_file = max(references_by_file.items(), 
                                  key=lambda x: len(x[1]))[0] if references_by_file else None
        
        response_data = {
            "status": "success",
            "symbol_name": symbol_name,
            "symbol_type": result.symbol_type,
            "total_references": result.total_references,
            "files_with_references": files_with_references,
            "most_referenced_file": most_referenced_file,
            "definition": definition_info,
            "references_by_type": references_by_type,
            "references_by_file": references_by_file,
            "all_references": [{
                "file_path": ref.file_path,
                "line_number": ref.line,
                "reference_type": ref.reference_type,
                "context": ref.context_line.strip(),
                "column": getattr(ref, 'column', None)
            } for ref in result.references]
        }
        
        return json.dumps(response_data, indent=2)
        
    except Exception as e:
        logging.error(f"Failed to find references for {symbol_name}: {e}")
        return json.dumps({"error": f"Failed to find references: {str(e)}"})


def register_reference_tools(mcp_server: "FastMCP") -> None:
    """Register reference-related tools with the MCP server."""

    # Import sandbox protection
    try:
        from . import sandbox_protect
    except ImportError:
        from __init__ import sandbox_protect

    @mcp_server.tool()
    def find_all_references_tool(symbol_name: str,
                                project_path: Optional[str] = None,
                                include_definitions: bool = Field(default=True,
                                                                description="Whether to include definition location")) -> str:
        """
        Find all references to a symbol (function, class, variable) in the codebase.
        Will auto-index the project if not already indexed.

        Args:
            symbol_name: Name of the symbol to find references for
            project_path: Optional project path to search in (auto-indexes if needed)
            include_definitions: Whether to include the definition location

        Returns:
            JSON with all references found, organized by file and type
        """
        # Apply sandbox protection as a protective glove
        protected_function = sandbox_protect(find_all_references)
        return protected_function(symbol_name, project_path, include_definitions)
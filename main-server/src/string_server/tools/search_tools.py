"""
Search-related tools for the MCP server.
Handles function and class searching operations.
"""

import json
import logging
from typing import Optional, TYPE_CHECKING
from pydantic import Field

from .base import get_metadata_extractor, _get_or_auto_index_project

if TYPE_CHECKING:
    from mcp.server.fastmcp import FastMCP


def find_function(function_name: str, project_path: Optional[str] = None) -> str:
    """
    Find a function by name in the indexed codebase.
    Will auto-index the project if not already indexed.
    
    Args:
        function_name: Name of the function to find
        project_path: Optional project path to search in (auto-indexes if needed)
    
    Returns:
        JSON with detailed information about the function
    """
    try:
        # Get or auto-index project
        project_metadata, status_msg = _get_or_auto_index_project(project_path)
        if not project_metadata:
            return json.dumps({
                "error": status_msg.replace("Auto-indexing complete!\n", "")
            })
        
        # Find the function
        function_info = project_metadata.find_function(function_name)
        if not function_info:
            return json.dumps({
                "error": f"Function '{function_name}' not found in the codebase"
            })
        
        # Get file path for the function
        file_path = None
        for path, metadata in project_metadata.files.items():
            if any(f.name == function_name or f.full_name == function_name 
                   for f in metadata.functions + 
                   [m for c in metadata.classes for m in c.methods]):
                file_path = path
                break
        
        result = {
            "status": "success",
            "function": {
                "name": function_info.name,
                "full_name": function_info.full_name,
                "file_path": file_path,
                "start_line": function_info.start_line,
                "end_line": function_info.end_line,
                "parameters": function_info.parameters,
                "return_type": function_info.return_type,
                "is_method": function_info.is_method,
                "class_name": function_info.class_name,
                "decorators": function_info.decorators,
                "docstring": function_info.docstring
            }
        }
        
        return json.dumps(result, indent=2)
        
    except Exception as e:
        logging.error(f"Failed to find function {function_name}: {e}")
        return json.dumps({"error": f"Failed to find function: {str(e)}"})


def get_function_context(function_name: str, 
                        project_path: Optional[str] = None,
                        max_depth: int = 2) -> str:
    """
    Get comprehensive context for a function including its relationships.
    
    Args:
        function_name: Name of the function
        project_path: Optional project path
        max_depth: Maximum depth for relationship analysis
    
    Returns:
        Comprehensive context information
    """
    try:
        # Get project metadata
        project_metadata = None
        if project_path:
            from pathlib import Path
            project_path = str(Path(project_path).resolve())
            from .base import project_cache
            cache = project_cache()
            if project_path in cache:
                project_metadata = cache[project_path]
            else:
                return f"Project {project_path} not indexed. Use index_project() first."
        else:
            from .base import project_cache
            cache = project_cache()
            if cache:
                project_metadata = next(iter(cache.values()))
            else:
                return "No projects indexed. Use index_project() first."
        
        # Get function context
        extractor = get_metadata_extractor()
        context = extractor.get_function_context(function_name, 
                                                project_metadata, max_depth)
        if not context:
            return f"Function '{function_name}' not found or context unavailable."
        
        # Format the response
        func_info = context["function"]
        result = f"""Function Context: {func_info['full_name']}

File: {context['file']}
Location: Lines {func_info['start_line']}-{func_info['end_line']}
Parameters: {', '.join(func_info['parameters']) if func_info['parameters'] else 'None'}

"""
        
        # Add class context if it's a method
        if context.get("class_context"):
            class_info = context["class_context"]
            result += f"Class: {class_info['name']} (lines {class_info['start_line']}-{class_info['end_line']})\n"
            if class_info.get('base_classes'):
                result += f"Inherits from: {', '.join(class_info['base_classes'])}\n"
        
        # Add related functions
        related = context.get("related_functions", {})
        if related.get("direct_calls"):
            result += f"\nDirectly calls: {', '.join(related['direct_calls'])}\n"
        
        if related.get("direct_callers"):
            result += f"Called by: {', '.join(related['direct_callers'])}\n"
        
        if related.get("indirect_related"):
            result += "\nIndirectly related functions:\n"
            for rel in related["indirect_related"][:10]:  # Limit to 10
                result += f"  - {rel['function']} (distance: {rel['distance']})\n"
        
        # Add imports context
        imports = context.get("imports", [])
        if imports:
            result += f"\nFile imports:\n"
            for imp in imports[:5]:  # Show first 5 imports
                if imp.get('items'):
                    result += f"  - from {imp['module']} import {', '.join(imp['items'])}\n"
                else:
                    result += f"  - import {imp['module']}\n"
        
        return result
        
    except Exception as e:
        logging.error(f"Failed to get function context for {function_name}: {e}")
        return f"Error getting function context: {str(e)}"


def list_functions(project_path: Optional[str] = None,
                  class_name: Optional[str] = None,
                  limit: int = 50) -> str:
    """
    List all functions in the indexed codebase.
    Will auto-index the project if not already indexed.
    
    Args:
        project_path: Optional project path
        class_name: Optional class name to filter methods
        limit: Maximum number of functions to return
    
    Returns:
        JSON with list of functions and basic information
    """
    try:
        # Get or auto-index project
        project_metadata, status_msg = _get_or_auto_index_project(project_path)
        if not project_metadata:
            return json.dumps({
                "error": status_msg.replace("Auto-indexing complete!\n", "")
            })
        
        # Get all functions
        all_functions = project_metadata.get_all_functions()
        
        # Filter by class if specified
        if class_name:
            all_functions = [f for f in all_functions 
                           if f.class_name == class_name]
        
        # Limit results
        functions_to_show = all_functions[:limit]
        
        # Get file paths for functions
        file_map = {}
        for path, metadata in project_metadata.files.items():
            for func in metadata.functions:
                file_map[func.full_name] = path
            for cls in metadata.classes:
                for method in cls.methods:
                    file_map[method.full_name] = path
        
        # Format the response
        functions_data = []
        for func in functions_to_show:
            docstring_preview = None
            if func.docstring:
                docstring_preview = func.docstring.split('\n')[0][:100] + "..."
            
            functions_data.append({
                "name": func.name,
                "full_name": func.full_name,
                "file_path": file_map.get(func.full_name),
                "start_line": func.start_line,
                "end_line": func.end_line,
                "parameters": func.parameters,
                "return_type": func.return_type,
                "is_method": func.is_method,
                "class_name": func.class_name,
                "decorators": func.decorators,
                "docstring": docstring_preview
            })
        
        result = {
            "status": "success",
            "total_functions": len(all_functions),
            "showing": len(functions_to_show),
            "filter": {"class_name": class_name} if class_name else None,
            "functions": functions_data
        }
        
        return json.dumps(result, indent=2)
        
    except Exception as e:
        logging.error(f"Failed to list functions: {e}")
        return json.dumps({"error": f"Failed to list functions: {str(e)}"})


def list_classes(project_path: Optional[str] = None, limit: int = 30) -> str:
    """
    List all classes in the indexed codebase.
    Will auto-index the project if not already indexed.
    
    Args:
        project_path: Optional project path
        limit: Maximum number of classes to return
    
    Returns:
        JSON with list of classes and basic information
    """
    try:
        # Get or auto-index project
        project_metadata, status_msg = _get_or_auto_index_project(project_path)
        if not project_metadata:
            return json.dumps({
                "error": status_msg.replace("Auto-indexing complete!\n", "")
            })
        
        # Get all classes
        all_classes = project_metadata.get_all_classes()
        
        # Limit results
        classes_to_show = all_classes[:limit]
        
        # Get file paths for classes
        file_map = {}
        for path, metadata in project_metadata.files.items():
            for cls in metadata.classes:
                file_map[cls.name] = path
        
        # Format the response
        classes_data = []
        for cls in classes_to_show:
            docstring_preview = None
            if cls.docstring:
                docstring_preview = cls.docstring.split('\n')[0][:100] + "..."
            
            classes_data.append({
                "name": cls.name,
                "file_path": file_map.get(cls.name),
                "start_line": cls.start_line,
                "end_line": cls.end_line,
                "base_classes": cls.base_classes,
                "decorators": cls.decorators,
                "method_count": len(cls.methods),
                "methods": [{"name": m.name, "line": m.start_line} 
                          for m in cls.methods],
                "docstring": docstring_preview
            })
        
        result = {
            "status": "success",
            "total_classes": len(all_classes),
            "showing": len(classes_to_show),
            "classes": classes_data
        }
        
        return json.dumps(result, indent=2)
        
    except Exception as e:
        logging.error(f"Failed to list classes: {e}")
        return json.dumps({"error": f"Failed to list classes: {str(e)}"})


def register_search_tools(mcp_server: "FastMCP") -> None:
    """Register search-related tools with the MCP server."""

    # Import sandbox protection
    try:
        from . import sandbox_protect
    except ImportError:
        from __init__ import sandbox_protect

    @mcp_server.tool()
    def find_function_tool(function_name: str,
                          project_path: Optional[str] = None) -> str:
        """
        Find a function by name in the indexed codebase.
        Will auto-index the project if not already indexed.

        Args:
            function_name: Name of the function to find
            project_path: Optional project path to search in (auto-indexes if needed)

        Returns:
            JSON with detailed information about the function
        """
        # Apply sandbox protection as a protective glove
        protected_function = sandbox_protect(find_function)
        return protected_function(function_name, project_path)
    
    @mcp_server.tool()
    def get_function_context_tool(function_name: str,
                                 project_path: Optional[str] = None,
                                 max_depth: int = Field(default=2, 
                                                       description="Maximum depth for finding related functions")) -> str:
        """
        Get comprehensive context for a function including its relationships.
        
        Args:
            function_name: Name of the function
            project_path: Optional project path
            max_depth: Maximum depth for relationship analysis
        
        Returns:
            Comprehensive context information
        """
        # Apply sandbox protection as a protective glove
        protected_function = sandbox_protect(get_function_context)
        return protected_function(function_name, project_path, max_depth)
    
    @mcp_server.tool()
    def list_functions_tool(project_path: Optional[str] = None,
                           class_name: Optional[str] = None,
                           limit: int = Field(default=50, 
                                            description="Maximum number of functions to return")) -> str:
        """
        List all functions in the indexed codebase.
        Will auto-index the project if not already indexed.
        
        Args:
            project_path: Optional project path
            class_name: Optional class name to filter methods
            limit: Maximum number of functions to return
        
        Returns:
            JSON with list of functions and basic information
        """
        # Apply sandbox protection as a protective glove
        protected_function = sandbox_protect(list_functions)
        return protected_function(project_path, class_name, limit)
    
    @mcp_server.tool()
    def list_classes_tool(project_path: Optional[str] = None,
                         limit: int = Field(default=30, 
                                          description="Maximum number of classes to return")) -> str:
        """
        List all classes in the indexed codebase.
        Will auto-index the project if not already indexed.
        
        Args:
            project_path: Optional project path
            limit: Maximum number of classes to return
        
        Returns:
            JSON with list of classes and basic information
        """
        # Apply sandbox protection as a protective glove
        protected_function = sandbox_protect(list_classes)
        return protected_function(project_path, limit)
"""
Indexing-related tools for the MCP server.
Handles project and file indexing operations.
"""

import logging
from pathlib import Path
from typing import TYPE_CHECKING, Optional

from .base import get_metadata_extractor, project_cache, _get_or_auto_index_project, _is_server_directory

if TYPE_CHECKING:
    from mcp.server.fastmcp import FastMCP


def index_project(project_path: str) -> str:
    """
    Index an entire Python project and build comprehensive metadata.
    
    Args:
        project_path: Path to the Python project directory
    
    Returns:
        Summary of the indexing results
    """
    try:
        project_path_obj = Path(project_path).resolve()
        
        # SECURITY CHECK: Prevent indexing server directories
        if _is_server_directory(str(project_path_obj)):
            return "Access denied: Operation not allowed on server directories for security purposes"

        if not project_path_obj.exists():
            return f"Error: Project path {project_path} does not exist"
        
        if not project_path_obj.is_dir():
            return f"Error: {project_path} is not a directory"
        
        # Extract project metadata
        extractor = get_metadata_extractor()
        project_metadata = extractor.extract_project_metadata(str(project_path_obj))
        
        # Cache the results
        cache = project_cache()
        cache[str(project_path_obj)] = project_metadata
        
        # Generate summary
        total_files = len(project_metadata.files)
        total_functions = len(project_metadata.get_all_functions())
        total_classes = len(project_metadata.get_all_classes())
        
        summary = f"""Project indexed successfully!
        
Project: {project_path_obj}
Files analyzed: {total_files}
Functions found: {total_functions}
Classes found: {total_classes}

Use other tools to explore the codebase:
- find_function() to locate specific functions
- get_function_context() to understand function relationships
- analyze_call_graph() to see function dependencies
- list_functions() to see all available functions
"""
        
        return summary
        
    except Exception as e:
        logging.error(f"Failed to index project {project_path}: {e}")
        return f"Error indexing project: {str(e)}"


def index_file(file_path: str) -> str:
    """
    Index a single Python file and extract its metadata.
    
    Args:
        file_path: Path to the Python file
    
    Returns:
        Summary of the file analysis
    """
    try:
        file_path_obj = Path(file_path).resolve()
        
        # SECURITY CHECK: Prevent indexing server files
        if _is_server_directory(str(file_path_obj.parent)):
            return "Access denied: Operation not allowed on server directories for security purposes"
        
        if not file_path_obj.exists():
            return f"Error: File path {file_path} does not exist"
        
        if not file_path_obj.is_file():
            return f"Error: {file_path} is not a file"
        
        if not file_path_obj.suffix == '.py':
            return f"Error: {file_path} is not a Python file"
        
        # Extract file metadata
        extractor = get_metadata_extractor()
        file_metadata = extractor.extract_file_metadata(str(file_path_obj))
        
        if not file_metadata:
            return f"Error: Failed to analyze file {file_path}"
        
        # Generate summary
        functions = file_metadata.functions
        classes = file_metadata.classes
        imports = file_metadata.imports
        
        summary = f"""File indexed successfully!
        
File: {file_path_obj}
Functions found: {len(functions)}
Classes found: {len(classes)}
Imports found: {len(imports)}

Functions:
{chr(10).join([f"  - {func.name}" for func in functions[:5]])}
{f"  ... and {len(functions) - 5} more" if len(functions) > 5 else ""}

Classes:
{chr(10).join([f"  - {cls.name}" for cls in classes[:5]])}
{f"  ... and {len(classes) - 5} more" if len(classes) > 5 else ""}

Use other tools to explore this file's relationships:
- get_function_context() to understand function relationships
- analyze_call_graph() to see function dependencies
- find_all_references() to see where symbols are used
"""
        
        return summary
        
    except Exception as e:
        logging.error(f"Failed to index file {file_path}: {e}")
        return f"Error indexing file: {str(e)}"


def register_indexing_tools(mcp_server: "FastMCP") -> None:
    """
    Register indexing-related tools with the MCP server.

    Note: Manual indexing tools removed since VSCode extension
    automatically handles code indexing through the complete pipeline:
    VSCode → Chunks → Rich Metadata → Embeddings → Vector Storage
    """

    # Manual indexing tools removed - VSCode extension handles this automatically
    # The following tools were removed:
    # - index_project_tool: VSCode extension auto-indexes projects
    # - index_file_tool: VSCode extension auto-indexes files

    # If manual indexing is needed for debugging, use the functions directly:
    # - index_project(project_path)
    # - index_file(file_path)

    pass  # No tools to register - VSCode extension handles indexing
    
    # Removed workspace discovery tools - violates MCP remote operation principles
    # Code should be streamed from IDE, not discovered by scanning filesystem
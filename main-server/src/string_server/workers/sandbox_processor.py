"""
Sandboxed processor for isolated code analysis with resource limits.
Provides secure execution environment for MCP tools with comprehensive monitoring.
"""

import os
import sys
import time
import signal
import psutil
import tempfile
import subprocess
import threading
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from contextlib import contextmanager
import json
import traceback

try:
    import docker
    DOCKER_AVAILABLE = True
except ImportError:
    docker = None
    DOCKER_AVAILABLE = False


class ProcessingStatus(Enum):
    """Processing status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"
    RESOURCE_LIMIT_EXCEEDED = "resource_limit_exceeded"
    SECURITY_VIOLATION = "security_violation"


class SecurityViolationType(Enum):
    """Security violation types."""
    NETWORK_ACCESS = "network_access"
    FILE_SYSTEM_ACCESS = "file_system_access"
    PROCESS_CREATION = "process_creation"
    SYSTEM_CALL = "system_call"
    MEMORY_VIOLATION = "memory_violation"


@dataclass
class ResourceLimits:
    """Resource limits for sandboxed execution."""
    max_memory_mb: int = 512
    max_cpu_percent: float = 50.0
    max_execution_time_seconds: int = 300
    max_file_size_mb: int = 100
    max_open_files: int = 100
    max_processes: int = 5
    allow_network: bool = False
    allow_subprocess: bool = False
    allowed_file_paths: List[str] = field(default_factory=list)
    blocked_file_paths: List[str] = field(default_factory=list)
    allowed_modules: List[str] = field(default_factory=list)
    blocked_modules: List[str] = field(default_factory=list)


@dataclass
class SecurityViolation:
    """Security violation details."""
    violation_type: SecurityViolationType
    description: str
    timestamp: datetime
    details: Dict[str, Any]


@dataclass
class ResourceUsage:
    """Resource usage tracking."""
    peak_memory_mb: float = 0.0
    peak_cpu_percent: float = 0.0
    execution_time_seconds: float = 0.0
    files_opened: int = 0
    processes_created: int = 0
    network_connections: int = 0


@dataclass
class ProcessingResult:
    """Result of sandboxed processing."""
    processing_id: str
    status: ProcessingStatus
    result: Optional[Any]
    error_message: Optional[str]
    resource_usage: ResourceUsage
    security_violations: List[SecurityViolation]
    execution_log: List[str]
    start_time: datetime
    end_time: Optional[datetime]
    metadata: Dict[str, Any] = field(default_factory=dict)


class ResourceMonitor:
    """Monitor resource usage during execution."""
    
    def __init__(self, limits: ResourceLimits, process_id: int):
        """Initialize resource monitor."""
        self.limits = limits
        self.process_id = process_id
        self.usage = ResourceUsage()
        self.violations: List[SecurityViolation] = []
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.logger = logging.getLogger(__name__)
    
    def start_monitoring(self) -> None:
        """Start resource monitoring."""
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            daemon=True
        )
        self.monitor_thread.start()
    
    def stop_monitoring(self) -> None:
        """Stop resource monitoring."""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
    
    def _monitor_loop(self) -> None:
        """Main monitoring loop."""
        try:
            process = psutil.Process(self.process_id)
            
            while self.monitoring:
                try:
                    # Memory usage
                    memory_info = process.memory_info()
                    memory_mb = memory_info.rss / (1024 * 1024)
                    self.usage.peak_memory_mb = max(
                        self.usage.peak_memory_mb, memory_mb
                    )
                    
                    # CPU usage
                    cpu_percent = process.cpu_percent()
                    self.usage.peak_cpu_percent = max(
                        self.usage.peak_cpu_percent, cpu_percent
                    )
                    
                    # File handles
                    try:
                        open_files = len(process.open_files())
                        self.usage.files_opened = max(
                            self.usage.files_opened, open_files
                        )
                    except (psutil.AccessDenied, psutil.NoSuchProcess):
                        pass
                    
                    # Network connections
                    try:
                        connections = len(process.connections())
                        self.usage.network_connections = max(
                            self.usage.network_connections, connections
                        )
                        
                        if connections > 0 and not self.limits.allow_network:
                            self._record_violation(
                                SecurityViolationType.NETWORK_ACCESS,
                                f"Network connection detected: {connections} active"
                            )
                    except (psutil.AccessDenied, psutil.NoSuchProcess):
                        pass
                    
                    # Check limits
                    self._check_limits(memory_mb, cpu_percent)
                    
                    time.sleep(0.1)  # Monitor every 100ms
                    
                except psutil.NoSuchProcess:
                    break
                except Exception as e:
                    self.logger.warning(f"Monitoring error: {e}")
                    
        except Exception as e:
            self.logger.error(f"Monitor loop failed: {e}")
    
    def _check_limits(self, memory_mb: float, cpu_percent: float) -> None:
        """Check if resource limits are exceeded."""
        if memory_mb > self.limits.max_memory_mb:
            self._record_violation(
                SecurityViolationType.MEMORY_VIOLATION,
                f"Memory limit exceeded: {memory_mb:.1f}MB > "
                f"{self.limits.max_memory_mb}MB"
            )
        
        # Note: CPU limit checking is informational only
        # as it's difficult to enforce reliably
    
    def _record_violation(self, violation_type: SecurityViolationType,
                         description: str, details: Optional[Dict] = None
                         ) -> None:
        """Record a security violation."""
        violation = SecurityViolation(
            violation_type=violation_type,
            description=description,
            timestamp=datetime.utcnow(),
            details=details or {}
        )
        self.violations.append(violation)
        self.logger.warning(f"Security violation: {description}")


class SandboxProcessor:
    """
    Sandboxed processor for secure code analysis.
    
    Provides isolated execution environment with:
    - Resource limits enforcement
    - Security monitoring
    - File system restrictions
    - Network isolation
    - Process monitoring
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize sandbox processor."""
        self.logger = logging.getLogger(__name__)
        self.config = config or {}
        self.temp_dir = Path(tempfile.gettempdir()) / "mcp_sandbox"
        self.temp_dir.mkdir(exist_ok=True)
        
        # Default resource limits
        self.default_limits = ResourceLimits(
            max_memory_mb=int(os.environ.get('SANDBOX_MAX_MEMORY_MB', 512)),
            max_cpu_percent=float(
                os.environ.get('SANDBOX_MAX_CPU_PERCENT', 50.0)
            ),
            max_execution_time_seconds=int(
                os.environ.get('SANDBOX_MAX_TIME_SECONDS', 300)
            ),
            max_file_size_mb=int(
                os.environ.get('SANDBOX_MAX_FILE_SIZE_MB', 100)
            ),
            allow_network=os.environ.get(
                'SANDBOX_ALLOW_NETWORK', 'false'
            ).lower() == 'true',
            allow_subprocess=os.environ.get(
                'SANDBOX_ALLOW_SUBPROCESS', 'false'
            ).lower() == 'true'
        )
        
        # Initialize Docker client if available
        self.docker_client = None
        if DOCKER_AVAILABLE and self.config.get('use_docker', False):
            try:
                self.docker_client = docker.from_env()
                self.logger.info("Docker client initialized")
            except Exception as e:
                self.logger.warning(f"Docker initialization failed: {e}")
    
    def generate_processing_id(self) -> str:
        """Generate unique processing ID."""
        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')
        return f"proc_{timestamp}"
    
    @contextmanager
    def _timeout_context(self, timeout_seconds: int):
        """Context manager for execution timeout (simplified for cross-platform compatibility)."""
        import platform

        # On Windows, signal-based timeouts don't work well
        # For now, we'll use a simplified approach that focuses on monitoring
        if platform.system() == "Windows":
            # Simple pass-through context for Windows
            try:
                yield
            finally:
                pass
        else:
            # Unix-like systems can use signal-based timeouts
            def timeout_handler(signum, frame):
                raise TimeoutError(f"Execution timeout after {timeout_seconds}s")

            # Set up timeout signal
            old_handler = signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(timeout_seconds)

            try:
                yield
            finally:
                signal.alarm(0)
                signal.signal(signal.SIGALRM, old_handler)
    
    def _create_restricted_environment(self, limits: ResourceLimits
                                     ) -> Dict[str, str]:
        """Create restricted environment variables."""
        env = os.environ.copy()
        
        # Remove potentially dangerous environment variables
        dangerous_vars = [
            'PATH', 'PYTHONPATH', 'LD_LIBRARY_PATH', 'DYLD_LIBRARY_PATH',
            'HOME', 'USER', 'USERNAME', 'LOGNAME'
        ]
        
        for var in dangerous_vars:
            if var in env:
                del env[var]
        
        # Set restricted PATH
        env['PATH'] = '/usr/bin:/bin'
        
        # Set Python path restrictions
        env['PYTHONPATH'] = ''
        env['PYTHONDONTWRITEBYTECODE'] = '1'
        env['PYTHONUNBUFFERED'] = '1'
        
        # Disable network if not allowed
        if not limits.allow_network:
            env['http_proxy'] = 'http://127.0.0.1:1'
            env['https_proxy'] = 'http://127.0.0.1:1'
            env['HTTP_PROXY'] = 'http://127.0.0.1:1'
            env['HTTPS_PROXY'] = 'http://127.0.0.1:1'
        
        return env
    
    def _validate_file_access(self, file_path: str, limits: ResourceLimits
                            ) -> bool:
        """Validate if file access is allowed."""
        path = Path(file_path).resolve()
        
        # Check blocked paths
        for blocked_path in limits.blocked_file_paths:
            if str(path).startswith(str(Path(blocked_path).resolve())):
                return False
        
        # Check allowed paths (if specified)
        if limits.allowed_file_paths:
            for allowed_path in limits.allowed_file_paths:
                if str(path).startswith(str(Path(allowed_path).resolve())):
                    return True
            return False
        
        # Default: allow access to temp directory and current working directory
        temp_path = Path(tempfile.gettempdir()).resolve()
        cwd_path = Path.cwd().resolve()
        
        return (str(path).startswith(str(temp_path)) or
                str(path).startswith(str(cwd_path)))
    
    def _execute_in_process(self, func: Callable, args: tuple, kwargs: dict,
                          limits: ResourceLimits) -> ProcessingResult:
        """Execute function in separate process with monitoring."""
        processing_id = self.generate_processing_id()
        start_time = datetime.utcnow()
        execution_log: List[str] = []
        
        try:
            # Create execution wrapper
            def execution_wrapper():
                try:
                    # Set up resource limits
                    if hasattr(os, 'setrlimit'):
                        import resource
                        
                        # Memory limit
                        memory_limit = limits.max_memory_mb * 1024 * 1024
                        resource.setrlimit(
                            resource.RLIMIT_AS, (memory_limit, memory_limit)
                        )
                        
                        # File size limit
                        file_size_limit = limits.max_file_size_mb * 1024 * 1024
                        resource.setrlimit(
                            resource.RLIMIT_FSIZE,
                            (file_size_limit, file_size_limit)
                        )
                        
                        # Open files limit
                        resource.setrlimit(
                            resource.RLIMIT_NOFILE,
                            (limits.max_open_files, limits.max_open_files)
                        )
                    
                    # Execute function
                    result = func(*args, **kwargs)
                    return result
                    
                except Exception as e:
                    execution_log.append(f"Execution error: {str(e)}")
                    execution_log.append(traceback.format_exc())
                    raise
            
            # Start resource monitoring
            monitor = ResourceMonitor(limits, os.getpid())
            monitor.start_monitoring()
            
            try:
                # Execute with timeout (cross-platform)
                with self._timeout_context(limits.max_execution_time_seconds):
                    result = execution_wrapper()
                
                end_time = datetime.utcnow()
                monitor.usage.execution_time_seconds = (
                    end_time - start_time
                ).total_seconds()
                
                status = ProcessingStatus.COMPLETED
                error_message = None
                
            except TimeoutError as e:
                end_time = datetime.utcnow()
                status = ProcessingStatus.TIMEOUT
                error_message = str(e)
                result = None
                execution_log.append(f"Timeout: {error_message}")
                
            except MemoryError as e:
                end_time = datetime.utcnow()
                status = ProcessingStatus.RESOURCE_LIMIT_EXCEEDED
                error_message = f"Memory limit exceeded: {str(e)}"
                result = None
                execution_log.append(f"Memory error: {error_message}")
                
            except Exception as e:
                end_time = datetime.utcnow()
                status = ProcessingStatus.FAILED
                error_message = str(e)
                result = None
                execution_log.append(f"Execution failed: {error_message}")
                
            finally:
                monitor.stop_monitoring()
            
            # Check for security violations
            if monitor.violations:
                status = ProcessingStatus.SECURITY_VIOLATION
                if not error_message:
                    error_message = f"Security violations: {len(monitor.violations)}"
            
            return ProcessingResult(
                processing_id=processing_id,
                status=status,
                result=result,
                error_message=error_message,
                resource_usage=monitor.usage,
                security_violations=monitor.violations,
                execution_log=execution_log,
                start_time=start_time,
                end_time=end_time
            )
            
        except Exception as e:
            end_time = datetime.utcnow()
            execution_log.append(f"Processing setup failed: {str(e)}")
            
            return ProcessingResult(
                processing_id=processing_id,
                status=ProcessingStatus.FAILED,
                result=None,
                error_message=f"Processing setup failed: {str(e)}",
                resource_usage=ResourceUsage(),
                security_violations=[],
                execution_log=execution_log,
                start_time=start_time,
                end_time=end_time
            )
    
    def _execute_in_docker(self, func: Callable, args: tuple, kwargs: dict,
                         limits: ResourceLimits) -> ProcessingResult:
        """Execute function in Docker container."""
        processing_id = self.generate_processing_id()
        start_time = datetime.utcnow()
        execution_log: List[str] = []
        
        if not self.docker_client:
            return ProcessingResult(
                processing_id=processing_id,
                status=ProcessingStatus.FAILED,
                result=None,
                error_message="Docker client not available",
                resource_usage=ResourceUsage(),
                security_violations=[],
                execution_log=["Docker client not initialized"],
                start_time=start_time,
                end_time=datetime.utcnow()
            )
        
        try:
            # TODO(out_of_scope): Implement Docker-based execution
            # This would require:
            # 1. Serializing function and arguments
            # 2. Creating secure Docker image
            # 3. Running container with resource limits
            # 4. Collecting results and monitoring data
            
            execution_log.append("Docker execution not yet implemented")
            
            return ProcessingResult(
                processing_id=processing_id,
                status=ProcessingStatus.FAILED,
                result=None,
                error_message="Docker execution not implemented",
                resource_usage=ResourceUsage(),
                security_violations=[],
                execution_log=execution_log,
                start_time=start_time,
                end_time=datetime.utcnow()
            )
            
        except Exception as e:
            execution_log.append(f"Docker execution failed: {str(e)}")
            
            return ProcessingResult(
                processing_id=processing_id,
                status=ProcessingStatus.FAILED,
                result=None,
                error_message=f"Docker execution failed: {str(e)}",
                resource_usage=ResourceUsage(),
                security_violations=[],
                execution_log=execution_log,
                start_time=start_time,
                end_time=datetime.utcnow()
            )
    
    def execute_safely(self, func: Callable, *args,
                      limits: Optional[ResourceLimits] = None,
                      use_docker: bool = False,
                      **kwargs) -> ProcessingResult:
        """
        Execute function safely in sandboxed environment.
        
        Args:
            func: Function to execute
            *args: Function arguments
            limits: Resource limits (uses defaults if None)
            use_docker: Whether to use Docker container
            **kwargs: Function keyword arguments
            
        Returns:
            Processing result with execution details
        """
        if limits is None:
            limits = self.default_limits
        
        self.logger.info(
            f"Starting sandboxed execution: {func.__name__} "
            f"(docker={use_docker})"
        )
        
        if use_docker and self.docker_client:
            return self._execute_in_docker(func, args, kwargs, limits)
        else:
            return self._execute_in_process(func, args, kwargs, limits)
    
    def execute_mcp_tool(self, tool_name: str, tool_args: Dict[str, Any],
                        limits: Optional[ResourceLimits] = None) -> ProcessingResult:
        """
        Execute MCP tool in sandboxed environment.
        
        Args:
            tool_name: Name of MCP tool to execute
            tool_args: Tool arguments
            limits: Resource limits
            
        Returns:
            Processing result
        """
        if limits is None:
            limits = self.default_limits
        
        def tool_wrapper():
            try:
                # Import MCP tools dynamically to avoid loading in main process
                # Note: workspace_tools removed - violates MCP remote operation
                from ..tools import (
                    indexing_tools, search_tools,
                    analysis_tools, reference_tools
                )
                
                # Map tool names to functions
                # Note: Removed workspace tools - code should be streamed from IDE
                tool_map = {
                    
                    # Indexing tools
                    'index_project': indexing_tools.index_project,
                    'index_file': indexing_tools.index_file,
                    
                    # Search tools
                    'find_function': search_tools.find_function,
                    'get_function_context': search_tools.get_function_context,
                    'list_functions': search_tools.list_functions,
                    'list_classes': search_tools.list_classes,
                    
                    # Analysis tools
                    'analyze_impact': analysis_tools.analyze_impact,
                    'analyze_call_graph': analysis_tools.analyze_call_graph,
                    'analyze_dead_code': analysis_tools.analyze_dead_code,
                    
                    # Reference tools
                    'find_all_references': reference_tools.find_all_references,
                }
                
                if tool_name not in tool_map:
                    raise ValueError(f"Unknown tool: {tool_name}")
                
                tool_func = tool_map[tool_name]
                
                # Execute tool with arguments
                if isinstance(tool_args, dict):
                    result = tool_func(**tool_args)
                else:
                    result = tool_func(tool_args)
                
                return result
                
            except Exception as e:
                self.logger.error(f"Tool execution failed: {e}")
                raise
        
        return self.execute_safely(
            tool_wrapper,
            limits=limits,
            use_docker=self.config.get('use_docker_for_tools', False)
        )
    
    def get_sandbox_stats(self) -> Dict[str, Any]:
        """Get sandbox processor statistics."""
        return {
            'config': {
                'docker_available': DOCKER_AVAILABLE,
                'docker_enabled': self.docker_client is not None,
                'temp_directory': str(self.temp_dir)
            },
            'default_limits': {
                'max_memory_mb': self.default_limits.max_memory_mb,
                'max_cpu_percent': self.default_limits.max_cpu_percent,
                'max_execution_time_seconds': self.default_limits.max_execution_time_seconds,
                'allow_network': self.default_limits.allow_network,
                'allow_subprocess': self.default_limits.allow_subprocess
            },
            'system_info': {
                'cpu_count': psutil.cpu_count(),
                'memory_total_mb': psutil.virtual_memory().total / (1024 * 1024),
                'memory_available_mb': psutil.virtual_memory().available / (1024 * 1024)
            }
        }
    
    def cleanup_temp_files(self, max_age_hours: int = 24) -> int:
        """
        Clean up old temporary files.
        
        Args:
            max_age_hours: Maximum age of files to keep
            
        Returns:
            Number of files cleaned up
        """
        cleaned_count = 0
        cutoff_time = datetime.utcnow() - timedelta(hours=max_age_hours)
        
        try:
            for file_path in self.temp_dir.iterdir():
                if file_path.is_file():
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_time < cutoff_time:
                        try:
                            file_path.unlink()
                            cleaned_count += 1
                        except Exception as e:
                            self.logger.warning(
                                f"Failed to cleanup {file_path}: {e}"
                            )
        except Exception as e:
            self.logger.error(f"Cleanup failed: {e}")
        
        return cleaned_count
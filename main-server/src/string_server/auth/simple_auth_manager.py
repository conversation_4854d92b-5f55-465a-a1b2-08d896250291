"""
Simple Authentication Manager for Connection Identification

This module provides a lightweight API key system focused on identifying
connections between VSCode extension and MCP server, not security protection.

Key Features:
- Simple user identification
- Connection tracking
- Basic usage statistics
- No complex security measures (as per requirements)
- Easy API key generation and validation

Note: This module has been moved to src/auth/ following Python best practices.
"""

import json
import logging
import os
import secrets
import sqlite3
from datetime import datetime
from typing import Optional, Dict, Any
from dataclasses import dataclass
from pathlib import Path

# Import thread-safe database manager
try:
    from ..database import get_railway_database_manager, ThreadSafeDatabaseManager
except ImportError:
    from src.database import get_railway_database_manager, ThreadSafeDatabaseManager


@dataclass
class SimpleUser:
    """Simple user representation for connection identification."""
    id: str
    identifier: str  # email or username
    name: str
    created_at: datetime
    api_key: Optional[str] = None
    last_used: Optional[datetime] = None
    usage_count: int = 0


class SimpleApiKeyManager:
    """
    Thread-safe simplified API key manager for connection identification.

    This is NOT a security system - it's purely for identifying
    which VSCode extension is connecting to the MCP server.

    Now uses thread-safe database operations optimized for Railway deployment.
    """

    def __init__(self, db_path: str = "simple_keys.db"):
        """Initialize the thread-safe simple API key manager."""
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)

        # Get thread-safe database manager optimized for Railway
        self.db_manager = get_railway_database_manager(db_path)

        # Initialize database schema
        self._init_database()

        self.logger.info(f"Thread-safe simple API key manager initialized: {db_path}")
    
    def _init_database(self) -> None:
        """Initialize SQLite database with simple schema using thread-safe operations."""
        schema_sql = """
            CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                identifier TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                api_key TEXT UNIQUE,
                created_at TEXT NOT NULL,
                last_used TEXT,
                usage_count INTEGER DEFAULT 0
            );

            -- Create indexes for better performance
            CREATE INDEX IF NOT EXISTS idx_users_identifier ON users(identifier);
            CREATE INDEX IF NOT EXISTS idx_users_api_key ON users(api_key);
            CREATE INDEX IF NOT EXISTS idx_users_last_used ON users(last_used);
        """

        self.db_manager.execute_script(schema_sql)
        self.logger.info("Database schema initialized with thread-safe operations")
    
    def create_user_with_key(self, identifier: str, name: str) -> Optional[SimpleUser]:
        """
        Atomically create a user and generate an API key in one step.

        Args:
            identifier: User identifier (email, username, etc.)
            name: Display name

        Returns:
            SimpleUser with API key or None if creation failed
        """
        try:
            # Generate simple API key (not cryptographically secure - just for identification)
            user_id = f"user_{secrets.token_hex(8)}"
            api_key = f"mcp_{secrets.token_hex(16)}"
            created_at = datetime.utcnow().isoformat()

            # Use atomic operation with retry logic
            def create_operation(conn):
                conn.execute("""
                    INSERT INTO users (id, identifier, name, api_key, created_at, usage_count)
                    VALUES (?, ?, ?, ?, ?, 0)
                """, (user_id, identifier, name, api_key, created_at))
                return user_id

            # Execute with automatic retry on deadlock
            created_user_id = self.db_manager.execute_with_retry(create_operation, immediate=True)

            user = SimpleUser(
                id=created_user_id,
                identifier=identifier,
                name=name,
                created_at=datetime.fromisoformat(created_at),
                api_key=api_key,
                usage_count=0
            )

            self.logger.info(f"Created user with API key: {identifier}")
            return user

        except sqlite3.IntegrityError:
            self.logger.warning(f"User already exists: {identifier}")
            return None
        except Exception as e:
            self.logger.error(f"Error creating user: {e}")
            return None
    
    def validate_api_key(self, api_key: str) -> Optional[SimpleUser]:
        """
        Atomically validate API key and update usage statistics.

        Args:
            api_key: API key to validate

        Returns:
            SimpleUser if valid, None otherwise
        """
        try:
            def validate_operation(conn):
                # First, get the user
                cursor = conn.execute("""
                    SELECT * FROM users WHERE api_key = ?
                """, (api_key,))
                row = cursor.fetchone()

                if not row:
                    return None

                # Update usage statistics atomically
                now = datetime.utcnow().isoformat()
                conn.execute("""
                    UPDATE users
                    SET last_used = ?, usage_count = usage_count + 1
                    WHERE api_key = ?
                """, (now, api_key))

                # Return updated user data
                return SimpleUser(
                    id=row['id'],
                    identifier=row['identifier'],
                    name=row['name'],
                    created_at=datetime.fromisoformat(row['created_at']),
                    api_key=row['api_key'],
                    last_used=datetime.fromisoformat(now),
                    usage_count=row['usage_count'] + 1
                )

            # Execute with automatic retry on deadlock
            return self.db_manager.execute_with_retry(validate_operation, immediate=True)

        except Exception as e:
            self.logger.error(f"Error validating API key: {e}")
            return None
    
    def get_user_by_identifier(self, identifier: str) -> Optional[SimpleUser]:
        """Get user by identifier using thread-safe operations."""
        try:
            rows = self.db_manager.execute_query("""
                SELECT * FROM users WHERE identifier = ?
            """, (identifier,))

            if rows:
                row = rows[0]
                return SimpleUser(
                    id=row['id'],
                    identifier=row['identifier'],
                    name=row['name'],
                    created_at=datetime.fromisoformat(row['created_at']),
                    api_key=row['api_key'],
                    last_used=datetime.fromisoformat(row['last_used']) if row['last_used'] else None,
                    usage_count=row['usage_count']
                )

            return None

        except Exception as e:
            self.logger.error(f"Error getting user: {e}")
            return None
    
    def get_all_users(self) -> list[SimpleUser]:
        """Get all users for admin purposes using thread-safe operations."""
        try:
            rows = self.db_manager.execute_query("SELECT * FROM users ORDER BY created_at DESC")

            users = []
            for row in rows:
                users.append(SimpleUser(
                    id=row['id'],
                    identifier=row['identifier'],
                    name=row['name'],
                    created_at=datetime.fromisoformat(row['created_at']),
                    api_key=row['api_key'],
                    last_used=datetime.fromisoformat(row['last_used']) if row['last_used'] else None,
                    usage_count=row['usage_count']
                ))

            return users

        except Exception as e:
            self.logger.error(f"Error getting all users: {e}")
            return []
    
    def get_stats(self) -> Dict[str, Any]:
        """Get simple statistics using thread-safe operations."""
        try:
            rows = self.db_manager.execute_query("""
                SELECT
                    COUNT(*) as total_users,
                    SUM(usage_count) as total_requests,
                    COUNT(CASE WHEN last_used IS NOT NULL THEN 1 END) as active_users
                FROM users
            """)

            if rows:
                row = rows[0]
                stats = {
                    "total_users": row[0] if row[0] else 0,
                    "total_requests": row[1] if row[1] else 0,
                    "active_users": row[2] if row[2] else 0,
                    "timestamp": datetime.utcnow().isoformat()
                }
            else:
                stats = {
                    "total_users": 0,
                    "total_requests": 0,
                    "active_users": 0,
                    "timestamp": datetime.utcnow().isoformat()
                }

            # Add database performance stats
            db_stats = self.db_manager.get_stats()
            stats["database_performance"] = {
                "pool_stats": db_stats.get("pool_stats", {}),
                "wal_mode_enabled": db_stats.get("config", {}).get("wal_mode_enabled", False)
            }

            return stats

        except Exception as e:
            self.logger.error(f"Error getting stats: {e}")
            return {
                "total_users": 0,
                "total_requests": 0,
                "active_users": 0,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    def regenerate_api_key(self, identifier: str) -> Optional[str]:
        """Atomically regenerate API key for a user."""
        try:
            new_api_key = f"mcp_{secrets.token_hex(16)}"

            def regenerate_operation(conn):
                cursor = conn.execute("""
                    UPDATE users SET api_key = ? WHERE identifier = ?
                """, (new_api_key, identifier))

                if cursor.rowcount > 0:
                    return new_api_key
                return None

            # Execute with automatic retry on deadlock
            result = self.db_manager.execute_with_retry(regenerate_operation, immediate=True)

            if result:
                self.logger.info(f"Regenerated API key for: {identifier}")
                return result

            return None

        except Exception as e:
            self.logger.error(f"Error regenerating API key: {e}")
            return None


# Global instance for easy access
_simple_auth_manager: Optional[SimpleApiKeyManager] = None


def get_simple_auth_manager(db_path: str = "simple_keys.db") -> SimpleApiKeyManager:
    """Get or create the global simple auth manager."""
    global _simple_auth_manager
    if _simple_auth_manager is None:
        _simple_auth_manager = SimpleApiKeyManager(db_path)
    return _simple_auth_manager


def validate_connection_key(api_key: str, db_path: str = "simple_keys.db") -> bool:
    """
    Simple validation function for connection identification.

    Args:
        api_key: API key to validate
        db_path: Database path (for testing)

    Returns:
        True if valid connection, False otherwise
    """
    if not api_key or not api_key.startswith("mcp_"):
        return False

    auth_manager = get_simple_auth_manager(db_path)
    user = auth_manager.validate_api_key(api_key)
    return user is not None


def get_user_id_from_key(api_key: str, db_path: str = "simple_keys.db") -> Optional[str]:
    """
    Get user ID from API key.

    Args:
        api_key: API key to look up
        db_path: Database path (for testing)

    Returns:
        User ID if valid, None otherwise
    """
    if not api_key or not api_key.startswith("mcp_"):
        return None

    auth_manager = get_simple_auth_manager(db_path)
    user = auth_manager.validate_api_key(api_key)
    return user.id if user else None


def extract_user_id_from_api_key(api_key: str) -> Optional[str]:
    """
    Centralized, secure user ID extraction from mcp_ API keys.

    This is the single source of truth for converting API keys to user IDs
    across the entire system. Only supports mcp_ prefixed keys.

    Args:
        api_key: API key (must be mcp_ prefixed)

    Returns:
        User ID if valid, None if invalid/unauthorized
    """
    if not api_key or not isinstance(api_key, str):
        return None

    # Only handle mcp_ prefixed keys - single source of truth
    if api_key.startswith("mcp_"):
        try:
            auth_manager = get_simple_auth_manager()
            user = auth_manager.validate_api_key(api_key)
            return user.id if user else None
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to validate mcp_ key: {e}")
            return None

    # Reject all other key formats - mcp_ is the only valid format
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"Invalid API key format - only mcp_ keys supported: {api_key[:8]}...")
    return None

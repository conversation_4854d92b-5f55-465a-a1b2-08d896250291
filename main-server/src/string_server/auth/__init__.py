"""
Authentication Module

Provides authentication and user management functionality for the MCP server.
Uses mcp_ prefixed API keys as the single source of truth for all authentication.

This module provides:
- Simple auth for VSCode extension connection identification
- User management and validation
- Centralized API key validation (mcp_ keys only)
"""

# Import from simple auth manager
from .simple_auth_manager import (
    <PERSON><PERSON><PERSON>,
    SimpleApiKeyManager,
    get_simple_auth_manager,
    validate_connection_key,
    get_user_id_from_key,
    extract_user_id_from_api_key
)

# Note: Removed key_manager imports - using only mcp_ keys as single source of truth
KEY_MANAGER_AVAILABLE = False

__all__ = [
    # Simple auth exports - mcp_ keys only
    'SimpleUser',
    'SimpleApiKeyManager',
    'get_simple_auth_manager',
    'validate_connection_key',
    'get_user_id_from_key',
    'extract_user_id_from_api_key',
    'KEY_MANAGER_AVAILABLE',
]


def get_auth_system_info():
    """Get information about available authentication systems."""
    return {
        'simple_auth_available': True,
        'key_manager_available': K<PERSON>Y_MANAGER_AVAILABLE,
        'api_key_format': 'mcp_',
        'single_source_of_truth': 'mcp_ keys only',
        'recommended_for_all': 'simple_auth with mcp_ keys'
    }

"""
Entry point for the MCP server package.

This module provides the proper entry point for running the MCP server
as a Python package, ensuring correct import resolution and Railway compatibility.

Usage:
    python -m src                    # Run from project root
    python src                       # Direct execution
    python -m src.server            # Explicit server module
"""

import sys
import os
import logging

def setup_python_path():
    """Setup Python path for proper module resolution."""
    # Get the directory containing this file (src/)
    src_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Get the parent directory (project root)
    project_root = os.path.dirname(src_dir)
    
    # Add both to Python path if not already present
    for path in [project_root, src_dir]:
        if path not in sys.path:
            sys.path.insert(0, path)

def main():
    """Main entry point with proper error handling and Railway compatibility."""
    # Setup logging early
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    logger = logging.getLogger(__name__)
    
    # Setup Python path
    setup_python_path()
    
    try:
        # Try package-style import first (best practice)
        logger.info("Attempting package-style import...")
        from .server import main as server_main
        logger.info("✅ Package-style import successful")
        server_main()
        
    except ImportError as e:
        logger.warning(f"Package import failed: {e}")
        logger.info("Attempting fallback import...")
        
        try:
            # Fallback for direct execution scenarios
            from server import main as server_main
            logger.info("✅ Fallback import successful")
            server_main()
            
        except ImportError as fallback_error:
            logger.error(f"❌ Fallback import also failed: {fallback_error}")
            logger.error("Unable to start MCP server - check Python path and dependencies")
            
            # Additional debugging information
            logger.error(f"Current working directory: {os.getcwd()}")
            logger.error(f"Python path: {sys.path[:3]}...")  # Show first 3 entries
            logger.error(f"__file__: {__file__}")
            logger.error(f"src directory: {os.path.dirname(os.path.abspath(__file__))}")
            
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"❌ Failed to start MCP server: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        sys.exit(1)

if __name__ == "__main__":
    main()

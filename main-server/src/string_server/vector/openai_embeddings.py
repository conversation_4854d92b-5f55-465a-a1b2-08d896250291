"""
OpenAI embedding service for code chunks using OpenAI's text-embedding models.
Provides text embedding functionality for vector storage using OpenAI API.
"""

import os
import logging
from typing import List, Optional, Dict, Any
from openai import OpenAI

logger = logging.getLogger(__name__)


class OpenAIEmbeddingService:
    """
    Service for generating embeddings using OpenAI's embedding models.
    Optimized for code and technical content.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        model_name: str = "text-embedding-3-small"
    ):
        """
        Initialize OpenAI embedding service with specified model.
        
        Args:
            api_key: OpenAI API key (if None, will use environment variable)
            model_name: Name of the OpenAI embedding model to use
        """
        self.model_name = model_name
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        
        if not self.api_key:
            raise ValueError(
                "OpenAI API key is required. Set 'OPENAI_API_KEY' environment "
                "variable or pass api_key parameter."
            )
        
        # Initialize OpenAI client
        self.client = OpenAI(api_key=self.api_key)
        
        # Model dimensions mapping
        self.model_dimensions = {
            "text-embedding-3-small": 1536,
            "text-embedding-3-large": 3072
        }
        
        logger.info(
            f"Initialized OpenAI embedding service with model: "
            f"{self.model_name}"
        )

    def embed_text(self, text: str) -> List[float]:
        """
        Generate embedding for a single text using OpenAI API.
        
        Args:
            text: Text to embed
            
        Returns:
            Vector embedding as list of floats
        """
        try:
            response = self.client.embeddings.create(
                model=self.model_name,
                input=text,
                encoding_format="float"
            )
            
            embedding = response.data[0].embedding
            return embedding
            
        except Exception as e:
            logger.error(f"Failed to generate OpenAI embedding for text: {e}")
            raise

    def embed_batch(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for multiple texts efficiently using OpenAI API.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of vector embeddings
        """
        try:
            response = self.client.embeddings.create(
                model=self.model_name,
                input=texts,
                encoding_format="float"
            )
            
            embeddings = [data.embedding for data in response.data]
            return embeddings
            
        except Exception as e:
            logger.error(f"Failed to generate OpenAI batch embeddings: {e}")
            raise

    def get_vector_size(self) -> int:
        """
        Get the dimension size of the embedding vectors.
        
        Returns:
            Vector dimension size
        """
        return self.model_dimensions.get(self.model_name, 1536)

    def preprocess_code(self, code: str, language: str = "python") -> str:
        """
        Preprocess code text for better embedding quality.
        
        Args:
            code: Raw code text
            language: Programming language
            
        Returns:
            Preprocessed code text
        """
        # Remove excessive whitespace
        lines = [line.rstrip() for line in code.split('\n')]
        
        # Remove empty lines at start and end
        while lines and not lines[0].strip():
            lines.pop(0)
        while lines and not lines[-1].strip():
            lines.pop()
        
        # Join with single newlines
        processed = '\n'.join(lines)
        
        # Add language context for better embeddings
        if language:
            processed = f"# {language.upper()} CODE\n{processed}"
        
        return processed

    def embed_code_chunk(
        self, code: str, metadata: Dict[str, Any]
    ) -> List[float]:
        """
        Generate embedding for a code chunk with metadata context using OpenAI.
        
        Args:
            code: Code content
            metadata: Chunk metadata (function name, class, etc.)
            
        Returns:
            Vector embedding
        """
        # Build context-rich text for embedding
        context_parts = []
        
        # Add language context
        if metadata.get('language'):
            context_parts.append(f"Language: {metadata['language']}")
        
        # Add function/class context
        if metadata.get('function_name'):
            context_parts.append(f"Function: {metadata['function_name']}")
        if metadata.get('class_name'):
            context_parts.append(f"Class: {metadata['class_name']}")
        
        # Add file context
        if metadata.get('file_path'):
            context_parts.append(f"File: {metadata['file_path']}")
        
        # Add imports context
        if metadata.get('imports'):
            imports_str = ', '.join(metadata['imports'][:5])  # Limit imports
            context_parts.append(f"Imports: {imports_str}")
        
        # Combine context and code
        context = ' | '.join(context_parts)
        processed_code = self.preprocess_code(
            code, 
            metadata.get('language', 'python')
        )
        
        # Create embedding text
        embedding_text = f"{context}\n\n{processed_code}"
        
        return self.embed_text(embedding_text)


# Global OpenAI embedding service instance
_openai_embedding_service: Optional[OpenAIEmbeddingService] = None


def get_openai_embedding_service(
    api_key: Optional[str] = None, 
    model_name: str = "text-embedding-3-small"
) -> OpenAIEmbeddingService:
    """
    Get or create global OpenAI embedding service instance.
    
    Args:
        api_key: OpenAI API key
        model_name: Model name to use
        
    Returns:
        OpenAIEmbeddingService instance
    """
    global _openai_embedding_service
    
    if (_openai_embedding_service is None or
            _openai_embedding_service.model_name != model_name):
        _openai_embedding_service = OpenAIEmbeddingService(api_key, model_name)
    
    return _openai_embedding_service
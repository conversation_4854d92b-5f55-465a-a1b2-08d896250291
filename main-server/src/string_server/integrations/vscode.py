"""
VS Code Extension Integration for MCP Server

This module adds HTTP endpoints to receive code chunks from the VS Code extension
and integrates them with the existing MCP server's indexing capabilities.
"""

import asyncio
import json
import logging
import os
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any, List, Set

from pydantic import BaseModel, Field
from starlette.requests import Request
from starlette.responses import JSONResponse

# Robust import handling for job completion
try:
    from ..processing.job_completion import (
        get_job_manager, JobType, JobStatus
    )
    JOB_COMPLETION_AVAILABLE = True
except ImportError:
    try:
        from processing.job_completion import (
            get_job_manager, JobType, JobStatus
        )
        JOB_COMPLETION_AVAILABLE = True
    except ImportError:
        # Graceful fallback - create dummy classes/functions
        JOB_COMPLETION_AVAILABLE = False

        class JobType:
            VECTOR_PROCESSING = "vector_processing"

        class JobStatus:
            PENDING = "pending"
            PROCESSING = "processing"
            COMPLETED = "completed"
            FAILED = "failed"

        def get_job_manager():
            """Dummy job manager for graceful degradation."""
            return None

logger = logging.getLogger(__name__)


def create_api_response(data: dict, success: bool = True, status_code: int = 200, request_id: str = None) -> JSONResponse:
    """Create standardized API response format."""
    import uuid
    if request_id is None:
        request_id = str(uuid.uuid4())

    if success:
        response = {
            "success": True,
            "data": data,
            "meta": {
                "timestamp": datetime.utcnow().isoformat(),
                "version": "v1",
                "request_id": request_id
            }
        }
    else:
        response = {
            "success": False,
            "error": data,
            "meta": {
                "timestamp": datetime.utcnow().isoformat(),
                "version": "v1",
                "request_id": request_id
            }
        }

    # Standard CORS headers
    headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers": "*",
    }

    return JSONResponse(response, status_code=status_code, headers=headers)


# Import Hybrid Cache System for performance optimization
try:
    from ..cache import (
        get_hybrid_cache,
        HybridCacheSystem,
        CacheResult,
        JobStatus
    )
    # Note: initialize_cache_system is available but not needed here
    HYBRID_CACHE_AVAILABLE = True
    logger.info("🚀 Hybrid cache system available - Redis + Server caching enabled")
except ImportError:
    try:
        from cache import (
            get_hybrid_cache,
            HybridCacheSystem,
            CacheResult,
            JobStatus
        )
        HYBRID_CACHE_AVAILABLE = True
        logger.info("🚀 Hybrid cache system available - Redis + Server caching enabled")
    except ImportError as e:
        HYBRID_CACHE_AVAILABLE = False
        logger.warning(f"Hybrid cache not available - using fallback processing. Import error: {e}")
        logger.info("To enable Redis caching, ensure 'redis>=5.0.0' is installed and Redis connection variables are set")

        # Create dummy classes for graceful degradation
        class HybridCacheSystem:
            def __init__(self):
                self.initialized = False

        class CacheResult:
            def __init__(self, hit=False, data=None):
                self.hit = hit
                self.data = data

        def get_hybrid_cache():
            """Dummy cache for graceful degradation."""
            return HybridCacheSystem()


# Import the new metadata extraction system
try:
    from ..metadata_extraction import extract_chunk_metadata, get_metadata_extractor_factory
    METADATA_EXTRACTION_AVAILABLE = True
    logger.info("Advanced metadata extraction system available")
except ImportError:
    try:
        from metadata_extraction import extract_chunk_metadata, get_metadata_extractor_factory
        METADATA_EXTRACTION_AVAILABLE = True
        logger.info("Advanced metadata extraction system available")
    except ImportError:
        METADATA_EXTRACTION_AVAILABLE = False
        logger.warning("Advanced metadata extraction not available - using basic extraction")

        # Create dummy functions for graceful degradation
        def extract_chunk_metadata(content, file_path=""):
            """Dummy metadata extraction for graceful degradation."""
            return {}

        def get_metadata_extractor_factory():
            """Dummy factory for graceful degradation."""
            return None


class CodeChunk(BaseModel):
    """Code chunk model for VS Code extension integration."""
    path: str = Field(..., description="Absolute path to the source file")
    idx: int = Field(..., description="Chunk index within the file")
    content: str = Field(..., description="Code content of the chunk")


class EnhancedFileChunkManager:
    """Enhanced manager for code chunks with deduplication and validation."""

    def __init__(self):
        self.file_chunks: Dict[str, List[Dict[str, Any]]] = {}
        self.temp_files: Dict[str, str] = {}  # Maps original path to temp file path
        self.chunk_hashes: Dict[str, Set[str]] = {}  # Track chunk hashes per file
        self.processing_stats = {
            "chunks_received": 0,
            "chunks_deduplicated": 0,
            "chunks_validated": 0,
            "files_processed": 0,
            "validation_errors": 0
        }

    def add_chunk(self, file_path: str, chunk_idx: int, content: str, metadata: Dict[str, Any] = None) -> bool:
        """
        Add a chunk for a file with deduplication and validation.

        Returns:
            True if chunk was added, False if it was a duplicate
        """
        if file_path not in self.file_chunks:
            self.file_chunks[file_path] = []
            self.chunk_hashes[file_path] = set()

        # Generate chunk hash for deduplication
        chunk_hash = self._generate_chunk_hash(file_path, chunk_idx, content)

        # Check for duplicates
        if chunk_hash in self.chunk_hashes[file_path]:
            logger.debug(f"Duplicate chunk detected for {file_path}:{chunk_idx}")
            self.processing_stats["chunks_deduplicated"] += 1
            return False

        # Validate chunk content
        validation_result = self._validate_chunk(content, metadata or {})
        if not validation_result["is_valid"]:
            logger.warning(f"Invalid chunk for {file_path}:{chunk_idx}: {validation_result['errors']}")
            self.processing_stats["validation_errors"] += 1
            # Still add it but mark as invalid

        chunk_data = {
            "idx": chunk_idx,
            "content": content,
            "timestamp": datetime.utcnow().isoformat(),
            "hash": chunk_hash,
            "metadata": metadata or {},
            "validation": validation_result
        }

        self.file_chunks[file_path].append(chunk_data)
        self.chunk_hashes[file_path].add(chunk_hash)

        # Sort chunks by index
        self.file_chunks[file_path].sort(key=lambda x: x["idx"])

        self.processing_stats["chunks_received"] += 1
        if validation_result["is_valid"]:
            self.processing_stats["chunks_validated"] += 1

        return True

    def _generate_chunk_hash(self, file_path: str, chunk_idx: int, content: str) -> str:
        """Generate a hash for chunk deduplication."""
        import hashlib
        hash_input = f"{file_path}:{chunk_idx}:{content}"
        return hashlib.md5(hash_input.encode()).hexdigest()

    def _validate_chunk(self, content: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Validate chunk content and metadata."""
        errors = []
        warnings = []

        # Basic content validation
        if not content or not content.strip():
            errors.append("Empty chunk content")

        if len(content) > 50000:  # 50KB limit
            errors.append("Chunk too large")

        # Check for encoding issues
        try:
            content.encode('utf-8')
        except UnicodeEncodeError:
            errors.append("Invalid UTF-8 encoding")

        # Validate metadata if present
        if metadata:
            if 'lineCount' in metadata and metadata['lineCount'] <= 0:
                warnings.append("Invalid line count in metadata")

            if 'hasCode' in metadata and not metadata['hasCode'] and len(content.strip()) > 100:
                warnings.append("Large content marked as non-code")

        return {
            "is_valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "content_length": len(content),
            "line_count": len(content.split('\n'))
        }
    
    def reconstruct_file(self, file_path: str) -> Optional[str]:
        """Reconstruct complete file content from chunks."""
        if file_path not in self.file_chunks:
            return None
        
        chunks = self.file_chunks[file_path]
        if not chunks:
            return None
        
        # Combine all chunk contents
        full_content = "".join(chunk["content"] for chunk in chunks)
        
        # Create temporary file
        temp_dir = tempfile.gettempdir()
        file_name = Path(file_path).name
        temp_file_path = os.path.join(temp_dir, f"vscode_chunk_{file_name}")
        
        try:
            with open(temp_file_path, 'w', encoding='utf-8') as f:
                f.write(full_content)
            
            self.temp_files[file_path] = temp_file_path
            return temp_file_path
        except Exception as e:
            logger.error(f"Failed to create temp file for {file_path}: {e}")
            return None
    
    def cleanup_temp_files(self) -> None:
        """Clean up temporary files."""
        for temp_path in self.temp_files.values():
            try:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
            except Exception as e:
                logger.warning(f"Failed to cleanup temp file {temp_path}: {e}")
        self.temp_files.clear()


class VSCodeIntegration:
    """Integration handler for VS Code extension with Hybrid Cache optimization."""

    def __init__(self, mcp_server, auth_manager=None):
        """
        Initialize VS Code integration with Hybrid Cache system.

        Args:
            mcp_server: The FastMCP server instance
            auth_manager: Optional authentication manager
        """
        self.mcp_server = mcp_server
        self.auth_manager = auth_manager
        self.chunk_manager = EnhancedFileChunkManager()

        # Hybrid cache system (Redis + Server)
        self.hybrid_cache: Optional[HybridCacheSystem] = None
        self.cache_initialized = False

        self.vector_processing_stats = {
            "embeddings_generated": 0,
            "chunks_stored": 0,
            "storage_errors": 0,
            "processing_time_ms": 0,
            "deduplication_saves": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "api_calls_saved": 0,
            "redis_coordination": 0,
            "server_storage": 0
        }

        # Initialize metadata extraction factory
        if METADATA_EXTRACTION_AVAILABLE:
            self.metadata_factory = get_metadata_extractor_factory()
            logger.info("Metadata extraction factory initialized")
        else:
            self.metadata_factory = None

    async def initialize_hybrid_cache(self):
        """Initialize Hybrid Cache system for performance optimization."""
        if not HYBRID_CACHE_AVAILABLE:
            logger.info("Hybrid cache not available - using fallback processing")
            return

        try:
            # Initialize hybrid cache system
            self.hybrid_cache = await get_hybrid_cache()

            if self.hybrid_cache.initialized:
                self.cache_initialized = True
                logger.info("🚀 Hybrid cache initialized successfully for VSCode integration")

                # Log cache statistics
                cache_stats = await self.hybrid_cache.get_comprehensive_stats()
                logger.info(f"Cache stats: Redis ops: {cache_stats['redis_operations']}, "
                           f"Server ops: {cache_stats['server_operations']}")

                # Health check
                health = await self.hybrid_cache.health_check()
                logger.info(f"Cache health: {health['hybrid_system']} "
                           f"(Redis: {health['redis_coordination']}, Server: {health['server_cache']})")
            else:
                logger.warning("Hybrid cache initialization failed - using fallback processing")

        except Exception as e:
            logger.error(f"Failed to initialize hybrid cache: {e}")
            self.cache_initialized = False

    def extract_chunk_metadata(self, content: str, file_path: str = "") -> Dict[str, Any]:
        """
        Extract metadata from a code chunk using the pluggable extraction system.

        Args:
            content: Code content to analyze
            file_path: File path for language detection

        Returns:
            Dictionary with extracted metadata
        """
        if METADATA_EXTRACTION_AVAILABLE and self.metadata_factory:
            try:
                # Use the advanced metadata extraction system
                result = self.metadata_factory.extract_metadata(content, file_path)

                # Convert to dictionary format expected by vector storage
                metadata = result.to_dict()

                logger.debug(f"Extracted metadata for {file_path}: {result.chunk_type} "
                           f"({result.function_name or result.class_name or 'no primary identifier'})")

                return metadata

            except Exception as e:
                logger.warning(f"Advanced metadata extraction failed: {e}, using fallback")

        # Fallback to basic metadata extraction
        return self._basic_metadata_extraction(content, file_path)

    def _basic_metadata_extraction(self, content: str, file_path: str = "") -> Dict[str, Any]:
        """
        Basic metadata extraction fallback.

        Args:
            content: Code content
            file_path: File path

        Returns:
            Basic metadata dictionary
        """
        metadata = {
            'function_name': '',
            'class_name': '',
            'functions_found': [],
            'classes_found': [],
            'chunk_type': 'module',
            'language': 'unknown',
            'has_code': bool(content.strip()),
            'line_count': len(content.split('\n')) if content else 0,
            'complexity_score': 0,
            'extraction_method': 'basic',
            'confidence': 0.5
        }

        # Basic language detection from file extension
        if file_path:
            if file_path.endswith('.py'):
                metadata['language'] = 'python'
            elif file_path.endswith(('.js', '.jsx')):
                metadata['language'] = 'javascript'
            elif file_path.endswith(('.ts', '.tsx')):
                metadata['language'] = 'typescript'

        # Basic complexity calculation
        if content:
            metadata['complexity_score'] = content.count('{') + content.count('(') + content.count('[')

        return metadata

    def register_endpoints(self):
        """Register VS Code integration endpoints with the MCP server."""

        @self.mcp_server.custom_route("/api/v1/vscode/chunks", methods=["POST"])
        async def receive_code_chunk(request: Request) -> JSONResponse:
            """
            Receive code chunks from VS Code extension.

            This endpoint receives code chunks and processes them using
            the existing MCP server's indexing capabilities with job tracking.
            """
            try:
                # Parse request body with enhanced validation
                body = await request.body()
                chunk_data = json.loads(body)

                # Handle both old format (path, idx, content) and new extension format
                # New extension format: {job_type, user_id, metadata: {file_path, chunk_index}, content, chunk_metadata: {index}}

                # Extract fields from either format
                if 'metadata' in chunk_data and 'file_path' in chunk_data['metadata']:
                    # New extension format
                    file_path = chunk_data['metadata']['file_path']
                    chunk_index = chunk_data['metadata'].get('chunk_index')
                    if chunk_index is None and 'chunk_metadata' in chunk_data:
                        chunk_index = chunk_data['chunk_metadata'].get('index')
                    content = chunk_data.get('content', '')

                    # Extract webhook URL from metadata if present
                    webhook_url = chunk_data['metadata'].get('webhook_url')

                    logger.info(f"Processing new extension format: {file_path}, chunk {chunk_index}")

                else:
                    # Old format (backward compatibility)
                    file_path = chunk_data.get('path')
                    chunk_index = chunk_data.get('idx')
                    content = chunk_data.get('content', '')
                    webhook_url = chunk_data.get('webhook_url')

                    logger.info(f"Processing legacy format: {file_path}, chunk {chunk_index}")

                # Validate required fields
                if not file_path:
                    error_data = {
                        "code": "MISSING_FILE_PATH",
                        "message": "Missing required field: file_path (in metadata.file_path or path)",
                        "details": {"required_fields": ["file_path"]}
                    }
                    return create_api_response(error_data, success=False, status_code=400)

                if chunk_index is None:
                    error_data = {
                        "code": "MISSING_CHUNK_INDEX",
                        "message": "Missing required field: chunk_index (in metadata.chunk_index, chunk_metadata.index, or idx)",
                        "details": {"required_fields": ["chunk_index"]}
                    }
                    return create_api_response(error_data, success=False, status_code=400)

                if not content:
                    error_data = {
                        "code": "MISSING_CONTENT",
                        "message": "Missing required field: content",
                        "details": {"required_fields": ["content"]}
                    }
                    return create_api_response(error_data, success=False, status_code=400)

                # Validate authentication for job creation
                user_id = await self._get_authenticated_user_id(request)
                if not user_id:
                    error_data = {
                        "code": "AUTHENTICATION_REQUIRED",
                        "message": "Authentication required for job tracking",
                        "details": {"auth_header": "Bearer token required"}
                    }
                    return create_api_response(error_data, success=False, status_code=401)

                # Validate field types
                if not isinstance(chunk_index, int):
                    error_data = {
                        "code": "INVALID_INDEX",
                        "message": "Field 'chunk_index' must be an integer",
                        "details": {"received_type": type(chunk_index).__name__}
                    }
                    return create_api_response(error_data, success=False, status_code=400)

                if not isinstance(file_path, str):
                    error_data = {
                        "code": "INVALID_PATH",
                        "message": "Field 'file_path' must be a string",
                        "details": {"received_type": type(file_path).__name__}
                    }
                    return create_api_response(error_data, success=False, status_code=400)

                if not isinstance(content, str):
                    error_data = {
                        "code": "INVALID_CONTENT",
                        "message": "Field 'content' must be a string",
                        "details": {"received_type": type(content).__name__}
                    }
                    return create_api_response(error_data, success=False, status_code=400)

                # Validate chunk size (matching extension's 10KB limit)
                if len(content) > 10000:
                    error_data = {
                        "code": "CHUNK_TOO_LARGE",
                        "message": "Chunk exceeds maximum size limit (10KB)",
                        "details": {"content_size": len(content), "max_size": 10000}
                    }
                    return create_api_response(error_data, success=False, status_code=413)

                # Extract metadata if present (matching real extension format)
                # For new format, metadata is already extracted above
                # For old format, get it from chunk_data
                if 'metadata' in chunk_data and 'file_path' in chunk_data['metadata']:
                    # New extension format - metadata is the main metadata object
                    metadata = chunk_data.get('metadata', {})
                    # Also include chunk_metadata if present
                    chunk_metadata = chunk_data.get('chunk_metadata', {})
                    if chunk_metadata:
                        metadata.update(chunk_metadata)
                else:
                    # Old format - metadata might be separate
                    metadata = chunk_data.get('metadata', {})

                # Real extension sends enhanced metadata structure:
                # {
                #   "file_path": string,
                #   "chunk_index": number,
                #   "content_length": number,
                #   "hash": string,
                #   "timestamp": ISO string,
                #   "source": "vscode-extension",
                #   "extension_version": string,
                #   "workspace_id": string,
                #   "job_id": string
                # }

                # Create chunk object using extracted fields
                chunk = CodeChunk(
                    path=file_path,
                    idx=chunk_index,
                    content=content
                )

                logger.info(f"Received chunk {chunk.idx} from {chunk.path}")
                logger.info(f"Content length: {len(chunk.content)} characters")

                # Extract function and class metadata from the chunk content
                extracted_metadata = self.extract_chunk_metadata(content, file_path)

                # Log extracted metadata for debugging
                if extracted_metadata.get('function_name') or extracted_metadata.get('class_name'):
                    logger.info(f"Extracted metadata - Function: {extracted_metadata.get('function_name', 'None')}, "
                              f"Class: {extracted_metadata.get('class_name', 'None')}, "
                              f"Type: {extracted_metadata.get('chunk_type', 'unknown')}")

                # Merge extracted metadata with existing metadata
                if metadata:
                    logger.debug(f"Original metadata: {metadata}")
                    # Update metadata with extracted information
                    metadata.update(extracted_metadata)
                else:
                    metadata = extracted_metadata

                # Create job for tracking vector store processing
                job_manager = get_job_manager()
                job_metadata = {
                    "file_path": file_path,
                    "chunk_index": chunk_index,
                    "content_length": len(content),
                    "webhook_url": webhook_url,
                    "source": "vscode-extension"
                }

                job_id = job_manager.create_job(
                    job_type=JobType.CHUNK_PROCESSING,
                    user_id=user_id,
                    metadata=job_metadata,
                    timeout_seconds=300  # 5 minutes timeout
                )

                # Start the job
                job_manager.start_job(job_id, total_steps=4)  # Parse, validate, embed, store

                # Register webhook callback if provided
                if webhook_url:
                    await self._register_webhook_callback(job_id, webhook_url)

                # Enhanced metadata with job tracking and user context
                enhanced_metadata = metadata.copy() if metadata else {}
                enhanced_metadata.update({
                    "job_id": job_id,
                    "user_id": user_id,  # Pass authenticated user ID
                    "timestamp": datetime.utcnow().isoformat(),
                    "source": "vscode-extension"
                })

                # Process the chunk with enhanced validation and job tracking
                result = await self._process_chunk_with_job_tracking(chunk, enhanced_metadata, job_id)

                # Return response in exact format expected by real extension
                response_data = {
                    "chunk_id": result.get("chunk_id"),  # Extension expects snake_case
                    "processing_time_ms": result.get("processing_time_ms"),
                    "job_id": job_id  # Return job ID for status tracking
                }
                return create_api_response(response_data, status_code=201)

            except json.JSONDecodeError:
                logger.error("Invalid JSON in request body")
                error_data = {
                    "code": "INVALID_JSON",
                    "message": "Request body must be valid JSON",
                    "details": {}
                }
                return create_api_response(error_data, success=False, status_code=400)
            except Exception as e:
                logger.error(f"Error processing chunk: {e}")
                error_data = {
                    "code": "CHUNK_PROCESSING_ERROR",
                    "message": "An error occurred while processing the chunk",
                    "details": {"error": str(e)}
                }
                return create_api_response(error_data, success=False, status_code=500)

        @self.mcp_server.custom_route("/api/v1/vscode/status", methods=["GET"])
        async def indexing_status(request: Request) -> JSONResponse:
            """Get indexing status and statistics."""
            try:
                # Initialize hybrid cache if not already done
                if not self.cache_initialized:
                    await self.initialize_hybrid_cache()

                status_data = {
                    "service": "vscode-mcp-integration",
                    "status": "healthy",
                    "chunk_processing_stats": self.chunk_manager.processing_stats,
                    "vector_processing_stats": self.vector_processing_stats,
                    "active_files": len(self.chunk_manager.file_chunks),
                    "temp_files": len(self.chunk_manager.temp_files),
                    "total_chunk_hashes": sum(len(hashes) for hashes in self.chunk_manager.chunk_hashes.values()),
                    "hybrid_cache": {
                        "enabled": HYBRID_CACHE_AVAILABLE,
                        "initialized": self.cache_initialized,
                        "system_health": "unknown"
                    },
                    "endpoints": {
                        "POST /api/v1/vscode/chunks": "Receive code chunks from VS Code",
                        "GET /api/v1/vscode/status": "Get indexing status",
                        "POST /api/v1/vscode/cleanup": "Clean temporary files"
                    }
                }

                # Add hybrid cache statistics if available
                if self.cache_initialized and self.hybrid_cache:
                    try:
                        cache_stats = await self.hybrid_cache.get_comprehensive_stats()
                        health = await self.hybrid_cache.health_check()

                        status_data["hybrid_cache"].update({
                            "system_health": health["hybrid_system"],
                            "redis_coordination": health["redis_coordination"],
                            "server_cache": health["server_cache"],
                            "stats": {
                                "redis_operations": cache_stats["redis_operations"],
                                "server_operations": cache_stats["server_operations"],
                                "hybrid_operations": cache_stats["hybrid_operations"],
                                "fallback_operations": cache_stats["fallback_operations"],
                                "performance": cache_stats["hybrid_performance"]
                            }
                        })

                    except Exception as e:
                        status_data["hybrid_cache"]["stats_error"] = str(e)

                return create_api_response(status_data)

            except Exception as e:
                logger.error(f"Error getting status: {e}")
                error_data = {
                    "code": "STATUS_ERROR",
                    "message": "An error occurred while retrieving status",
                    "details": {"error": str(e)}
                }
                return create_api_response(error_data, success=False, status_code=500)

        @self.mcp_server.custom_route("/api/v1/vscode/cleanup", methods=["POST"])
        async def cleanup_temp_files(request: Request) -> JSONResponse:
            """Clean up temporary files created during processing."""
            try:
                files_cleaned = len(self.chunk_manager.temp_files)
                self.chunk_manager.cleanup_temp_files()

                response_data = {
                    "status": "completed",
                    "message": f"Cleaned up {files_cleaned} temporary files",
                    "files_cleaned": files_cleaned
                }
                return create_api_response(response_data)

            except Exception as e:
                logger.error(f"Error during cleanup: {e}")
                error_data = {
                    "code": "CLEANUP_ERROR",
                    "message": "An error occurred during cleanup",
                    "details": {"error": str(e)}
                }
                return create_api_response(error_data, success=False, status_code=500)

        # Job completion and tracking endpoints
        @self.mcp_server.custom_route("/api/v1/jobs/{job_id}", methods=["GET"])
        async def get_job_status(request: Request) -> JSONResponse:
            """Get status of a specific job."""
            try:
                job_id = request.path_params["job_id"]

                # Validate authentication
                user_id = await self._get_authenticated_user_id(request)
                if not user_id:
                    error_data = {
                        "code": "AUTHENTICATION_REQUIRED",
                        "message": "Authentication required",
                        "details": {"auth_header": "Bearer token required"}
                    }
                    return create_api_response(error_data, success=False, status_code=401)

                job_manager = get_job_manager()
                job = job_manager.get_job(job_id)

                if not job:
                    error_data = {
                        "code": "JOB_NOT_FOUND",
                        "message": f"Job not found: {job_id}",
                        "details": {"job_id": job_id}
                    }
                    return create_api_response(error_data, success=False, status_code=404)

                # Check if user owns this job
                if job.user_id != user_id:
                    error_data = {
                        "code": "ACCESS_DENIED",
                        "message": "Access denied to this job",
                        "details": {"job_id": job_id}
                    }
                    return create_api_response(error_data, success=False, status_code=403)

                response_data = {
                    "job": {
                        "job_id": job.job_id,
                        "job_type": job.job_type.value,
                        "status": job.status.value,
                        "created_at": job.created_at.isoformat(),
                        "started_at": job.started_at.isoformat() if job.started_at else None,
                        "completed_at": job.completed_at.isoformat() if job.completed_at else None,
                        "duration_seconds": job.duration_seconds,
                        "progress": {
                            "current_step": job.progress.current_step,
                            "total_steps": job.progress.total_steps,
                            "percentage": job.progress.percentage,
                            "current_operation": job.progress.current_operation,
                            "estimated_completion": job.progress.estimated_completion.isoformat() if job.progress.estimated_completion else None
                        },
                        "result": {
                            "success": job.result.success if job.result else None,
                            "data": job.result.data if job.result else {},
                            "error_message": job.result.error_message if job.result else None,
                            "warnings": job.result.warnings if job.result else [],
                            "metrics": job.result.metrics if job.result else {}
                        } if job.result else None,
                        "metadata": job.metadata
                    }
                }
                return create_api_response(response_data)

            except Exception as e:
                logger.error(f"Error getting job status: {e}")
                error_data = {
                    "code": "JOB_STATUS_ERROR",
                    "message": "An error occurred while retrieving job status",
                    "details": {"error": str(e)}
                }
                return create_api_response(error_data, success=False, status_code=500)

        @self.mcp_server.custom_route("/api/v1/jobs", methods=["GET"])
        async def get_user_jobs(request: Request) -> JSONResponse:
            """Get all jobs for the authenticated user."""
            try:
                # Validate authentication
                user_id = await self._get_authenticated_user_id(request)
                if not user_id:
                    error_data = {
                        "code": "AUTHENTICATION_REQUIRED",
                        "message": "Authentication required",
                        "details": {"auth_header": "Bearer token required"}
                    }
                    return create_api_response(error_data, success=False, status_code=401)

                # Parse query parameters
                status_filter = request.query_params.get("status")
                limit = int(request.query_params.get("limit", "50"))

                job_manager = get_job_manager()

                # Filter by status if provided
                status_list = None
                if status_filter:
                    try:
                        status_list = [JobStatus(status_filter)]
                    except ValueError:
                        error_data = {
                            "code": "INVALID_STATUS",
                            "message": f"Invalid status: {status_filter}",
                            "details": {"valid_statuses": [s.value for s in JobStatus]}
                        }
                        return create_api_response(error_data, success=False, status_code=400)

                jobs = job_manager.get_user_jobs(user_id, status_list)

                # Limit results
                jobs = jobs[:limit]

                job_data = []
                for job in jobs:
                    job_data.append({
                        "job_id": job.job_id,
                        "job_type": job.job_type.value,
                        "status": job.status.value,
                        "created_at": job.created_at.isoformat(),
                        "started_at": job.started_at.isoformat() if job.started_at else None,
                        "completed_at": job.completed_at.isoformat() if job.completed_at else None,
                        "duration_seconds": job.duration_seconds,
                        "progress_percentage": job.progress.percentage,
                        "current_operation": job.progress.current_operation,
                        "success": job.result.success if job.result else None,
                        "metadata": job.metadata
                    })

                response_data = {
                    "jobs": job_data,
                    "total_count": len(job_data),
                    "user_id": user_id,
                    "filters": {
                        "status": status_filter,
                        "limit": limit
                    }
                }
                return create_api_response(response_data)

            except Exception as e:
                logger.error(f"Error getting user jobs: {e}")
                error_data = {
                    "code": "JOBS_RETRIEVAL_ERROR",
                    "message": "An error occurred while retrieving jobs",
                    "details": {"error": str(e)}
                }
                return create_api_response(error_data, success=False, status_code=500)

        @self.mcp_server.custom_route("/api/v1/jobs/stats", methods=["GET"])
        async def get_job_stats(request: Request) -> JSONResponse:
            """Get job manager statistics."""
            try:
                # Validate authentication
                user_id = await self._get_authenticated_user_id(request)
                if not user_id:
                    error_data = {
                        "code": "AUTHENTICATION_REQUIRED",
                        "message": "Authentication required",
                        "details": {"auth_header": "Bearer token required"}
                    }
                    return create_api_response(error_data, success=False, status_code=401)

                job_manager = get_job_manager()
                stats = job_manager.get_stats()

                # Add user-specific stats
                user_jobs = job_manager.get_user_jobs(user_id)
                user_stats = {
                    "total_jobs": len(user_jobs),
                    "active_jobs": len([j for j in user_jobs if j.is_active]),
                    "completed_jobs": len([j for j in user_jobs if j.is_completed]),
                    "success_rate": 0.0
                }

                completed_jobs = [j for j in user_jobs if j.is_completed and j.result]
                if completed_jobs:
                    successful_jobs = [j for j in completed_jobs if j.result.success]
                    user_stats["success_rate"] = len(successful_jobs) / len(completed_jobs) * 100

                response_data = {
                    "global_stats": stats,
                    "user_stats": user_stats,
                    "user_id": user_id
                }
                return create_api_response(response_data)

            except Exception as e:
                logger.error(f"Error getting job stats: {e}")
                error_data = {
                    "code": "STATS_ERROR",
                    "message": "An error occurred while retrieving statistics",
                    "details": {"error": str(e)}
                }
                return create_api_response(error_data, success=False, status_code=500)

    async def _process_chunk_enhanced(self, chunk: CodeChunk, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Enhanced chunk processing with validation, deduplication, and monitoring.

        Args:
            chunk: The code chunk to process
            metadata: Additional metadata from the extension

        Returns:
            Processing result dictionary with enhanced information
        """
        import time
        import uuid

        start_time = time.time()
        chunk_id = str(uuid.uuid4())

        try:
            # Add chunk to enhanced manager with deduplication
            was_added = self.chunk_manager.add_chunk(
                chunk.path,
                chunk.idx,
                chunk.content,
                metadata
            )

            if not was_added:
                # Chunk was a duplicate
                self.vector_processing_stats["deduplication_saves"] += 1
                processing_time = int((time.time() - start_time) * 1000)

                return {
                    "chunk_id": chunk_id,
                    "file_path": chunk.path,
                    "chunk_index": chunk.idx,
                    "status": "duplicate_skipped",
                    "processing_time_ms": processing_time,
                    "deduplication": True
                }

            # Extract file information
            file_extension = chunk.path.split('.')[-1] if '.' in chunk.path else ''

            result = {
                "chunk_id": chunk_id,
                "file_path": chunk.path,
                "chunk_index": chunk.idx,
                "file_extension": file_extension,
                "content_length": len(chunk.content),
                "lines_count": len(chunk.content.split('\n')),
                "processed_at": datetime.utcnow().isoformat(),
                "status": "chunk_received",
                "validation": self.chunk_manager.file_chunks[chunk.path][-1]["validation"],
                "metadata": metadata or {}
            }

            # Enhanced file completion detection
            chunks_for_file = self.chunk_manager.file_chunks.get(chunk.path, [])
            should_process_file = self._should_process_complete_file(chunk, chunks_for_file, metadata)

            if should_process_file:
                logger.info(f"Processing complete file for {chunk.path} with {len(chunks_for_file)} chunks")
                temp_file_path = self.chunk_manager.reconstruct_file(chunk.path)
                if temp_file_path:
                    # Extract user_id from API key for vector storage
                    user_id = self._extract_user_id()

                    # Create job for file processing
                    job_id = await self._create_file_processing_job(
                        chunk.path,
                        user_id or "vscode_user",
                        len(chunks_for_file)
                    )

                    # Use the enhanced pipeline to process the file with job tracking
                    analysis_result = await self._analyze_complete_file_enhanced_with_job(
                        temp_file_path,
                        file_extension,
                        user_id,
                        len(chunks_for_file),
                        job_id
                    )
                    result.update(analysis_result)
                    result["status"] = "file_processed"
                    result["job_id"] = job_id
                    self.chunk_manager.processing_stats["files_processed"] += 1

            # Language-specific processing
            result["language_info"] = self._get_language_info(file_extension)

            processing_time = int((time.time() - start_time) * 1000)
            result["processing_time_ms"] = processing_time

            logger.info(f"Successfully processed chunk {chunk.idx} from {chunk.path} in {processing_time}ms")
            return result

        except Exception as e:
            self.vector_processing_stats["storage_errors"] += 1
            logger.error(f"Error in _process_chunk_enhanced: {e}")
            raise

    async def _get_authenticated_user_id(self, request: Request) -> Optional[str]:
        """
        Extract and validate user ID from request authentication.

        Uses centralized authentication to ensure consistent user identity
        binding across the entire system. No fallback authentication.
        """
        try:
            auth_header = request.headers.get("Authorization")
            if not auth_header or not auth_header.startswith("Bearer "):
                logger.debug("Missing or invalid Authorization header")
                return None

            api_key = auth_header[7:]  # Remove "Bearer " prefix

            # Use centralized user ID extraction - single source of truth
            from ..auth import extract_user_id_from_api_key
            user_id = extract_user_id_from_api_key(api_key)

            if user_id:
                logger.debug(f"Authenticated user: {user_id}")
                return user_id
            else:
                logger.warning(f"Authentication failed for key: {api_key[:8]}...")
                return None

        except Exception as e:
            logger.error(f"Error extracting user ID: {e}")
            return None

    async def _register_webhook_callback(self, job_id: str, webhook_url: str):
        """Register a webhook callback for job completion notification."""
        import aiohttp
        from urllib.parse import urlparse

        # Validate webhook URL
        try:
            parsed_url = urlparse(webhook_url)
            if not parsed_url.scheme or not parsed_url.netloc:
                logger.warning(f"Invalid webhook URL format: {webhook_url}")
                return
            if parsed_url.scheme not in ['http', 'https']:
                logger.warning(f"Unsupported webhook URL scheme: {parsed_url.scheme}")
                return
        except Exception as e:
            logger.error(f"Error validating webhook URL {webhook_url}: {e}")
            return

        async def webhook_callback(job):
            """Send HTTP POST notification to webhook URL when job completes."""
            try:
                # Safely extract job data with fallbacks
                payload = {
                    "job_id": getattr(job, 'job_id', 'unknown'),
                    "status": getattr(job.status, 'value', 'unknown') if hasattr(job, 'status') else 'unknown',
                    "success": getattr(job.result, 'success', False) if hasattr(job, 'result') and job.result else False,
                    "completed_at": job.completed_at.isoformat() if hasattr(job, 'completed_at') and job.completed_at else None,
                    "duration_seconds": getattr(job, 'duration_seconds', 0),
                    "result_data": getattr(job.result, 'data', {}) if hasattr(job, 'result') and job.result else {},
                    "error_message": getattr(job.result, 'error_message', None) if hasattr(job, 'result') and job.result else None,
                    "warnings": getattr(job.result, 'warnings', []) if hasattr(job, 'result') and job.result else [],
                    "metrics": getattr(job.result, 'metrics', {}) if hasattr(job, 'result') and job.result else {},
                    "metadata": getattr(job, 'metadata', {})
                }

                timeout = aiohttp.ClientTimeout(total=10)  # 10 second timeout
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.post(
                        webhook_url,
                        json=payload,
                        headers={
                            "Content-Type": "application/json",
                            "User-Agent": "MCP-Server-Webhook/1.0"
                        }
                    ) as response:
                        response_text = await response.text()
                        if response.status == 200:
                            logger.info(f"Webhook notification sent successfully for job {payload['job_id']}")
                        else:
                            logger.warning(f"Webhook notification failed for job {payload['job_id']}: {response.status} - {response_text[:200]}")

            except aiohttp.ClientError as e:
                logger.error(f"HTTP client error sending webhook for job {getattr(job, 'job_id', 'unknown')}: {e}")
            except asyncio.TimeoutError:
                logger.error(f"Webhook timeout for job {getattr(job, 'job_id', 'unknown')}: {webhook_url}")
            except Exception as e:
                logger.error(f"Unexpected error sending webhook for job {getattr(job, 'job_id', 'unknown')}: {e}")

        # Register the callback with the job manager
        try:
            job_manager = get_job_manager()
            job_manager.add_completion_callback(job_id, webhook_callback)
            logger.info(f"Registered webhook callback for job {job_id} to {webhook_url}")
        except Exception as e:
            logger.error(f"Failed to register webhook callback for job {job_id}: {e}")

    async def _process_chunk_with_job_tracking(self, chunk: CodeChunk, metadata: Dict[str, Any], job_id: str) -> Dict[str, Any]:
        """Process chunk with Hybrid Cache optimization and job progress tracking."""
        import time
        import uuid

        start_time = time.time()
        chunk_id = str(uuid.uuid4())
        job_manager = get_job_manager()

        try:
            # Initialize hybrid cache if needed
            if not self.cache_initialized:
                await self.initialize_hybrid_cache()

            # Step 1: Check hybrid cache for chunk first
            job_manager.update_progress(job_id, 1, "Checking cache and validating chunk")

            cache_hit = False
            if self.cache_initialized and self.hybrid_cache:
                try:
                    # Check hybrid cache for chunk
                    cache_result = await self.hybrid_cache.get_cached_chunk(chunk.path, chunk.idx)
                    if cache_result.success and cache_result.data:
                        cache_hit = True
                        self.vector_processing_stats["cache_hits"] += 1

                        # Complete job immediately with cached result
                        job_manager.complete_job(
                            job_id=job_id,
                            success=True,
                            result_data={
                                "status": "cache_hit",
                                "chunk_id": chunk_id,
                                "cached_data": cache_result.data,
                                "cache_source": cache_result.source,
                                "hybrid_cache_optimization": True
                            },
                            metrics={"processing_time_ms": int((time.time() - start_time) * 1000)}
                        )

                        processing_time = int((time.time() - start_time) * 1000)
                        logger.info(f"🚀 Cache hit for chunk {chunk.idx} from {chunk.path} ({processing_time}ms, source: {cache_result.source})")

                        return {
                            "chunk_id": chunk_id,
                            "file_path": chunk.path,
                            "chunk_index": chunk.idx,
                            "status": "cache_hit",
                            "processing_time_ms": processing_time,
                            "cache_source": cache_result.source,
                            "hybrid_cache_optimization": True,
                            "cached_data": cache_result.data
                        }
                except Exception as e:
                    logger.warning(f"Hybrid cache lookup failed: {e}")

            if not cache_hit:
                self.vector_processing_stats["cache_misses"] += 1

            # Step 2: Cache chunk with hybrid system (includes deduplication)
            if self.cache_initialized and self.hybrid_cache:
                try:
                    # Extract user_id from metadata
                    user_id = metadata.get("user_id", "vscode_user")

                    # Cache chunk with hybrid system
                    cache_result = await self.hybrid_cache.cache_chunk(
                        chunk.path, chunk.idx, chunk.content, metadata, user_id
                    )

                    if cache_result.source == "duplicate_detected":
                        # Duplicate detected by hybrid cache
                        job_manager.complete_job(
                            job_id=job_id,
                            success=True,
                            result_data={
                                "status": "duplicate_skipped",
                                "chunk_id": chunk_id,
                                "cache_source": cache_result.source,
                                "hybrid_deduplication": True
                            },
                            metrics={"processing_time_ms": int((time.time() - start_time) * 1000)}
                        )

                        self.vector_processing_stats["deduplication_saves"] += 1
                        processing_time = int((time.time() - start_time) * 1000)

                        logger.info(f"🔄 Duplicate chunk detected: {chunk.path}:{chunk.idx} ({processing_time}ms)")

                        return {
                            "chunk_id": chunk_id,
                            "file_path": chunk.path,
                            "chunk_index": chunk.idx,
                            "status": "duplicate_skipped",
                            "processing_time_ms": processing_time,
                            "hybrid_deduplication": True,
                            "cache_source": cache_result.source
                        }
                    else:
                        # Successfully cached new chunk
                        self.vector_processing_stats["chunks_stored"] += 1

                except Exception as e:
                    logger.warning(f"Hybrid cache chunk storage failed: {e}")

            # Fallback to local deduplication
            was_added = self.chunk_manager.add_chunk(
                chunk.path,
                chunk.idx,
                chunk.content,
                metadata
            )

            if not was_added:
                # Chunk was a duplicate - complete job immediately
                job_manager.complete_job(
                    job_id=job_id,
                    success=True,
                    result_data={
                        "status": "duplicate_skipped",
                        "chunk_id": chunk_id,
                        "deduplication": True
                    },
                    metrics={"processing_time_ms": int((time.time() - start_time) * 1000)}
                )

                self.vector_processing_stats["deduplication_saves"] += 1
                processing_time = int((time.time() - start_time) * 1000)

                return {
                    "chunk_id": chunk_id,
                    "file_path": chunk.path,
                    "chunk_index": chunk.idx,
                    "status": "duplicate_skipped",
                    "processing_time_ms": processing_time,
                    "deduplication": True
                }

            # Step 2: Process chunk content
            job_manager.update_progress(job_id, 2, "Processing chunk content")

            # Extract file information
            file_extension = chunk.path.split('.')[-1] if '.' in chunk.path else ''

            result = {
                "chunk_id": chunk_id,
                "file_path": chunk.path,
                "chunk_index": chunk.idx,
                "file_extension": file_extension,
                "content_length": len(chunk.content),
                "lines_count": len(chunk.content.split('\n')),
                "processed_at": datetime.utcnow().isoformat(),
                "status": "chunk_received",
                "validation": self.chunk_manager.file_chunks[chunk.path][-1]["validation"],
                "metadata": metadata or {}
            }

            # Step 3: Check for file completion and vector processing
            job_manager.update_progress(job_id, 3, "Checking file completion")

            chunks_for_file = self.chunk_manager.file_chunks.get(chunk.path, [])
            should_process_file = self._should_process_complete_file(chunk, chunks_for_file, metadata)

            if should_process_file:
                # Step 4: Process complete file with vector storage
                job_manager.update_progress(job_id, 4, "Processing complete file and storing vectors")

                logger.info(f"Processing complete file for {chunk.path} with {len(chunks_for_file)} chunks")
                temp_file_path = self.chunk_manager.reconstruct_file(chunk.path)
                if temp_file_path:
                    # Extract user_id from metadata
                    user_id = metadata.get("user_id", "vscode_user")

                    # Use the enhanced pipeline to process the file
                    analysis_result = await self._analyze_complete_file_enhanced_with_job(
                        temp_file_path,
                        file_extension,
                        user_id,
                        len(chunks_for_file),
                        job_id
                    )
                    result.update(analysis_result)
                    result["status"] = "file_processed"
                    result["job_id"] = job_id
                    self.chunk_manager.processing_stats["files_processed"] += 1

                    # Complete job with success
                    job_manager.complete_job(
                        job_id=job_id,
                        success=True,
                        result_data={
                            "chunks_processed": len(chunks_for_file),
                            "file_path": chunk.path,
                            "vector_storage": analysis_result.get("vector_storage", {}),
                            "analysis_result": analysis_result
                        },
                        metrics={
                            "processing_time_ms": int((time.time() - start_time) * 1000),
                            "chunks_count": len(chunks_for_file),
                            "file_size_bytes": len(chunk.content)
                        }
                    )
            else:
                # Complete job for single chunk processing
                job_manager.complete_job(
                    job_id=job_id,
                    success=True,
                    result_data={
                        "status": "chunk_processed",
                        "chunk_id": chunk_id,
                        "awaiting_more_chunks": True
                    },
                    metrics={"processing_time_ms": int((time.time() - start_time) * 1000)}
                )

            # Language-specific processing
            result["language_info"] = self._get_language_info(file_extension)

            processing_time = int((time.time() - start_time) * 1000)
            result["processing_time_ms"] = processing_time

            logger.info(f"Successfully processed chunk {chunk.idx} from {chunk.path} in {processing_time}ms with job {job_id}")
            return result

        except Exception as e:
            # Complete job with failure
            job_manager.complete_job(
                job_id=job_id,
                success=False,
                error_message=str(e),
                metrics={"processing_time_ms": int((time.time() - start_time) * 1000)}
            )

            self.vector_processing_stats["storage_errors"] += 1
            logger.error(f"Error in _process_chunk_with_job_tracking: {e}")
            raise

    async def _create_file_processing_job(self, file_path: str, user_id: str, chunk_count: int) -> str:
        """Create a job for file processing and return job ID."""
        job_manager = get_job_manager()

        job_id = job_manager.create_job(
            job_type=JobType.FILE_PROCESSING,
            user_id=user_id,
            metadata={
                "file_path": file_path,
                "chunk_count": chunk_count,
                "processing_type": "enhanced_pipeline"
            },
            timeout_seconds=600  # 10 minutes for file processing
        )

        # Start the job with estimated steps
        total_steps = 4  # chunking, embedding, storage, completion
        job_manager.start_job(job_id, total_steps)

        return job_id

    async def _process_chunk(self, chunk: CodeChunk) -> Dict[str, Any]:
        """
        Process a code chunk using existing MCP server capabilities.

        Args:
            chunk: The code chunk to process

        Returns:
            Processing result dictionary
        """
        try:
            # Add chunk to manager
            self.chunk_manager.add_chunk(chunk.path, chunk.idx, chunk.content)
            self.processing_stats["chunks_received"] += 1

            # Extract file information
            file_extension = chunk.path.split('.')[-1] if '.' in chunk.path else ''

            result = {
                "file_path": chunk.path,
                "chunk_index": chunk.idx,
                "file_extension": file_extension,
                "content_length": len(chunk.content),
                "lines_count": len(chunk.content.split('\n')),
                "processed_at": datetime.utcnow().isoformat(),
                "status": "chunk_received"
            }

            # Try to process complete file if we have enough chunks
            # This is a simple heuristic - in practice you might want to wait for an "end" signal
            chunks_for_file = self.chunk_manager.file_chunks.get(chunk.path, [])

            # If this looks like the last chunk (heuristic: chunk content is smaller than usual)
            # or if we have accumulated many chunks, try to process the complete file
            should_process_file = (
                len(chunk.content) < 1000 or  # Small chunk might indicate end
                len(chunks_for_file) > 10     # Many chunks accumulated
            )

            if should_process_file:
                temp_file_path = self.chunk_manager.reconstruct_file(chunk.path)
                if temp_file_path:
                    # Extract user_id from metadata (passed from authenticated request)
                    user_id = metadata.get("user_id")
                    if not user_id:
                        logger.error("No user_id in metadata for file processing")
                        return result

                    # Use the integration test pattern pipeline to process the file
                    # This creates rich function/class relationship data for MCP tools
                    analysis_result = await self._analyze_complete_file_integration_test_pattern(
                        temp_file_path,
                        file_extension,
                        user_id,
                        len(chunks_for_file)
                    )
                    result.update(analysis_result)
                    result["status"] = "file_processed"
                    self.processing_stats["files_processed"] += 1

            # Language-specific processing
            if file_extension == 'py':
                result["language"] = "python"
                result["analysis_type"] = "Python code analysis"
            elif file_extension in ['ts', 'js']:
                result["language"] = "typescript/javascript"
                result["analysis_type"] = "TypeScript/JavaScript analysis"
            elif file_extension in ['java', 'kt']:
                result["language"] = "jvm"
                result["analysis_type"] = "JVM language analysis"
            else:
                result["language"] = "generic"
                result["analysis_type"] = "Generic text analysis"

            logger.info(f"Successfully processed chunk {chunk.idx} from {chunk.path}")
            return result

        except Exception as e:
            self.processing_stats["errors"] += 1
            logger.error(f"Error in _process_chunk: {e}")
            raise

    def _should_process_complete_file(self, chunk: CodeChunk, chunks_for_file: List[Dict], metadata: Dict[str, Any]) -> bool:
        """Enhanced logic to determine if we should process the complete file."""
        # Check if this is explicitly marked as the last chunk
        if metadata and metadata.get('isLastChunk'):
            return True

        # Original heuristics with improvements
        small_chunk_threshold = metadata.get('maxChunkSize', 1000) * 0.5
        is_small_chunk = len(chunk.content) < small_chunk_threshold
        has_many_chunks = len(chunks_for_file) > 10

        # Additional heuristics
        has_file_ending_patterns = any(
            pattern in chunk.content.lower()
            for pattern in ['# end of file', '// end of file', '/* end', 'eof']
        )

        # Time-based processing (process if chunks haven't been received for a while)
        if chunks_for_file:
            last_chunk_time = chunks_for_file[-1].get('timestamp')
            if last_chunk_time:
                from datetime import datetime, timedelta
                last_time = datetime.fromisoformat(last_chunk_time.replace('Z', '+00:00'))
                time_since_last = datetime.utcnow() - last_time.replace(tzinfo=None)
                has_timeout = time_since_last > timedelta(seconds=30)
            else:
                has_timeout = False
        else:
            has_timeout = False

        return (is_small_chunk or has_many_chunks or
                has_file_ending_patterns or has_timeout)

    def _extract_user_id(self) -> Optional[str]:
        """
        Extract user ID for vector storage.

        DEPRECATED: Use user_id from authenticated request context instead.
        This method should not be used as it cannot access request context.
        """
        logger.warning("_extract_user_id is deprecated - use user_id from request context")
        return None

    def _get_language_info(self, file_extension: str) -> Dict[str, str]:
        """Get language information for the file extension."""
        language_map = {
            'py': {'language': 'python', 'analysis_type': 'Python code analysis'},
            'ts': {'language': 'typescript', 'analysis_type': 'TypeScript analysis'},
            'js': {'language': 'javascript', 'analysis_type': 'JavaScript analysis'},
            'tsx': {'language': 'typescript-react', 'analysis_type': 'TypeScript React analysis'},
            'jsx': {'language': 'javascript-react', 'analysis_type': 'JavaScript React analysis'},
            'java': {'language': 'java', 'analysis_type': 'Java analysis'},
            'kt': {'language': 'kotlin', 'analysis_type': 'Kotlin analysis'},
            'go': {'language': 'go', 'analysis_type': 'Go analysis'},
            'rs': {'language': 'rust', 'analysis_type': 'Rust analysis'},
            'cpp': {'language': 'cpp', 'analysis_type': 'C++ analysis'},
            'c': {'language': 'c', 'analysis_type': 'C analysis'},
            'cs': {'language': 'csharp', 'analysis_type': 'C# analysis'},
            'php': {'language': 'php', 'analysis_type': 'PHP analysis'},
            'rb': {'language': 'ruby', 'analysis_type': 'Ruby analysis'}
        }
        return language_map.get(file_extension, {
            'language': 'generic',
            'analysis_type': 'Generic text analysis'
        })

    async def _analyze_complete_file_enhanced(
        self,
        temp_file_path: str,
        file_extension: str,
        user_id: Optional[str] = None,
        chunk_count: int = 0
    ) -> Dict[str, Any]:
        """
        Enhanced analysis of complete reconstructed file with monitoring and optimization.
        """
        import time

        start_time = time.time()

        try:
            analysis = {
                "temp_file_created": True,
                "temp_file_path": temp_file_path,
                "file_size": os.path.getsize(temp_file_path),
                "chunk_count": chunk_count,
                "pipeline_used": "enhanced"
            }

            # Step 1: Enhanced Code Chunking
            from src.processing.code_chunker import CodeChunkerFactory

            chunker = CodeChunkerFactory.create_chunker(temp_file_path)
            chunks = chunker.chunk_file(temp_file_path)

            analysis["chunking_result"] = {
                "chunks_created": len(chunks),
                "chunker_type": type(chunker).__name__,
                "avg_chunk_size": sum(len(c.content) for c in chunks) / len(chunks) if chunks else 0
            }

            if chunks:
                # Step 2: Redis-Optimized Embedding Generation
                embedding_start = time.time()
                embedded_chunks = []

                if self.redis_initialized and self.cached_embedding_service:
                    # Use Redis-cached embedding service for massive performance improvement
                    logger.info("Using Redis-cached embedding service for optimal performance")

                    try:
                        # Batch process with intelligent caching
                        chunk_contents = [chunk.content for chunk in chunks]

                        # Get embeddings with cache optimization
                        embeddings = await self.cached_embedding_service.embed_batch(chunk_contents)

                        for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
                            chunk_dict = chunk.to_dict()
                            chunk_dict['embedding'] = embedding
                            chunk_dict['embedding_size'] = len(embedding)
                            embedded_chunks.append(chunk_dict)

                        # Get cache statistics
                        cache_stats = self.cached_embedding_service.get_stats()
                        self.vector_processing_stats["embeddings_generated"] += len(embeddings)
                        self.vector_processing_stats["api_calls_saved"] += cache_stats.get("api_calls_saved", 0)

                        logger.info(f"Embedding batch completed: {cache_stats.get('cache_hit_rate', 0):.1%} cache hit rate")

                    except Exception as e:
                        logger.warning(f"Redis-cached embedding failed, falling back to direct API: {e}")
                        # Fallback to direct embedding service
                        from src.vector.openai_embeddings import OpenAIEmbeddingService
                        embedding_service = OpenAIEmbeddingService()
                        embeddings = embedding_service.embed_batch(chunk_contents)

                        for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
                            chunk_dict = chunk.to_dict()
                            chunk_dict['embedding'] = embedding
                            chunk_dict['embedding_size'] = len(embedding)
                            embedded_chunks.append(chunk_dict)

                        self.vector_processing_stats["embeddings_generated"] += len(embeddings)
                else:
                    # Fallback to standard embedding service
                    logger.info("Using standard embedding service (Redis cache not available)")
                    from src.vector.openai_embeddings import OpenAIEmbeddingService

                    embedding_service = OpenAIEmbeddingService()
                    chunk_contents = [chunk.content for chunk in chunks]

                    try:
                        # Use batch embedding for better performance
                        embeddings = embedding_service.embed_batch(chunk_contents)

                        for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
                            chunk_dict = chunk.to_dict()
                            chunk_dict['embedding'] = embedding
                            chunk_dict['embedding_size'] = len(embedding)
                            embedded_chunks.append(chunk_dict)

                        self.vector_processing_stats["embeddings_generated"] += len(embeddings)

                    except Exception as e:
                        # Fallback to individual embeddings
                        logger.warning(f"Batch embedding failed, using individual: {e}")
                        for chunk in chunks:
                            try:
                                chunk_dict = chunk.to_dict()
                                embedding = embedding_service.embed_code_chunk(
                                    chunk.content,
                                    chunk.metadata
                                )
                                chunk_dict['embedding'] = embedding
                                chunk_dict['embedding_size'] = len(embedding)
                                embedded_chunks.append(chunk_dict)
                                self.vector_processing_stats["embeddings_generated"] += 1
                            except Exception as embed_error:
                                logger.error(f"Failed to embed chunk: {embed_error}")
                                continue

                embedding_time = time.time() - embedding_start
                analysis["embedding_result"] = {
                    "embeddings_generated": len(embedded_chunks),
                    "embedding_model": embedding_service.model_name,
                    "vector_size": embedding_service.get_vector_size(),
                    "embedding_time_ms": int(embedding_time * 1000)
                }

                # Step 3: Enhanced Vector Storage with Monitoring
                if user_id and embedded_chunks:
                    storage_start = time.time()
                    try:
                        from src.vector.openai_qdrant_client import OpenAIQdrantClient, OpenAIQdrantConfig

                        config = OpenAIQdrantConfig(
                            url=os.getenv('QDRANT_URL', 'https://localhost:6333'),
                            api_key=os.getenv('QDRANT_API_KEY'),
                            timeout=30,
                            openai_api_key=os.getenv('OPENAI_API_KEY'),
                            embedding_model="text-embedding-3-small",
                            vector_size=1536
                        )

                        qdrant_client = OpenAIQdrantClient(config)

                        # Create user collection if needed
                        if qdrant_client.create_user_collection(user_id):
                            # Prepare chunks for storage with enhanced metadata
                            storage_chunks = []
                            for i, chunk in enumerate(embedded_chunks):
                                storage_chunk = {k: v for k, v in chunk.items() if k != 'embedding'}
                                storage_chunk['id'] = f"{temp_file_path}_{i}_{int(time.time())}"
                                storage_chunk['source_file'] = temp_file_path
                                storage_chunk['processing_timestamp'] = datetime.utcnow().isoformat()
                                storage_chunk['chunk_count_in_file'] = len(embedded_chunks)
                                storage_chunks.append(storage_chunk)

                            # Store chunks with monitoring
                            if qdrant_client.store_code_chunks(user_id, storage_chunks):
                                storage_time = time.time() - storage_start
                                self.vector_processing_stats["chunks_stored"] += len(storage_chunks)

                                analysis["vector_storage_result"] = {
                                    "stored_successfully": True,
                                    "chunks_stored": len(storage_chunks),
                                    "user_collection": user_id,
                                    "storage_time_ms": int(storage_time * 1000)
                                }
                            else:
                                self.vector_processing_stats["storage_errors"] += 1
                                analysis["vector_storage_result"] = {
                                    "stored_successfully": False,
                                    "error": "Failed to store chunks"
                                }
                        else:
                            self.vector_processing_stats["storage_errors"] += 1
                            analysis["vector_storage_result"] = {
                                "stored_successfully": False,
                                "error": "Failed to create user collection"
                            }

                    except Exception as e:
                        self.vector_processing_stats["storage_errors"] += 1
                        logger.warning(f"Vector storage failed: {e}")
                        analysis["vector_storage_result"] = {
                            "stored_successfully": False,
                            "error": str(e)
                        }
                else:
                    analysis["vector_storage_result"] = {
                        "skipped": True,
                        "reason": "No user_id provided or no embedded chunks"
                    }

            # Update processing time statistics
            total_time = time.time() - start_time
            self.vector_processing_stats["processing_time_ms"] += int(total_time * 1000)
            analysis["total_processing_time_ms"] = int(total_time * 1000)

            return analysis

        except Exception as e:
            logger.error(f"Error in enhanced file analysis: {e}")
            return {"analysis_error": str(e)}

    async def _analyze_complete_file_integration_test_pattern(
        self,
        temp_file_path: str,
        file_extension: str,
        user_id: Optional[str] = None,
        chunk_count: int = 0
    ) -> Dict[str, Any]:
        """
        Enhanced analysis following the exact integration test pattern for rich metadata.
        This creates the same rich function/class relationship data that MCP tools expect.

        Based on scripts/integration_test.py pattern:
        1. Code chunking with rich metadata extraction
        2. Embedding generation with metadata context
        3. Vector storage with comprehensive payload structure
        4. Payload structure validation
        """
        import time

        start_time = time.time()

        try:
            analysis = {
                "temp_file_created": True,
                "temp_file_path": temp_file_path,
                "file_size": os.path.getsize(temp_file_path),
                "chunk_count": chunk_count,
                "pipeline_used": "integration_test_pattern",
                "pattern_source": "scripts/integration_test.py"
            }

            # Step 1: Code Chunking with Rich Metadata (Integration Test Pattern)
            logger.info("Step 1: Code chunking with rich metadata extraction")
            from src.processing.code_chunker import CodeChunkerFactory

            chunker = CodeChunkerFactory.create_chunker(temp_file_path)
            chunks = chunker.chunk_file(temp_file_path)

            # Analyze chunk composition like integration test
            chunk_types = {}
            function_chunks = []
            class_chunks = []
            module_chunks = []

            for chunk in chunks:
                chunk_type = chunk.chunk_type
                chunk_types[chunk_type] = chunk_types.get(chunk_type, 0) + 1

                if chunk_type == 'function':
                    function_chunks.append(chunk)
                elif chunk_type == 'class':
                    class_chunks.append(chunk)
                elif chunk_type == 'module':
                    module_chunks.append(chunk)

            analysis["chunking_result"] = {
                "chunks_created": len(chunks),
                "chunker_type": type(chunker).__name__,
                "avg_chunk_size": sum(len(c.content) for c in chunks) / len(chunks) if chunks else 0,
                "chunk_types": chunk_types,
                "functions_found": len(function_chunks),
                "classes_found": len(class_chunks),
                "modules_found": len(module_chunks),
                "rich_metadata_extracted": True
            }

            # Log chunk details like integration test
            logger.info(f"Generated {len(chunks)} chunks with rich metadata")
            for i, chunk in enumerate(chunks):
                logger.debug(f"Chunk {i+1}: {chunk.chunk_type} - Lines {chunk.start_line}-{chunk.end_line}")
                logger.debug(f"  Metadata keys: {list(chunk.metadata.keys())}")

            if chunks:
                # Step 2: Embedding Generation with Metadata Context (Integration Test Pattern)
                logger.info("Step 2: Embedding generation with metadata context")
                embedding_start = time.time()
                from src.vector.openai_embeddings import OpenAIEmbeddingService

                embedding_service = OpenAIEmbeddingService()
                embedded_chunks = []

                # Process each chunk individually like integration test
                for i, chunk in enumerate(chunks):
                    logger.debug(f"Processing chunk {i+1}/{len(chunks)}: {chunk.chunk_type}")

                    # Convert chunk to dict format (integration test pattern)
                    chunk_dict = chunk.to_dict()

                    # Generate embedding with metadata context (integration test pattern)
                    embedding = embedding_service.embed_code_chunk(
                        chunk.content,
                        chunk.metadata
                    )

                    # Add embedding to chunk data (integration test pattern)
                    chunk_dict['embedding'] = embedding
                    chunk_dict['embedding_size'] = len(embedding)

                    embedded_chunks.append(chunk_dict)

                    # Log embedding details like integration test
                    logger.debug(f"Generated embedding: {len(embedding)} dimensions")

                    # Test preprocessing like integration test
                    preprocessed = embedding_service.preprocess_code(
                        chunk.content,
                        chunk.metadata.get('language', 'python')
                    )
                    logger.debug(f"Preprocessed content length: {len(preprocessed)} characters")

                self.vector_processing_stats["embeddings_generated"] += len(embedded_chunks)

                embedding_time = time.time() - embedding_start
                analysis["embedding_result"] = {
                    "embeddings_generated": len(embedded_chunks),
                    "embedding_model": embedding_service.model_name,
                    "vector_size": embedding_service.get_vector_size(),
                    "embedding_time_ms": int(embedding_time * 1000),
                    "metadata_context_used": True
                }

                logger.info(f"Embedding generation completed for {len(embedded_chunks)} chunks")

                # Step 3: Vector Storage with Rich Payload Structure (Integration Test Pattern)
                if user_id and embedded_chunks:
                    logger.info("Step 3: Vector storage with rich payload structure")
                    storage_start = time.time()
                    try:
                        from src.vector.openai_qdrant_client import OpenAIQdrantClient, OpenAIQdrantConfig

                        config = OpenAIQdrantConfig(
                            url=os.getenv('QDRANT_URL', 'https://localhost:6333'),
                            api_key=os.getenv('QDRANT_API_KEY'),
                            timeout=30,
                            openai_api_key=os.getenv('OPENAI_API_KEY'),
                            embedding_model="text-embedding-3-small",
                            vector_size=1536
                        )

                        qdrant_client = OpenAIQdrantClient(config)

                        # Test health check like integration test
                        if not qdrant_client.health_check():
                            logger.error("Qdrant health check failed")
                            analysis["vector_storage_result"] = {
                                "stored_successfully": False,
                                "error": "Qdrant health check failed"
                            }
                        else:
                            logger.info("Qdrant health check passed")

                            # Create user collection like integration test
                            if qdrant_client.create_user_collection(user_id):
                                logger.info(f"Created/verified collection for user: {user_id}")

                                # Prepare chunks for storage with rich metadata (integration test pattern)
                                storage_chunks = []
                                for i, chunk in enumerate(embedded_chunks):
                                    # Remove embedding field as it's generated internally
                                    storage_chunk = {k: v for k, v in chunk.items() if k != 'embedding'}
                                    # Use integer ID for Qdrant compatibility
                                    storage_chunk['id'] = int(time.time() * 1000000) + i  # Microsecond timestamp + index
                                    storage_chunk['source_file'] = temp_file_path
                                    storage_chunk['processing_timestamp'] = datetime.utcnow().isoformat()
                                    storage_chunk['chunk_count_in_file'] = len(embedded_chunks)
                                    storage_chunk['integration_test_pattern'] = True
                                    storage_chunks.append(storage_chunk)

                                # Store chunks like integration test
                                if qdrant_client.store_code_chunks(user_id, storage_chunks):
                                    storage_time = time.time() - storage_start
                                    self.vector_processing_stats["chunks_stored"] += len(storage_chunks)

                                    logger.info(f"Stored {len(storage_chunks)} chunks successfully")

                                    # Step 4: Payload Structure Validation (Integration Test Pattern)
                                    validation_result = await self._validate_payload_structure(user_id, qdrant_client)

                                    analysis["vector_storage_result"] = {
                                        "stored_successfully": True,
                                        "chunks_stored": len(storage_chunks),
                                        "user_collection": user_id,
                                        "storage_time_ms": int(storage_time * 1000),
                                        "rich_payload_structure": True,
                                        "payload_validation": validation_result
                                    }
                                else:
                                    self.vector_processing_stats["storage_errors"] += 1
                                    analysis["vector_storage_result"] = {
                                        "stored_successfully": False,
                                        "error": "Failed to store chunks"
                                    }
                            else:
                                self.vector_processing_stats["storage_errors"] += 1
                                analysis["vector_storage_result"] = {
                                    "stored_successfully": False,
                                    "error": "Failed to create user collection"
                                }

                    except Exception as e:
                        self.vector_processing_stats["storage_errors"] += 1
                        logger.warning(f"Vector storage failed: {e}")
                        analysis["vector_storage_result"] = {
                            "stored_successfully": False,
                            "error": str(e)
                        }
                else:
                    analysis["vector_storage_result"] = {
                        "skipped": True,
                        "reason": "No user_id provided or no embedded chunks"
                    }

            # Update processing time statistics
            total_time = time.time() - start_time
            self.vector_processing_stats["processing_time_ms"] += int(total_time * 1000)
            analysis["total_processing_time_ms"] = int(total_time * 1000)

            logger.info(f"Integration test pattern analysis completed in {total_time:.2f}s")
            return analysis

        except Exception as e:
            logger.error(f"Error in integration test pattern analysis: {e}")
            return {"analysis_error": str(e)}

    async def _validate_payload_structure(self, user_id: str, qdrant_client) -> Dict[str, Any]:
        """
        Validate payload structure like integration test.

        Based on scripts/integration_test.py test_payload_structure_validation()
        """
        try:
            logger.info("Step 4: Payload structure validation")

            # Search for chunks to validate payload structure
            results = qdrant_client.search_code(
                user_id=user_id,
                query="python function class",
                limit=3
            )

            if not results:
                return {
                    "validation_passed": False,
                    "error": "No results found in vector store"
                }

            # Expected fields from integration test
            expected_fields = [
                'content', 'file_path', 'function_name', 'class_name',
                'start_line', 'end_line', 'imports', 'dependencies',
                'complexity', 'chunk_type', 'language', 'timestamp'
            ]

            validation_results = []

            for i, result in enumerate(results):
                chunk_validation = {
                    "chunk_id": result.get('id'),
                    "chunk_type": result.get('chunk_type'),
                    "score": result.get('score', 0.0)
                }

                # Check metadata completeness
                metadata = result.get('metadata', {})
                missing_fields = []
                present_fields = []

                for field in expected_fields:
                    if field in metadata:
                        present_fields.append(field)
                    else:
                        missing_fields.append(field)

                chunk_validation.update({
                    "total_fields": len(metadata),
                    "expected_fields_present": len(present_fields),
                    "expected_fields_missing": len(missing_fields),
                    "missing_fields": missing_fields,
                    "completeness_percentage": (len(present_fields) / len(expected_fields)) * 100
                })

                # Validate rich metadata like integration test
                rich_metadata_validation = {}

                # Check imports
                imports = metadata.get('imports', [])
                rich_metadata_validation['imports'] = {
                    "count": len(imports),
                    "has_imports": len(imports) > 0
                }

                # Check complexity
                complexity = metadata.get('complexity', 0)
                rich_metadata_validation['complexity'] = {
                    "value": complexity,
                    "is_valid": complexity > 0
                }

                # Check dependencies
                dependencies = metadata.get('dependencies', [])
                rich_metadata_validation['dependencies'] = {
                    "count": len(dependencies),
                    "has_dependencies": len(dependencies) > 0
                }

                # Check function/class specific metadata
                if result.get('chunk_type') == 'function':
                    params = metadata.get('parameters', [])
                    decorators = metadata.get('decorators', [])
                    is_async = metadata.get('is_async', False)
                    rich_metadata_validation['function_metadata'] = {
                        "parameters_count": len(params),
                        "decorators_count": len(decorators),
                        "is_async": is_async
                    }

                elif result.get('chunk_type') == 'class':
                    methods = metadata.get('methods', [])
                    base_classes = metadata.get('base_classes', [])
                    rich_metadata_validation['class_metadata'] = {
                        "methods_count": len(methods),
                        "base_classes_count": len(base_classes)
                    }

                chunk_validation['rich_metadata_validation'] = rich_metadata_validation
                validation_results.append(chunk_validation)

            # Overall validation summary
            total_chunks = len(validation_results)
            avg_completeness = sum(r['completeness_percentage'] for r in validation_results) / total_chunks

            validation_summary = {
                "validation_passed": avg_completeness >= 80.0,  # 80% completeness threshold
                "total_chunks_validated": total_chunks,
                "average_completeness_percentage": avg_completeness,
                "chunks_with_rich_metadata": len([r for r in validation_results if r['completeness_percentage'] >= 80]),
                "validation_results": validation_results
            }

            logger.info(f"Payload validation completed: {avg_completeness:.1f}% average completeness")
            return validation_summary

        except Exception as e:
            logger.error(f"Payload validation failed: {e}")
            return {
                "validation_passed": False,
                "error": str(e)
            }

    async def _analyze_complete_file_enhanced_with_job(
        self,
        temp_file_path: str,
        file_extension: str,
        user_id: Optional[str] = None,
        chunk_count: int = 0,
        job_id: str = None
    ) -> Dict[str, Any]:
        """
        Enhanced analysis with job tracking and progress updates.
        """
        import time

        start_time = time.time()
        job_manager = get_job_manager()

        try:
            # Update job progress - Step 1: File reconstruction complete
            if job_id:
                job_manager.update_progress(job_id, 1, "File reconstruction completed")

            analysis = {
                "temp_file_created": True,
                "temp_file_path": temp_file_path,
                "file_size": os.path.getsize(temp_file_path),
                "chunk_count": chunk_count,
                "pipeline_used": "enhanced_with_job_tracking",
                "job_id": job_id
            }

            # Step 2: Enhanced Code Chunking
            if job_id:
                job_manager.update_progress(job_id, 2, "Processing code chunks")

            from src.processing.code_chunker import CodeChunkerFactory
            chunker = CodeChunkerFactory.create_chunker(temp_file_path)
            chunks = chunker.chunk_file(temp_file_path)

            analysis["chunking_result"] = {
                "chunks_created": len(chunks),
                "chunker_type": type(chunker).__name__,
                "avg_chunk_size": sum(len(c.content) for c in chunks) / len(chunks) if chunks else 0
            }

            if chunks:
                # Step 3: Optimized Embedding Generation
                if job_id:
                    job_manager.update_progress(job_id, 3, f"Generating embeddings for {len(chunks)} chunks")

                embedding_start = time.time()
                from src.vector.openai_embeddings import OpenAIEmbeddingService

                embedding_service = OpenAIEmbeddingService()
                embedded_chunks = []

                # Batch process embeddings for efficiency
                chunk_contents = [chunk.content for chunk in chunks]

                try:
                    # Use batch embedding for better performance
                    embeddings = embedding_service.embed_batch(chunk_contents)

                    for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
                        chunk_dict = chunk.to_dict()
                        chunk_dict['embedding'] = embedding
                        chunk_dict['embedding_size'] = len(embedding)
                        embedded_chunks.append(chunk_dict)

                    self.vector_processing_stats["embeddings_generated"] += len(embeddings)

                except Exception as e:
                    # Fallback to individual embeddings
                    logger.warning(f"Batch embedding failed, using individual: {e}")
                    for i, chunk in enumerate(chunks):
                        try:
                            if job_id and i % 5 == 0:  # Update every 5 chunks
                                progress = 3 + (i / len(chunks)) * 0.5  # Between step 3 and 3.5
                                job_manager.update_progress(job_id, progress, f"Embedding chunk {i+1}/{len(chunks)}")

                            chunk_dict = chunk.to_dict()
                            embedding = embedding_service.embed_code_chunk(
                                chunk.content,
                                chunk.metadata
                            )
                            chunk_dict['embedding'] = embedding
                            chunk_dict['embedding_size'] = len(embedding)
                            embedded_chunks.append(chunk_dict)
                            self.vector_processing_stats["embeddings_generated"] += 1
                        except Exception as embed_error:
                            logger.error(f"Failed to embed chunk: {embed_error}")
                            continue

                embedding_time = time.time() - embedding_start
                analysis["embedding_result"] = {
                    "embeddings_generated": len(embedded_chunks),
                    "embedding_model": embedding_service.model_name,
                    "vector_size": embedding_service.get_vector_size(),
                    "embedding_time_ms": int(embedding_time * 1000)
                }

                # Step 4: Enhanced Vector Storage with Monitoring
                if user_id and embedded_chunks:
                    if job_id:
                        job_manager.update_progress(job_id, 4, f"Storing {len(embedded_chunks)} chunks in vector database")

                    storage_start = time.time()
                    try:
                        from src.vector.openai_qdrant_client import OpenAIQdrantClient, OpenAIQdrantConfig

                        config = OpenAIQdrantConfig(
                            url=os.getenv('QDRANT_URL', 'https://localhost:6333'),
                            api_key=os.getenv('QDRANT_API_KEY'),
                            timeout=30,
                            openai_api_key=os.getenv('OPENAI_API_KEY'),
                            embedding_model="text-embedding-3-small",
                            vector_size=1536
                        )

                        qdrant_client = OpenAIQdrantClient(config)

                        # Create user collection if needed
                        if qdrant_client.create_user_collection(user_id):
                            # Prepare chunks for storage with enhanced metadata
                            storage_chunks = []
                            for i, chunk in enumerate(embedded_chunks):
                                storage_chunk = {k: v for k, v in chunk.items() if k != 'embedding'}
                                # Use integer ID for Qdrant compatibility
                                storage_chunk['id'] = int(time.time() * 1000000) + i  # Microsecond timestamp + index
                                storage_chunk['source_file'] = temp_file_path
                                storage_chunk['processing_timestamp'] = datetime.utcnow().isoformat()
                                storage_chunk['chunk_count_in_file'] = len(embedded_chunks)
                                storage_chunk['job_id'] = job_id
                                storage_chunks.append(storage_chunk)

                            # Store chunks with monitoring
                            if qdrant_client.store_code_chunks(user_id, storage_chunks):
                                storage_time = time.time() - storage_start
                                self.vector_processing_stats["chunks_stored"] += len(storage_chunks)

                                analysis["vector_storage_result"] = {
                                    "stored_successfully": True,
                                    "chunks_stored": len(storage_chunks),
                                    "user_collection": user_id,
                                    "storage_time_ms": int(storage_time * 1000)
                                }

                                # Complete job successfully
                                if job_id:
                                    job_manager.complete_job(
                                        job_id,
                                        success=True,
                                        result_data={
                                            "file_path": temp_file_path,
                                            "chunks_processed": len(chunks),
                                            "chunks_stored": len(storage_chunks),
                                            "user_collection": user_id
                                        },
                                        metrics={
                                            "processing_time_ms": int((time.time() - start_time) * 1000),
                                            "embedding_time_ms": int(embedding_time * 1000),
                                            "storage_time_ms": int(storage_time * 1000),
                                            "chunks_per_second": len(chunks) / (time.time() - start_time)
                                        }
                                    )
                            else:
                                self.vector_processing_stats["storage_errors"] += 1
                                analysis["vector_storage_result"] = {
                                    "stored_successfully": False,
                                    "error": "Failed to store chunks"
                                }

                                # Complete job with failure
                                if job_id:
                                    job_manager.complete_job(
                                        job_id,
                                        success=False,
                                        error_message="Failed to store chunks in vector database"
                                    )
                        else:
                            self.vector_processing_stats["storage_errors"] += 1
                            analysis["vector_storage_result"] = {
                                "stored_successfully": False,
                                "error": "Failed to create user collection"
                            }

                            # Complete job with failure
                            if job_id:
                                job_manager.complete_job(
                                    job_id,
                                    success=False,
                                    error_message="Failed to create user collection"
                                )

                    except Exception as e:
                        self.vector_processing_stats["storage_errors"] += 1
                        logger.warning(f"Vector storage failed: {e}")
                        analysis["vector_storage_result"] = {
                            "stored_successfully": False,
                            "error": str(e)
                        }

                        # Complete job with failure
                        if job_id:
                            job_manager.complete_job(
                                job_id,
                                success=False,
                                error_message=f"Vector storage error: {str(e)}"
                            )
                else:
                    analysis["vector_storage_result"] = {
                        "skipped": True,
                        "reason": "No user_id provided or no embedded chunks"
                    }

                    # Complete job with partial success
                    if job_id:
                        job_manager.complete_job(
                            job_id,
                            success=True,
                            result_data={
                                "file_path": temp_file_path,
                                "chunks_processed": len(chunks),
                                "storage_skipped": True
                            },
                            warnings=["Vector storage skipped - no user_id or embedded chunks"]
                        )

            # Update processing time statistics
            total_time = time.time() - start_time
            self.vector_processing_stats["processing_time_ms"] += int(total_time * 1000)
            analysis["total_processing_time_ms"] = int(total_time * 1000)

            return analysis

        except Exception as e:
            logger.error(f"Error in enhanced file analysis with job tracking: {e}")

            # Complete job with failure
            if job_id:
                job_manager.complete_job(
                    job_id,
                    success=False,
                    error_message=f"File analysis error: {str(e)}"
                )

            return {"analysis_error": str(e), "job_id": job_id}

    async def _analyze_complete_file(self, temp_file_path: str, file_extension: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Analyze a complete reconstructed file using the full tools pipeline.

        Args:
            temp_file_path: Path to the temporary file
            file_extension: File extension for language detection
            user_id: User identifier for vector storage

        Returns:
            Analysis results dictionary
        """
        try:
            analysis = {
                "temp_file_created": True,
                "temp_file_path": temp_file_path,
                "file_size": os.path.getsize(temp_file_path),
                "pipeline_used": "full"
            }

            # Use the complete pipeline from integration_test.py
            try:
                # Step 1: Code Chunking with Rich Metadata
                from src.processing.code_chunker import CodeChunkerFactory

                chunker = CodeChunkerFactory.create_chunker(temp_file_path)
                chunks = chunker.chunk_file(temp_file_path)

                analysis["chunking_result"] = {
                    "chunks_created": len(chunks),
                    "chunker_type": type(chunker).__name__
                }

                if chunks:
                    # Step 2: Generate Embeddings
                    from src.vector.openai_embeddings import OpenAIEmbeddingService

                    embedding_service = OpenAIEmbeddingService()
                    embedded_chunks = []

                    for chunk in chunks:
                        chunk_dict = chunk.to_dict()

                        # Generate embedding with metadata context
                        embedding = embedding_service.embed_code_chunk(
                            chunk.content,
                            chunk.metadata
                        )

                        chunk_dict['embedding'] = embedding
                        chunk_dict['embedding_size'] = len(embedding)
                        embedded_chunks.append(chunk_dict)

                    analysis["embedding_result"] = {
                        "embeddings_generated": len(embedded_chunks),
                        "embedding_model": embedding_service.model_name,
                        "vector_size": embedding_service.get_vector_size()
                    }

                    # Step 3: Store in Vector Database (if user_id provided)
                    if user_id:
                        try:
                            from src.vector.openai_qdrant_client import OpenAIQdrantClient, OpenAIQdrantConfig

                            config = OpenAIQdrantConfig(
                                url=os.getenv('QDRANT_URL', 'https://localhost:6333'),
                                api_key=os.getenv('QDRANT_API_KEY'),
                                timeout=30,
                                openai_api_key=os.getenv('OPENAI_API_KEY'),
                                embedding_model="text-embedding-3-small",
                                vector_size=1536
                            )

                            qdrant_client = OpenAIQdrantClient(config)

                            # Create user collection if needed
                            if qdrant_client.create_user_collection(user_id):
                                # Prepare chunks for storage (remove embedding field)
                                import time
                                storage_chunks = []
                                for i, chunk in enumerate(embedded_chunks):
                                    storage_chunk = {k: v for k, v in chunk.items() if k != 'embedding'}
                                    # Use integer ID for Qdrant compatibility
                                    storage_chunk['id'] = int(time.time() * 1000000) + i  # Microsecond timestamp + index
                                    storage_chunks.append(storage_chunk)

                                # Store chunks
                                if qdrant_client.store_code_chunks(user_id, storage_chunks):
                                    analysis["vector_storage_result"] = {
                                        "stored_successfully": True,
                                        "chunks_stored": len(storage_chunks),
                                        "user_collection": user_id
                                    }
                                else:
                                    analysis["vector_storage_result"] = {
                                        "stored_successfully": False,
                                        "error": "Failed to store chunks"
                                    }
                            else:
                                analysis["vector_storage_result"] = {
                                    "stored_successfully": False,
                                    "error": "Failed to create user collection"
                                }

                        except Exception as e:
                            logger.warning(f"Vector storage failed: {e}")
                            analysis["vector_storage_result"] = {
                                "stored_successfully": False,
                                "error": str(e)
                            }
                    else:
                        analysis["vector_storage_result"] = {
                            "skipped": True,
                            "reason": "No user_id provided"
                        }

                # Step 4: Basic indexing (fallback)
                from .tools.indexing_tools import index_file
                index_result = index_file(temp_file_path)
                analysis["basic_indexing_result"] = index_result

            except ImportError as e:
                logger.warning(f"Full pipeline not available, using basic tools: {e}")
                analysis["pipeline_used"] = "basic"

                # Fallback to basic tools
                try:
                    from .tools.indexing_tools import index_file
                    from .tools.analysis_tools import analyze_complexity

                    index_result = index_file(temp_file_path)
                    analysis["indexing_result"] = index_result

                    complexity_result = analyze_complexity(temp_file_path)
                    analysis["complexity_analysis"] = complexity_result

                except ImportError as e2:
                    logger.warning(f"Basic tools not available: {e2}")
                    analysis["analysis_note"] = "No analysis tools available"

            return analysis

        except Exception as e:
            logger.error(f"Error analyzing complete file: {e}")
            return {"analysis_error": str(e)}


def add_vscode_integration(mcp_server, auth_manager=None) -> VSCodeIntegration:
    """
    Add VS Code integration to an existing MCP server.

    Args:
        mcp_server: The FastMCP server instance
        auth_manager: Optional authentication manager

    Returns:
        VSCodeIntegration instance
    """
    integration = VSCodeIntegration(mcp_server, auth_manager)
    integration.register_endpoints()

    logger.info("VS Code integration endpoints registered:")
    logger.info("  - POST /api/v1/vscode/chunks (receive code chunks)")
    logger.info("  - GET /api/v1/vscode/status (indexing status)")
    logger.info("  - POST /api/v1/vscode/cleanup (cleanup temp files)")
    logger.info("  - GET /api/v1/jobs (list user jobs)")
    logger.info("  - GET /api/v1/jobs/{id} (get job status)")
    logger.info("  - GET /api/v1/jobs/stats (job statistics)")

    return integration

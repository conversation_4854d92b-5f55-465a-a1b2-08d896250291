"""
Code chunker for splitting code files into meaningful chunks with rich metadata.
Extracts functions, classes, imports, and other structural information.
"""

import ast
import logging
import re
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Union

logger = logging.getLogger(__name__)


class CodeChunk:
    """Represents a chunk of code with metadata."""
    
    def __init__(
        self,
        content: str,
        file_path: str,
        start_line: int,
        end_line: int,
        chunk_type: str = "code",
        **metadata
    ):
        self.content = content
        self.file_path = file_path
        self.start_line = start_line
        self.end_line = end_line
        self.chunk_type = chunk_type
        self.metadata = metadata
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert chunk to dictionary for storage."""
        return {
            'content': self.content,
            'file_path': self.file_path,
            'start_line': self.start_line,
            'end_line': self.end_line,
            'chunk_type': self.chunk_type,
            'timestamp': datetime.utcnow().isoformat(),
            **self.metadata
        }


class PythonCodeChunker:
    """Chunker specifically for Python code files."""
    
    def __init__(self, max_chunk_size: int = 1000):
        self.max_chunk_size = max_chunk_size
    
    def chunk_file(self, file_path: str) -> List[CodeChunk]:
        """
        Chunk a Python file into meaningful segments.
        
        Args:
            file_path: Path to the Python file
            
        Returns:
            List of code chunks with metadata
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return self.chunk_content(content, file_path)
        except Exception as e:
            logger.error(f"Failed to chunk file {file_path}: {e}")
            return []
    
    def chunk_content(self, content: str, file_path: str) -> List[CodeChunk]:
        """
        Chunk Python content into meaningful segments.
        
        Args:
            content: Python source code
            file_path: Path to the source file
            
        Returns:
            List of code chunks
        """
        chunks = []
        
        try:
            # Parse the AST
            tree = ast.parse(content)
            lines = content.split('\n')
            
            # Extract imports first
            imports = self._extract_imports(tree)
            
            # Process top-level nodes
            for node in ast.walk(tree):
                if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    chunk = self._create_function_chunk(
                        node, lines, file_path, imports
                    )
                    if chunk:
                        chunks.append(chunk)
                
                elif isinstance(node, ast.ClassDef):
                    chunk = self._create_class_chunk(
                        node, lines, file_path, imports
                    )
                    if chunk:
                        chunks.append(chunk)
            
            # Handle remaining code (module-level code)
            module_chunks = self._create_module_chunks(
                tree, lines, file_path, imports, chunks
            )
            chunks.extend(module_chunks)
            
        except SyntaxError as e:
            logger.warning(f"Syntax error in {file_path}: {e}")
            # Fall back to simple line-based chunking
            chunks = self._fallback_chunk(content, file_path)
        
        return chunks
    
    def _extract_imports(self, tree: ast.AST) -> List[str]:
        """Extract import statements from AST."""
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ""
                for alias in node.names:
                    if alias.name == "*":
                        imports.append(f"{module}.*")
                    else:
                        imports.append(f"{module}.{alias.name}")
        
        return imports
    
    def _create_function_chunk(
        self,
        node: Union[ast.FunctionDef, ast.AsyncFunctionDef],
        lines: List[str],
        file_path: str,
        imports: List[str]
    ) -> Optional[CodeChunk]:
        """Create a chunk for a function definition."""
        try:
            start_line = node.lineno
            end_line = node.end_lineno or start_line
            
            # Get function content
            content = '\n'.join(lines[start_line-1:end_line])
            
            # Extract function metadata
            metadata = {
                'function_name': node.name,
                'language': 'python',
                'imports': imports,
                'parameters': [arg.arg for arg in node.args.args],
                'is_async': isinstance(node, ast.AsyncFunctionDef),
                'decorators': [self._get_decorator_name(d) for d in node.decorator_list],
                'complexity': self._calculate_complexity(node),
                'docstring': ast.get_docstring(node) or "",
                'dependencies': self._extract_dependencies(node)
            }
            
            return CodeChunk(
                content=content,
                file_path=file_path,
                start_line=start_line,
                end_line=end_line,
                chunk_type='function',
                **metadata
            )
        except Exception as e:
            logger.error(f"Failed to create function chunk: {e}")
            return None
    
    def _create_class_chunk(
        self,
        node: ast.ClassDef,
        lines: List[str],
        file_path: str,
        imports: List[str]
    ) -> Optional[CodeChunk]:
        """Create a chunk for a class definition."""
        try:
            start_line = node.lineno
            end_line = node.end_lineno or start_line
            
            # Get class content
            content = '\n'.join(lines[start_line-1:end_line])
            
            # Extract class metadata
            methods = []
            for item in node.body:
                if isinstance(item, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    methods.append(item.name)
            
            metadata = {
                'class_name': node.name,
                'language': 'python',
                'imports': imports,
                'base_classes': [self._get_base_class_name(base) for base in node.bases],
                'methods': methods,
                'decorators': [self._get_decorator_name(d) for d in node.decorator_list],
                'complexity': self._calculate_complexity(node),
                'docstring': ast.get_docstring(node) or "",
                'dependencies': self._extract_dependencies(node)
            }
            
            return CodeChunk(
                content=content,
                file_path=file_path,
                start_line=start_line,
                end_line=end_line,
                chunk_type='class',
                **metadata
            )
        except Exception as e:
            logger.error(f"Failed to create class chunk: {e}")
            return None
    
    def _create_module_chunks(
        self,
        tree: ast.AST,
        lines: List[str],
        file_path: str,
        imports: List[str],
        existing_chunks: List[CodeChunk]
    ) -> List[CodeChunk]:
        """Create chunks for module-level code."""
        chunks = []
        
        # Get lines that are already covered by existing chunks
        covered_lines = set()
        for chunk in existing_chunks:
            for line_num in range(chunk.start_line, chunk.end_line + 1):
                covered_lines.add(line_num)
        
        # Find uncovered code segments
        current_segment = []
        current_start = None
        
        for i, line in enumerate(lines, 1):
            if i not in covered_lines and line.strip():
                if current_start is None:
                    current_start = i
                current_segment.append(line)
            else:
                if current_segment:
                    # Create chunk for accumulated segment
                    content = '\n'.join(current_segment)
                    if len(content.strip()) > 10:  # Only create meaningful chunks
                        chunk = CodeChunk(
                            content=content,
                            file_path=file_path,
                            start_line=current_start,
                            end_line=current_start + len(current_segment) - 1,
                            chunk_type='module',
                            language='python',
                            imports=imports,
                            dependencies=[]
                        )
                        chunks.append(chunk)
                    
                    current_segment = []
                    current_start = None
        
        # Handle final segment
        if current_segment:
            content = '\n'.join(current_segment)
            if len(content.strip()) > 10:
                chunk = CodeChunk(
                    content=content,
                    file_path=file_path,
                    start_line=current_start,
                    end_line=current_start + len(current_segment) - 1,
                    chunk_type='module',
                    language='python',
                    imports=imports,
                    dependencies=[]
                )
                chunks.append(chunk)
        
        return chunks
    
    def _get_decorator_name(self, decorator: ast.expr) -> str:
        """Extract decorator name from AST node."""
        if isinstance(decorator, ast.Name):
            return decorator.id
        elif isinstance(decorator, ast.Attribute):
            return f"{decorator.attr}"
        elif isinstance(decorator, ast.Call):
            if isinstance(decorator.func, ast.Name):
                return decorator.func.id
            elif isinstance(decorator.func, ast.Attribute):
                return decorator.func.attr
        return "unknown"
    
    def _get_base_class_name(self, base: ast.expr) -> str:
        """Extract base class name from AST node."""
        if isinstance(base, ast.Name):
            return base.id
        elif isinstance(base, ast.Attribute):
            return f"{base.attr}"
        return "unknown"
    
    def _calculate_complexity(self, node: ast.AST) -> int:
        """Calculate cyclomatic complexity of a code node."""
        complexity = 1  # Base complexity
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1
        
        return complexity
    
    def _extract_dependencies(self, node: ast.AST) -> List[str]:
        """Extract function/class dependencies from AST node."""
        dependencies = set()
        
        for child in ast.walk(node):
            if isinstance(child, ast.Call):
                if isinstance(child.func, ast.Name):
                    dependencies.add(child.func.id)
                elif isinstance(child.func, ast.Attribute):
                    dependencies.add(child.func.attr)
        
        return list(dependencies)
    
    def _fallback_chunk(self, content: str, file_path: str) -> List[CodeChunk]:
        """Fallback chunking for files with syntax errors."""
        chunks = []
        lines = content.split('\n')
        
        # Simple line-based chunking
        chunk_size = min(self.max_chunk_size // 50, 50)  # ~50 lines per chunk
        
        for i in range(0, len(lines), chunk_size):
            chunk_lines = lines[i:i + chunk_size]
            chunk_content = '\n'.join(chunk_lines)
            
            if chunk_content.strip():
                chunk = CodeChunk(
                    content=chunk_content,
                    file_path=file_path,
                    start_line=i + 1,
                    end_line=min(i + chunk_size, len(lines)),
                    chunk_type='fallback',
                    language='python',
                    imports=[],
                    dependencies=[]
                )
                chunks.append(chunk)
        
        return chunks


class GenericCodeChunker:
    """Generic chunker for non-Python files."""
    
    def __init__(self, max_chunk_size: int = 1000):
        self.max_chunk_size = max_chunk_size
    
    def chunk_file(self, file_path: str) -> List[CodeChunk]:
        """
        Chunk a generic code file.
        
        Args:
            file_path: Path to the code file
            
        Returns:
            List of code chunks
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return self.chunk_content(content, file_path)
        except Exception as e:
            logger.error(f"Failed to chunk file {file_path}: {e}")
            return []
    
    def chunk_content(self, content: str, file_path: str) -> List[CodeChunk]:
        """
        Chunk generic content into segments.
        
        Args:
            content: Source code content
            file_path: Path to the source file
            
        Returns:
            List of code chunks
        """
        chunks = []
        lines = content.split('\n')
        language = self._detect_language(file_path)
        
        # Simple function-based chunking for common languages
        if language in ['javascript', 'typescript', 'java', 'cpp', 'c']:
            chunks = self._chunk_by_functions(content, file_path, language)
        else:
            # Line-based chunking for other files
            chunks = self._chunk_by_lines(content, file_path, language)
        
        return chunks
    
    def _detect_language(self, file_path: str) -> str:
        """Detect programming language from file extension."""
        ext = Path(file_path).suffix.lower()
        
        language_map = {
            '.js': 'javascript',
            '.ts': 'typescript',
            '.jsx': 'javascript',
            '.tsx': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.cc': 'cpp',
            '.cxx': 'cpp',
            '.c': 'c',
            '.h': 'c',
            '.hpp': 'cpp',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby',
            '.cs': 'csharp',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.json': 'json',
            '.xml': 'xml',
            '.html': 'html',
            '.css': 'css',
            '.sql': 'sql',
            '.sh': 'bash',
            '.bash': 'bash',
            '.ps1': 'powershell',
            '.dockerfile': 'dockerfile',
            '.tf': 'terraform',
            '.md': 'markdown'
        }
        
        return language_map.get(ext, 'text')
    
    def _chunk_by_functions(
        self, 
        content: str, 
        file_path: str, 
        language: str
    ) -> List[CodeChunk]:
        """Chunk code by function definitions."""
        chunks = []
        lines = content.split('\n')
        
        # Simple regex patterns for function detection
        function_patterns = {
            'javascript': r'^\s*(function\s+\w+|const\s+\w+\s*=|let\s+\w+\s*=|var\s+\w+\s*=).*{',
            'typescript': r'^\s*(function\s+\w+|const\s+\w+\s*=|let\s+\w+\s*=).*{',
            'java': r'^\s*(public|private|protected)?\s*(static)?\s*\w+\s+\w+\s*\(',
            'cpp': r'^\s*\w+\s+\w+\s*\(',
            'c': r'^\s*\w+\s+\w+\s*\('
        }
        
        pattern = function_patterns.get(language)
        if not pattern:
            return self._chunk_by_lines(content, file_path, language)
        
        current_chunk = []
        current_start = None
        brace_count = 0
        in_function = False
        
        for i, line in enumerate(lines, 1):
            if re.match(pattern, line) and not in_function:
                # Start of new function
                if current_chunk:
                    # Save previous chunk
                    chunk_content = '\n'.join(current_chunk)
                    if chunk_content.strip():
                        chunk = CodeChunk(
                            content=chunk_content,
                            file_path=file_path,
                            start_line=current_start,
                            end_line=i - 1,
                            chunk_type='function',
                            language=language,
                            imports=[],
                            dependencies=[]
                        )
                        chunks.append(chunk)
                
                current_chunk = [line]
                current_start = i
                in_function = True
                brace_count = line.count('{') - line.count('}')
            
            elif in_function:
                current_chunk.append(line)
                brace_count += line.count('{') - line.count('}')
                
                if brace_count <= 0:
                    # End of function
                    chunk_content = '\n'.join(current_chunk)
                    chunk = CodeChunk(
                        content=chunk_content,
                        file_path=file_path,
                        start_line=current_start,
                        end_line=i,
                        chunk_type='function',
                        language=language,
                        imports=[],
                        dependencies=[]
                    )
                    chunks.append(chunk)
                    
                    current_chunk = []
                    current_start = None
                    in_function = False
                    brace_count = 0
            
            else:
                # Module-level code
                if current_chunk and current_start:
                    current_chunk.append(line)
                elif line.strip():
                    current_chunk = [line]
                    current_start = i
        
        # Handle final chunk
        if current_chunk:
            chunk_content = '\n'.join(current_chunk)
            if chunk_content.strip():
                chunk = CodeChunk(
                    content=chunk_content,
                    file_path=file_path,
                    start_line=current_start,
                    end_line=len(lines),
                    chunk_type='module' if not in_function else 'function',
                    language=language,
                    imports=[],
                    dependencies=[]
                )
                chunks.append(chunk)
        
        return chunks
    
    def _chunk_by_lines(
        self, 
        content: str, 
        file_path: str, 
        language: str
    ) -> List[CodeChunk]:
        """Chunk content by line count."""
        chunks = []
        lines = content.split('\n')
        
        chunk_size = min(self.max_chunk_size // 20, 100)  # ~100 lines per chunk
        
        for i in range(0, len(lines), chunk_size):
            chunk_lines = lines[i:i + chunk_size]
            chunk_content = '\n'.join(chunk_lines)
            
            if chunk_content.strip():
                chunk = CodeChunk(
                    content=chunk_content,
                    file_path=file_path,
                    start_line=i + 1,
                    end_line=min(i + chunk_size, len(lines)),
                    chunk_type='segment',
                    language=language,
                    imports=[],
                    dependencies=[]
                )
                chunks.append(chunk)
        
        return chunks


class CodeChunkerFactory:
    """Factory for creating appropriate code chunkers."""
    
    @staticmethod
    def create_chunker(file_path: str, max_chunk_size: int = 1000):
        """
        Create appropriate chunker for the file type.
        
        Args:
            file_path: Path to the file
            max_chunk_size: Maximum chunk size in characters
            
        Returns:
            Appropriate chunker instance
        """
        ext = Path(file_path).suffix.lower()
        
        if ext == '.py':
            return PythonCodeChunker(max_chunk_size)
        else:
            return GenericCodeChunker(max_chunk_size)
    
    @staticmethod
    def chunk_file(file_path: str, max_chunk_size: int = 1000) -> List[CodeChunk]:
        """
        Convenience method to chunk a file.
        
        Args:
            file_path: Path to the file
            max_chunk_size: Maximum chunk size in characters
            
        Returns:
            List of code chunks
        """
        chunker = CodeChunkerFactory.create_chunker(file_path, max_chunk_size)
        return chunker.chunk_file(file_path)
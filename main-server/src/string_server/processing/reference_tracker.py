"""
Reference tracker for finding all references to symbols across a Python project.
This is critical for safe refactoring - knowing where a symbol is used before changing it.
"""

import logging
from dataclasses import dataclass, field
from pathlib import Path
from typing import List

from tree_sitter import Node

try:
    from .code_indexer import CodeIndexer, FunctionInfo, ClassInfo
    from .metadata_extractor import ProjectMetadata
except ImportError:
    from code_indexer import CodeIndexer, FunctionInfo, ClassInfo
    from metadata_extractor import ProjectMetadata


@dataclass
class Reference:
    """A reference to a symbol (function, class, variable)."""
    file_path: str
    line: int
    column: int
    context_line: str  # The actual line of code
    reference_type: str  # 'call', 'assignment', 'import', 'instantiation', etc.
    symbol_name: str


@dataclass
class ReferenceResult:
    """Result of finding all references to a symbol."""
    symbol_name: str
    symbol_type: str  # 'function', 'class', 'variable'
    total_references: int
    references: List[Reference] = field(default_factory=list)
    definitions: List[Reference] = field(default_factory=list)  # Where the symbol is defined


class ReferenceTracker:
    """Tracks all references to symbols across a Python project."""
    
    def __init__(self, indexer: CodeIndexer):
        self.indexer = indexer
    
    def find_all_references(self, symbol_name: str, project_metadata: ProjectMetadata) -> ReferenceResult:
        """Find all references to a symbol across the entire project."""
        result = ReferenceResult(
            symbol_name=symbol_name,
            symbol_type=self._determine_symbol_type(symbol_name, project_metadata),
            total_references=0
        )
        
        # Search through all files
        for file_path, metadata in project_metadata.files.items():
            file_references = self._find_references_in_file(
                symbol_name, 
                file_path, 
                metadata, 
                project_metadata
            )
            result.references.extend(file_references)
        
        # Find definitions
        result.definitions = self._find_definitions(symbol_name, project_metadata)
        
        result.total_references = len(result.references)
        return result
    
    def _determine_symbol_type(self, symbol_name: str, project_metadata: ProjectMetadata) -> str:
        """Determine if symbol is a function, class, or variable."""
        # Check if it's a function
        if project_metadata.find_function(symbol_name):
            return "function"
        
        # Check if it's a class
        if project_metadata.find_class(symbol_name):
            return "class"
        
        # Otherwise assume it's a variable
        return "variable"
    
    def _find_references_in_file(self, symbol_name: str, file_path: str, metadata, project_metadata: ProjectMetadata) -> List[Reference]:
        """Find all references to a symbol in a specific file."""
        references = []
        
        try:
            # Read the file content
            full_path = Path(project_metadata.project_path) / file_path
            with open(full_path, 'r', encoding='utf-8') as f:
                code = f.read()
                lines = code.split('\n')
            
            # Parse with tree-sitter
            root_node = self.indexer.parse_code(code)
            if not root_node:
                return references
            
            # Search for references
            self._search_node_for_references(root_node, symbol_name, code, lines, file_path, references)
            
        except Exception as e:
            logging.error(f"Failed to find references in {file_path}: {e}")
        
        return references
    
    def _search_node_for_references(self, node: Node, symbol_name: str, code: str, lines: List[str], 
                                   file_path: str, references: List[Reference]):
        """Recursively search AST nodes for references to the symbol."""
        # Check if this node is an identifier matching our symbol
        if node.type == "identifier":
            node_text = code[node.start_byte:node.end_byte]
            if node_text == symbol_name:
                line_num = node.start_point[0] + 1
                column = node.start_point[1]
                context_line = lines[node.start_point[0]] if node.start_point[0] < len(lines) else ""
                
                # Determine reference type based on parent context
                ref_type = self._determine_reference_type(node, code)
                
                reference = Reference(
                    file_path=file_path,
                    line=line_num,
                    column=column,
                    context_line=context_line.strip(),
                    reference_type=ref_type,
                    symbol_name=symbol_name
                )
                references.append(reference)
        
        # Recursively search children
        for child in node.children:
            self._search_node_for_references(child, symbol_name, code, lines, file_path, references)
    
    def _determine_reference_type(self, node: Node, code: str) -> str:
        """Determine the type of reference based on context."""
        parent = node.parent
        if not parent:
            return "reference"
        
        # Function call
        if parent.type == "call":
            return "function_call"
        
        # Import statement
        if parent.type in ["import_statement", "import_from_statement"]:
            return "import"
        
        # Assignment
        if parent.type == "assignment":
            return "assignment"
        
        # Attribute access (obj.method)
        if parent.type == "attribute":
            return "attribute_access"
        
        # Class instantiation (similar to function call but we can detect via context)
        if parent.type == "call":
            # Check if the called identifier starts with uppercase (likely a class)
            identifier_text = code[node.start_byte:node.end_byte]
            if identifier_text and identifier_text[0].isupper():
                return "class_instantiation"
            return "function_call"
        
        return "reference"
    
    def _find_definitions(self, symbol_name: str, project_metadata: ProjectMetadata) -> List[Reference]:
        """Find where a symbol is defined."""
        definitions = []
        
        # Check functions
        func = project_metadata.find_function(symbol_name)
        if func:
            # Find the file containing this function
            for file_path, metadata in project_metadata.files.items():
                if func in metadata.functions:
                    definitions.append(Reference(
                        file_path=file_path,
                        line=func.start_line,
                        column=0,
                        context_line=f"def {func.name}({', '.join(func.parameters)}):",
                        reference_type="function_definition",
                        symbol_name=symbol_name
                    ))
                    break
                for cls in metadata.classes:
                    if func in cls.methods:
                        definitions.append(Reference(
                            file_path=file_path,
                            line=func.start_line,
                            column=0,
                            context_line=f"def {func.name}({', '.join(func.parameters)}):",
                            reference_type="method_definition",
                            symbol_name=symbol_name
                        ))
                        break
        
        # Check classes
        cls = project_metadata.find_class(symbol_name)
        if cls:
            for file_path, metadata in project_metadata.files.items():
                if cls in metadata.classes:
                    definitions.append(Reference(
                        file_path=file_path,
                        line=cls.start_line,
                        column=0,
                        context_line=f"class {cls.name}({', '.join(cls.base_classes)}):",
                        reference_type="class_definition",
                        symbol_name=symbol_name
                    ))
                    break
        
        return definitions
    
    def find_function_callers(self, function_name: str, project_metadata: ProjectMetadata) -> List[Reference]:
        """Find all places where a specific function is called."""
        result = self.find_all_references(function_name, project_metadata)
        return [ref for ref in result.references if ref.reference_type in ["function_call", "method_call"]]
    
    def find_class_usages(self, class_name: str, project_metadata: ProjectMetadata) -> List[Reference]:
        """Find all places where a class is used (instantiated, imported, etc.)."""
        result = self.find_all_references(class_name, project_metadata)
        return [ref for ref in result.references if ref.reference_type in ["class_instantiation", "import", "reference"]]
    
    def get_reference_summary(self, symbol_name: str, project_metadata: ProjectMetadata) -> str:
        """Get a formatted summary of all references to a symbol."""
        result = self.find_all_references(symbol_name, project_metadata)
        
        if result.total_references == 0:
            return f"No references found for '{symbol_name}'"
        
        summary = f"References to '{symbol_name}' ({result.symbol_type}):\n\n"
        
        # Definitions
        if result.definitions:
            summary += "Definitions:\n"
            for defn in result.definitions:
                summary += f"  📍 {defn.file_path}:{defn.line} - {defn.context_line}\n"
            summary += "\n"
        
        # Group references by file
        refs_by_file = {}
        for ref in result.references:
            if ref.file_path not in refs_by_file:
                refs_by_file[ref.file_path] = []
            refs_by_file[ref.file_path].append(ref)
        
        summary += f"References ({result.total_references} total):\n"
        for file_path, refs in refs_by_file.items():
            summary += f"  📄 {file_path} ({len(refs)} references):\n"
            for ref in sorted(refs, key=lambda r: r.line):
                icon = self._get_reference_icon(ref.reference_type)
                summary += f"    {icon} Line {ref.line}: {ref.context_line}\n"
            summary += "\n"
        
        return summary
    
    def _get_reference_icon(self, ref_type: str) -> str:
        """Get an icon for the reference type."""
        icons = {
            "function_call": "📞",
            "method_call": "📞", 
            "class_instantiation": "🏗️",
            "import": "📦",
            "assignment": "📝",
            "attribute_access": "🔗",
            "reference": "👁️"
        }
        return icons.get(ref_type, "❓") 
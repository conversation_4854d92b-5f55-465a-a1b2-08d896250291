"""
Call graph builder for analyzing function and method relationships in Python code.
"""

import logging
from typing import Dict, List, Set, Optional, Tuple
from dataclasses import dataclass, field
import networkx as nx
from tree_sitter import Node

try:
    from .code_indexer import CodeIndexer, FunctionInfo, ClassInfo
except ImportError:
    from code_indexer import CodeIndexer, FunctionInfo, ClassInfo


@dataclass
class CallRelation:
    """Represents a function call relationship."""
    caller: str  # full name of calling function
    callee: str  # full name of called function
    line: int    # line number where call occurs
    call_type: str = "function_call"  # function_call, method_call, etc.


@dataclass
class CallGraphNode:
    """Node in the call graph representing a function or method."""
    name: str
    full_name: str
    function_info: FunctionInfo
    calls: Set[str] = field(default_factory=set)  # functions this one calls
    called_by: Set[str] = field(default_factory=set)  # functions that call this one
    
    def add_call(self, callee: str):
        """Add a function that this function calls."""
        self.calls.add(callee)
    
    def add_caller(self, caller: str):
        """Add a function that calls this function."""
        self.called_by.add(caller)


class CallGraphBuilder:
    """Builds call graphs for Python code using tree-sitter analysis."""
    
    def __init__(self, code_indexer: CodeIndexer):
        self.indexer = code_indexer
        self.call_graph = nx.DiGraph()
        self.nodes: Dict[str, CallGraphNode] = {}
        self.relations: List[CallRelation] = []
    
    def build_call_graph(self, code: str, file_path: str = "<string>") -> nx.DiGraph:
        """Build a complete call graph for the given code."""
        # Parse the code
        root_node = self.indexer.parse_code(code)
        if not root_node:
            logging.error(f"Failed to parse code for call graph: {file_path}")
            return self.call_graph
        
        # Extract all functions and classes
        functions = self.indexer.extract_functions(root_node, code)
        classes = self.indexer.extract_classes(root_node, code)
        
        # Add all functions to the graph
        self._add_functions_to_graph(functions)
        
        # Add class methods to the graph
        for class_info in classes:
            self._add_functions_to_graph(class_info.methods)
        
        # Analyze function calls
        self._analyze_calls(root_node, code)
        
        # Build the NetworkX graph
        self._build_networkx_graph()
        
        return self.call_graph
    
    def _add_functions_to_graph(self, functions: List[FunctionInfo]):
        """Add functions to the call graph nodes."""
        for func in functions:
            node = CallGraphNode(
                name=func.name,
                full_name=func.full_name,
                function_info=func
            )
            self.nodes[func.full_name] = node
    
    def _analyze_calls(self, node: Node, code: str, current_function: Optional[str] = None):
        """Recursively analyze function calls in the AST."""
        # Track current function context
        if node.type == "function_definition":
            # Get function name
            func_name = self._get_function_name_from_node(node, code)
            if func_name:
                current_function = func_name
        
        # Look for function calls
        if node.type == "call":
            if current_function:
                callee = self._extract_call_target(node, code)
                if callee:
                    line = node.start_point[0] + 1
                    self._add_call_relation(current_function, callee, line)
        
        # Recursively analyze children
        for child in node.children:
            self._analyze_calls(child, code, current_function)
    
    def _get_function_name_from_node(self, node: Node, code: str) -> Optional[str]:
        """Extract function name from a function definition node."""
        for child in node.children:
            if child.type == "identifier":
                name = self._get_node_text(child, code)
                # Check if this is a method (inside a class)
                class_name = self._find_containing_class(node, code)
                if class_name:
                    return f"{class_name}.{name}"
                return name
        return None
    
    def _find_containing_class(self, node: Node, code: str) -> Optional[str]:
        """Find the class that contains this node, if any."""
        current = node.parent
        while current:
            if current.type == "class_definition":
                for child in current.children:
                    if child.type == "identifier":
                        return self._get_node_text(child, code)
            current = current.parent
        return None
    
    def _extract_call_target(self, call_node: Node, code: str) -> Optional[str]:
        """Extract the target function/method being called."""
        # Look for the function being called
        for child in call_node.children:
            if child.type == "identifier":
                # Simple function call: func()
                return self._get_node_text(child, code)
            elif child.type == "attribute":
                # Method call: obj.method()
                return self._extract_attribute_call(child, code)
        return None
    
    def _extract_attribute_call(self, attr_node: Node, code: str) -> Optional[str]:
        """Extract method call from attribute node (obj.method)."""
        parts = []
        
        # Get the full attribute chain
        for child in attr_node.children:
            if child.type == "identifier":
                parts.append(self._get_node_text(child, code))
        
        if len(parts) >= 2:
            # For obj.method(), we're interested in the method name
            # But we need to resolve what 'obj' refers to
            method_name = parts[-1]
            
            # For now, return just the method name
            # TODO: Implement more sophisticated resolution
            return method_name
        
        return None
    
    def _add_call_relation(self, caller: str, callee: str, line: int):
        """Add a call relationship between two functions."""
        # Create relation
        relation = CallRelation(
            caller=caller,
            callee=callee,
            line=line
        )
        self.relations.append(relation)
        
        # Update nodes if they exist
        if caller in self.nodes:
            self.nodes[caller].add_call(callee)
        
        if callee in self.nodes:
            self.nodes[callee].add_caller(caller)
    
    def _build_networkx_graph(self):
        """Build the NetworkX graph from the collected data."""
        # Add nodes
        for full_name, node in self.nodes.items():
            self.call_graph.add_node(
                full_name,
                name=node.name,
                function_info=node.function_info,
                calls=list(node.calls),
                called_by=list(node.called_by)
            )
        
        # Add edges
        for relation in self.relations:
            self.call_graph.add_edge(
                relation.caller,
                relation.callee,
                line=relation.line,
                call_type=relation.call_type
            )
    
    def _get_node_text(self, node: Node, code: str) -> str:
        """Get the text content of a node."""
        return code[node.start_byte:node.end_byte]
    
    def get_function_dependencies(self, function_name: str) -> Dict[str, List[str]]:
        """Get all dependencies for a function (what it calls and what calls it)."""
        if function_name not in self.nodes:
            return {"calls": [], "called_by": []}
        
        node = self.nodes[function_name]
        return {
            "calls": list(node.calls),
            "called_by": list(node.called_by)
        }
    
    def get_call_chain(self, start_function: str, max_depth: int = 5) -> List[List[str]]:
        """Get call chains starting from a function."""
        if start_function not in self.call_graph:
            return []
        
        chains = []
        
        def dfs(current: str, path: List[str], depth: int):
            if depth >= max_depth:
                return
            
            path = path + [current]
            
            # Get all functions this one calls
            successors = list(self.call_graph.successors(current))
            
            if not successors:
                # End of chain
                chains.append(path)
            else:
                for successor in successors:
                    if successor not in path:  # Avoid cycles
                        dfs(successor, path, depth + 1)
        
        dfs(start_function, [], 0)
        return chains
    
    def get_reverse_call_chain(self, target_function: str, max_depth: int = 5) -> List[List[str]]:
        """Get call chains that lead to a target function."""
        if target_function not in self.call_graph:
            return []
        
        chains = []
        
        def dfs(current: str, path: List[str], depth: int):
            if depth >= max_depth:
                return
            
            path = [current] + path
            
            # Get all functions that call this one
            predecessors = list(self.call_graph.predecessors(current))
            
            if not predecessors:
                # Start of chain
                chains.append(path)
            else:
                for predecessor in predecessors:
                    if predecessor not in path:  # Avoid cycles
                        dfs(predecessor, path, depth + 1)
        
        dfs(target_function, [], 0)
        return chains
    
    def find_related_functions(self, function_name: str, max_distance: int = 2) -> Dict[str, int]:
        """Find all functions related to the given function within max_distance."""
        if function_name not in self.call_graph:
            return {}
        
        # Use BFS to find related functions
        related = {}
        visited = set()
        queue = [(function_name, 0)]
        
        while queue:
            current, distance = queue.pop(0)
            
            if current in visited or distance > max_distance:
                continue
            
            visited.add(current)
            if current != function_name:
                related[current] = distance
            
            # Add neighbors (both callers and callees)
            neighbors = (
                list(self.call_graph.successors(current)) +
                list(self.call_graph.predecessors(current))
            )
            
            for neighbor in neighbors:
                if neighbor not in visited:
                    queue.append((neighbor, distance + 1))
        
        return related
    
    def get_graph_statistics(self) -> Dict[str, any]:
        """Get statistics about the call graph."""
        return {
            "total_functions": len(self.nodes),
            "total_calls": len(self.relations),
            "connected_components": nx.number_connected_components(self.call_graph.to_undirected()),
            "average_calls_per_function": len(self.relations) / len(self.nodes) if self.nodes else 0,
            "most_called_functions": self._get_most_called_functions(5),
            "most_calling_functions": self._get_most_calling_functions(5)
        }
    
    def _get_most_called_functions(self, limit: int) -> List[Tuple[str, int]]:
        """Get functions that are called most frequently."""
        call_counts = {}
        for node in self.nodes.values():
            call_counts[node.full_name] = len(node.called_by)
        
        return sorted(call_counts.items(), key=lambda x: x[1], reverse=True)[:limit]
    
    def _get_most_calling_functions(self, limit: int) -> List[Tuple[str, int]]:
        """Get functions that make the most calls."""
        call_counts = {}
        for node in self.nodes.values():
            call_counts[node.full_name] = len(node.calls)
        
        return sorted(call_counts.items(), key=lambda x: x[1], reverse=True)[:limit]

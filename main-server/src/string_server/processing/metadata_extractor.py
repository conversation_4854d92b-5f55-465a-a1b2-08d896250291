"""
Metadata extractor for comprehensive code analysis and context building.
"""

import ast
import fnmatch
import logging
from dataclasses import dataclass, field
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple

try:
    from .code_indexer import CodeIndexer, FunctionInfo, ClassInfo, ImportInfo
    from .call_graph_builder import CallGraphBuilder
except ImportError:
    from code_indexer import CodeIndexer, FunctionInfo, ClassInfo, ImportInfo
    from call_graph_builder import CallGraphBuilder


@dataclass
class CodeMetadata:
    """Comprehensive metadata for a code file or project."""
    file_path: str
    functions: List[FunctionInfo] = field(default_factory=list)
    classes: List[ClassInfo] = field(default_factory=list)
    imports: List[ImportInfo] = field(default_factory=list)
    global_variables: List[str] = field(default_factory=list)
    constants: List[str] = field(default_factory=list)
    complexity_metrics: Dict[str, Any] = field(default_factory=dict)
    call_graph_data: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metadata to dictionary for serialization."""
        return {
            "file_path": self.file_path,
            "functions": [self._function_to_dict(f) for f in self.functions],
            "classes": [self._class_to_dict(c) for c in self.classes],
            "imports": [self._import_to_dict(i) for i in self.imports],
            "global_variables": self.global_variables,
            "constants": self.constants,
            "complexity_metrics": self.complexity_metrics,
            "call_graph_data": self.call_graph_data
        }
    
    def _function_to_dict(self, func: FunctionInfo) -> Dict[str, Any]:
        """Convert FunctionInfo to dictionary."""
        return {
            "name": func.name,
            "full_name": func.full_name,
            "start_line": func.start_line,
            "end_line": func.end_line,
            "docstring": func.docstring,
            "parameters": func.parameters,
            "return_type": func.return_type,
            "is_method": func.is_method,
            "class_name": func.class_name,
            "decorators": func.decorators
        }
    
    def _class_to_dict(self, cls: ClassInfo) -> Dict[str, Any]:
        """Convert ClassInfo to dictionary."""
        return {
            "name": cls.name,
            "start_line": cls.start_line,
            "end_line": cls.end_line,
            "docstring": cls.docstring,
            "methods": [self._function_to_dict(m) for m in cls.methods],
            "base_classes": cls.base_classes,
            "decorators": cls.decorators
        }
    
    def _import_to_dict(self, imp: ImportInfo) -> Dict[str, Any]:
        """Convert ImportInfo to dictionary."""
        return {
            "module": imp.module,
            "alias": imp.alias,
            "items": imp.items,
            "line": imp.line
        }


@dataclass
class ProjectMetadata:
    """Metadata for an entire project."""
    project_path: str
    files: Dict[str, CodeMetadata] = field(default_factory=dict)
    global_call_graph: Dict[str, Any] = field(default_factory=dict)
    cross_file_dependencies: Dict[str, List[str]] = field(default_factory=dict)
    
    def add_file_metadata(self, file_path: str, metadata: CodeMetadata):
        """Add metadata for a file."""
        self.files[file_path] = metadata
    
    def get_all_functions(self) -> List[FunctionInfo]:
        """Get all functions across all files."""
        all_functions = []
        for metadata in self.files.values():
            all_functions.extend(metadata.functions)
            for cls in metadata.classes:
                all_functions.extend(cls.methods)
        return all_functions
    
    def get_all_classes(self) -> List[ClassInfo]:
        """Get all classes across all files."""
        all_classes = []
        for metadata in self.files.values():
            all_classes.extend(metadata.classes)
        return all_classes
    
    def find_function(self, function_name: str) -> Optional[FunctionInfo]:
        """Find a function by name across all files."""
        for func in self.get_all_functions():
            if func.name == function_name or func.full_name == function_name:
                return func
        return None
    
    def find_class(self, class_name: str) -> Optional[ClassInfo]:
        """Find a class by name across all files."""
        for cls in self.get_all_classes():
            if cls.name == class_name:
                return cls
        return None


class MetadataExtractor:
    """Extracts comprehensive metadata from Python code."""

    def __init__(self):
        self.indexer = CodeIndexer()
        self.call_graph_builder = CallGraphBuilder(self.indexer)

        # Default ignore patterns for smart filtering
        self.default_ignore_patterns = [
            # Hidden files and directories
            '.*',

            # Cache and build directories
            '__pycache__',
            '*.pyc',
            '*.pyo',
            '*.pyd',
            '.pytest_cache',
            '.coverage',
            '.tox',
            '.nox',
            'htmlcov',
            '.cache',

            # Node.js
            'node_modules',
            'npm-debug.log*',
            'yarn-debug.log*',
            'yarn-error.log*',

            # Virtual environments
            'venv',
            'env',
            '.venv',
            '.env',
            'ENV',
            'env.bak',
            'venv.bak',

            # IDE and editor files
            '.vscode',
            '.idea',
            '*.swp',
            '*.swo',
            '*~',

            # OS generated files
            '.DS_Store',
            'Thumbs.db',

            # Build and distribution
            'build',
            'dist',
            '*.egg-info',
            '.eggs',

            # Documentation builds
            'docs/_build',
            'site',

            # Temporary files
            '*.tmp',
            '*.temp',
            '*.log',

            # Database files
            '*.db',
            '*.sqlite',
            '*.sqlite3',

            # Compiled files
            '*.so',
            '*.dll',
            '*.dylib',
        ]
    
    def extract_file_metadata(self, file_path: str) -> Optional[CodeMetadata]:
        """Extract comprehensive metadata from a single Python file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            
            return self.extract_code_metadata(code, file_path)
        except Exception as e:
            logging.error(f"Failed to extract metadata from {file_path}: {e}")
            return None
    
    def extract_code_metadata(self, code: str, file_path: str = "<string>") -> Optional[CodeMetadata]:
        """Extract comprehensive metadata from code string."""
        try:
            # Parse the code
            root_node = self.indexer.parse_code(code)
            if not root_node:
                return None
            
            # Extract basic elements
            functions = self.indexer.extract_functions(root_node, code)
            classes = self.indexer.extract_classes(root_node, code)
            imports = self.indexer.extract_imports(root_node, code)
            
            # Extract global variables and constants
            global_vars, constants = self._extract_global_variables(code)
            
            # Calculate complexity metrics
            complexity_metrics = self._calculate_complexity_metrics(code, functions, classes)
            
            # Build call graph
            call_graph = self.call_graph_builder.build_call_graph(code, file_path)
            call_graph_data = self._serialize_call_graph(call_graph)
            
            return CodeMetadata(
                file_path=file_path,
                functions=functions,
                classes=classes,
                imports=imports,
                global_variables=global_vars,
                constants=constants,
                complexity_metrics=complexity_metrics,
                call_graph_data=call_graph_data
            )
        except Exception as e:
            logging.error(f"Failed to extract metadata: {e}")
            return None
    
    def extract_project_metadata(self, project_path: str, custom_ignore_patterns: Optional[List[str]] = None) -> ProjectMetadata:
        """Extract metadata from an entire Python project."""
        project_metadata = ProjectMetadata(project_path=project_path)

        # Find all Python files with smart filtering
        python_files = self._find_python_files(project_path, custom_ignore_patterns)

        # Extract metadata for each file
        for file_path in python_files:
            metadata = self.extract_file_metadata(file_path)
            if metadata:
                relative_path = str(Path(file_path).relative_to(project_path))
                project_metadata.add_file_metadata(relative_path, metadata)

        # Build cross-file dependencies
        project_metadata.cross_file_dependencies = self._build_cross_file_dependencies(project_metadata)

        return project_metadata

    def discover_and_index_workspace(self, workspace_path: str, custom_ignore_patterns: Optional[List[str]] = None) -> Dict[str, ProjectMetadata]:
        """Dynamically discover and index all Python projects in a workspace."""
        workspace_dir = Path(workspace_path)
        projects = {}

        if not workspace_dir.exists():
            logging.error(f"Workspace path does not exist: {workspace_path}")
            return projects

        # Find all potential Python projects
        project_dirs = self._discover_python_projects(workspace_dir)

        logging.info(f"Discovered {len(project_dirs)} Python projects in workspace")

        # Index each project
        for project_dir in project_dirs:
            try:
                project_name = project_dir.name
                logging.info(f"Indexing project: {project_name}")

                project_metadata = self.extract_project_metadata(str(project_dir), custom_ignore_patterns)
                projects[project_name] = project_metadata

                logging.info(f"Successfully indexed {project_name}: {len(project_metadata.files)} files")
            except Exception as e:
                logging.error(f"Failed to index project {project_dir}: {e}")

        return projects

    def _discover_python_projects(self, workspace_dir: Path) -> List[Path]:
        """Discover Python projects in a workspace directory."""
        projects = []

        # Look for directories that contain Python files or project indicators
        for item in workspace_dir.iterdir():
            if not item.is_dir():
                continue

            # Skip hidden directories and common non-project directories
            if item.name.startswith('.') or item.name in ['node_modules', '__pycache__']:
                continue

            # Check if this looks like a Python project
            if self._is_python_project(item):
                projects.append(item)

        return projects

    def _is_python_project(self, directory: Path) -> bool:
        """Check if a directory contains a Python project."""
        # Project indicators
        project_files = [
            'setup.py', 'pyproject.toml', 'requirements.txt',
            'Pipfile', 'poetry.lock', 'environment.yml',
            'main.py', 'app.py', '__init__.py'
        ]

        # Check for project files
        for project_file in project_files:
            if (directory / project_file).exists():
                return True

        # Check if it contains Python files
        python_files = list(directory.glob('*.py'))
        if python_files:
            return True

        # Check subdirectories for Python files (but not too deep)
        for subdir in directory.iterdir():
            if subdir.is_dir() and not subdir.name.startswith('.'):
                if list(subdir.glob('*.py')):
                    return True

        return False
    
    def _find_python_files(self, project_path: str, custom_ignore_patterns: Optional[List[str]] = None) -> List[str]:
        """Find all Python files in a project directory with smart filtering."""
        python_files = []
        project_dir = Path(project_path)

        # Combine default and custom ignore patterns
        ignore_patterns = self.default_ignore_patterns.copy()
        if custom_ignore_patterns:
            ignore_patterns.extend(custom_ignore_patterns)

        # Load .gitignore patterns if available
        gitignore_patterns = self._load_gitignore_patterns(project_dir)
        ignore_patterns.extend(gitignore_patterns)

        # Find all Python files
        for file_path in project_dir.rglob("*.py"):
            relative_path = file_path.relative_to(project_dir)

            # Check if file should be ignored
            if self._should_ignore_path(relative_path, ignore_patterns):
                continue

            python_files.append(str(file_path))

        logging.info(f"Found {len(python_files)} Python files in {project_path}")
        return python_files

    def _load_gitignore_patterns(self, project_dir: Path) -> List[str]:
        """Load patterns from .gitignore file if it exists."""
        gitignore_path = project_dir / '.gitignore'
        patterns = []

        if gitignore_path.exists():
            try:
                with open(gitignore_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        # Skip empty lines and comments
                        if line and not line.startswith('#'):
                            patterns.append(line)
                logging.info(f"Loaded {len(patterns)} patterns from .gitignore")
            except Exception as e:
                logging.warning(f"Failed to read .gitignore: {e}")

        return patterns

    def _should_ignore_path(self, path: Path, ignore_patterns: List[str]) -> bool:
        """Check if a path should be ignored based on patterns."""
        path_str = str(path)
        path_parts = path.parts

        for pattern in ignore_patterns:
            # Check full path match
            if fnmatch.fnmatch(path_str, pattern):
                return True

            # Check if any part of the path matches
            for part in path_parts:
                if fnmatch.fnmatch(part, pattern):
                    return True

            # Check directory patterns (ending with /)
            if pattern.endswith('/'):
                dir_pattern = pattern[:-1]
                for part in path_parts[:-1]:  # Exclude filename
                    if fnmatch.fnmatch(part, dir_pattern):
                        return True

        return False
    
    def _extract_global_variables(self, code: str) -> Tuple[List[str], List[str]]:
        """Extract global variables and constants from code."""
        try:
            tree = ast.parse(code)
            global_vars = []
            constants = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Assign):
                    # Check if this is a top-level assignment
                    if isinstance(node.targets[0], ast.Name):
                        var_name = node.targets[0].id
                        
                        # Check if it's a constant (all uppercase)
                        if var_name.isupper():
                            constants.append(var_name)
                        else:
                            global_vars.append(var_name)
            
            return global_vars, constants
        except Exception as e:
            logging.error(f"Failed to extract global variables: {e}")
            return [], []
    
    def _calculate_complexity_metrics(self, code: str, functions: List[FunctionInfo], classes: List[ClassInfo]) -> Dict[str, Any]:
        """Calculate various complexity metrics for the code."""
        try:
            lines = code.split('\n')
            
            metrics = {
                "total_lines": len(lines),
                "non_empty_lines": len([line for line in lines if line.strip()]),
                "comment_lines": len([line for line in lines if line.strip().startswith('#')]),
                "function_count": len(functions),
                "class_count": len(classes),
                "average_function_length": 0,
                "max_function_length": 0,
                "cyclomatic_complexity": self._calculate_cyclomatic_complexity(code)
            }
            
            # Calculate function length metrics
            if functions:
                function_lengths = [func.end_line - func.start_line + 1 for func in functions]
                metrics["average_function_length"] = int(sum(function_lengths) / len(function_lengths))
                metrics["max_function_length"] = max(function_lengths)
            
            return metrics
        except Exception as e:
            logging.error(f"Failed to calculate complexity metrics: {e}")
            return {}
    
    def _calculate_cyclomatic_complexity(self, code: str) -> int:
        """Calculate cyclomatic complexity of the code."""
        try:
            # Simple approximation: count decision points
            decision_keywords = ['if', 'elif', 'while', 'for', 'except', 'and', 'or']
            complexity = 1  # Base complexity
            
            for line in code.split('\n'):
                line = line.strip()
                for keyword in decision_keywords:
                    complexity += line.count(keyword)
            
            return complexity
        except Exception as e:
            logging.error(f"Failed to calculate cyclomatic complexity: {e}")
            return 1
    
    def _serialize_call_graph(self, call_graph) -> Dict[str, Any]:
        """Serialize call graph data for storage."""
        try:
            nodes_data = {}
            edges_data = []
            
            for node in call_graph.nodes():
                node_data = call_graph.nodes[node]
                nodes_data[node] = {
                    "name": node_data.get("name", ""),
                    "calls": node_data.get("calls", []),
                    "called_by": node_data.get("called_by", [])
                }
            
            for edge in call_graph.edges():
                edge_data = call_graph.edges[edge]
                edges_data.append({
                    "caller": edge[0],
                    "callee": edge[1],
                    "line": edge_data.get("line", 0),
                    "call_type": edge_data.get("call_type", "function_call")
                })
            
            return {
                "nodes": nodes_data,
                "edges": edges_data
            }
        except Exception as e:
            logging.error(f"Failed to serialize call graph: {e}")
            return {}
    
    def _build_cross_file_dependencies(self, project_metadata: ProjectMetadata) -> Dict[str, List[str]]:
        """Build cross-file dependencies based on imports."""
        dependencies = {}
        
        for file_path, metadata in project_metadata.files.items():
            file_deps = []
            
            for import_info in metadata.imports:
                # Try to resolve import to actual files in the project
                resolved_files = self._resolve_import_to_files(import_info, project_metadata)
                file_deps.extend(resolved_files)
            
            dependencies[file_path] = list(set(file_deps))  # Remove duplicates
        
        return dependencies
    
    def _resolve_import_to_files(self, import_info: ImportInfo, project_metadata: ProjectMetadata) -> List[str]:
        """Resolve an import statement to actual files in the project."""
        resolved_files = []
        
        # Simple resolution: look for files that match the module name
        module_parts = import_info.module.split('.')
        
        for file_path in project_metadata.files.keys():
            file_parts = Path(file_path).with_suffix('').parts
            
            # Check if this file could be the imported module
            if len(file_parts) >= len(module_parts):
                if file_parts[-len(module_parts):] == tuple(module_parts):
                    resolved_files.append(file_path)
        
        return resolved_files
    
    def get_function_context(self, function_name: str, project_metadata: ProjectMetadata, max_depth: int = 2) -> Dict[str, Any]:
        """Get comprehensive context for a function including related functions."""
        function_info = project_metadata.find_function(function_name)
        if not function_info:
            return {}
        
        # Find the file containing this function
        containing_file = None
        for file_path, metadata in project_metadata.files.items():
            if function_info in metadata.functions:
                containing_file = file_path
                break
            for cls in metadata.classes:
                if function_info in cls.methods:
                    containing_file = file_path
                    break
        
        if not containing_file:
            return {}
        
        # Get call graph data for this file
        file_metadata = project_metadata.files[containing_file]
        call_graph_data = file_metadata.call_graph_data
        
        # Convert function info to dictionary safely
        function_dict = {
            "name": function_info.name,
            "full_name": function_info.full_name,
            "start_line": function_info.start_line,
            "end_line": function_info.end_line,
            "docstring": function_info.docstring,
            "parameters": function_info.parameters,
            "return_type": function_info.return_type,
            "is_method": function_info.is_method,
            "class_name": function_info.class_name,
            "decorators": function_info.decorators
        }
        
        # Build context
        context = {
            "function": function_dict,
            "file": containing_file,
            "related_functions": self._find_related_functions_in_graph(function_info.full_name, call_graph_data, max_depth),
            "imports": [imp.__dict__ for imp in file_metadata.imports],
            "class_context": None
        }
        
        # Add class context if it's a method
        if function_info.is_method and function_info.class_name:
            class_info = project_metadata.find_class(function_info.class_name)
            if class_info:
                context["class_context"] = {
                    "name": class_info.name,
                    "start_line": class_info.start_line,
                    "end_line": class_info.end_line,
                    "docstring": class_info.docstring,
                    "base_classes": class_info.base_classes,
                    "decorators": class_info.decorators
                }
        
        return context
    
    def _find_related_functions_in_graph(self, function_name: str, call_graph_data: Dict[str, Any], max_depth: int) -> Dict[str, Any]:
        """Find related functions using the call graph data."""
        nodes = call_graph_data.get("nodes", {})
        edges = call_graph_data.get("edges", [])
        
        if function_name not in nodes:
            return {}
        
        # Find direct relationships
        calls = nodes[function_name].get("calls", [])
        called_by = nodes[function_name].get("called_by", [])
        
        # Find indirect relationships (up to max_depth)
        related = {"direct_calls": calls, "direct_callers": called_by}
        
        if max_depth > 1:
            # BFS to find indirect relationships
            visited = set([function_name])
            queue = [(func, 1) for func in calls + called_by]
            indirect_related = []
            
            while queue:
                current_func, depth = queue.pop(0)
                
                if current_func in visited or depth >= max_depth:
                    continue
                
                visited.add(current_func)
                indirect_related.append({"function": current_func, "distance": depth})
                
                if current_func in nodes:
                    next_funcs = nodes[current_func].get("calls", []) + nodes[current_func].get("called_by", [])
                    for next_func in next_funcs:
                        if next_func not in visited:
                            queue.append((next_func, depth + 1))
            
            related["indirect_related"] = indirect_related
        
        return related

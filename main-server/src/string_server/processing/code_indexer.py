"""
Core code indexing functionality using tree-sitter for Python code analysis.
"""

import logging
import os
from typing import List, Optional

try:
    import tree_sitter_python as tspython
    from tree_sitter import Language, Node, Parser
    HAS_TREE_SITTER = True
except ImportError:
    HAS_TREE_SITTER = False
    # Define dummy types for when tree-sitter is not available
    Language = None
    Node = None
    Parser = None
    tspython = None
    logging.warning("tree-sitter-python not available. Code indexing will be limited.")


from dataclasses import dataclass, field

@dataclass
class FunctionInfo:
    """Information about a function or method."""
    name: str
    full_name: str  # includes class name if method
    start_line: int
    end_line: int
    docstring: Optional[str] = None
    parameters: List[str] = field(default_factory=list)
    return_type: Optional[str] = None
    is_method: bool = False
    class_name: Optional[str] = None
    decorators: List[str] = field(default_factory=list)


@dataclass
class ClassInfo:
    """Information about a class."""
    name: str
    start_line: int
    end_line: int
    docstring: Optional[str] = None
    methods: List[FunctionInfo] = field(default_factory=list)
    base_classes: List[str] = field(default_factory=list)
    decorators: List[str] = field(default_factory=list)


@dataclass
class ImportInfo:
    """Information about imports."""
    module: str
    alias: Optional[str] = None
    items: List[str] = field(default_factory=list)  # for from imports
    line: int = 0


class CodeIndexer:
    """Main class for indexing Python code using tree-sitter."""
    
    CACHE_DIR = os.path.expanduser("~/.code_indexer_cache")
    
    def __init__(self):
        self.parser = None
        self._setup_parser()
    
    def _setup_parser(self):
        """Setup tree-sitter parser for Python using modern API."""
        if not HAS_TREE_SITTER:
            logging.error("tree-sitter-python not available")
            return
            
        try:
            # Use modern tree-sitter API as shown in documentation
            # https://github.com/tree-sitter/py-tree-sitter
            if tspython is not None and Language is not None and Parser is not None:
                python_language = Language(tspython.language())
                self.parser = Parser(python_language)
                logging.info("Successfully initialized Python parser with modern tree-sitter API")
            
        except Exception as e:
            logging.error(f"Failed to setup Python parser: {e}")
            raise
    
    def parse_file(self, file_path: str) -> Optional[Node]:
        """Parse a Python file and return the root node."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            return self.parse_code(code)
        except Exception as e:
            logging.error(f"Failed to parse file {file_path}: {e}")
            return None
    
    def parse_code(self, code: str) -> Optional[Node]:
        """Parse Python code and return the root node."""
        if not self.parser:
            raise RuntimeError("Parser not initialized")
        
        try:
            tree = self.parser.parse(bytes(code, "utf8"))
            if tree is None:
                logging.error("Failed to parse the code")
                return None
            return tree.root_node
        except Exception as e:
            logging.error(f"Failed to parse code: {e}")
            return None
    
    def extract_functions(self, node: Node, code: str, class_name: Optional[str] = None) -> List[FunctionInfo]:
        """Extract all function definitions from the AST."""
        functions = []
        
        if node.type == "function_definition":
            func_info = self._extract_function_info(node, code, class_name)
            if func_info:
                functions.append(func_info)
        
        # Recursively search child nodes
        for child in node.children:
            functions.extend(self.extract_functions(child, code, class_name))
        
        return functions
    
    def extract_classes(self, node: Node, code: str) -> List[ClassInfo]:
        """Extract all class definitions from the AST."""
        classes = []
        
        if node.type == "class_definition":
            class_info = self._extract_class_info(node, code)
            if class_info:
                classes.append(class_info)
        
        # Recursively search child nodes (but not inside classes we already found)
        if node.type != "class_definition":
            for child in node.children:
                classes.extend(self.extract_classes(child, code))
        
        return classes
    
    def extract_imports(self, node: Node, code: str) -> List[ImportInfo]:
        """Extract all import statements from the AST."""
        imports = []
        
        if node.type in ["import_statement", "import_from_statement"]:
            import_info = self._extract_import_info(node, code)
            if import_info:
                imports.append(import_info)
        
        # Recursively search child nodes
        for child in node.children:
            imports.extend(self.extract_imports(child, code))
        
        return imports
    
    def _extract_function_info(self, node: Node, code: str, class_name: Optional[str] = None) -> Optional[FunctionInfo]:
        """Extract detailed information about a function."""
        try:
            # Get function name - be more specific about which identifier we want
            # In a function_definition, the structure is: def <identifier> <parameters> ...
            # So we want the identifier that comes right after the 'def' keyword
            name_node = None

            # Look for the pattern: def -> identifier
            for i, child in enumerate(node.children):
                if child.type == "def" and i + 1 < len(node.children):
                    next_child = node.children[i + 1]
                    if next_child.type == "identifier":
                        name_node = next_child
                        break

            # Fallback to the old method if the above doesn't work
            if not name_node:
                for child in node.children:
                    if child.type == "identifier":
                        name_node = child
                        break

            if not name_node:
                logging.warning(f"No function name found in function_definition node at line {node.start_point[0] + 1}")
                return None

            name = self._get_node_text(name_node, code)

            # Validate the extracted name
            if not name or not name.isidentifier():
                logging.warning(f"Invalid function name extracted: '{name}' at line {node.start_point[0] + 1}")
                return None

            full_name = f"{class_name}.{name}" if class_name else name
            
            # Get line numbers
            start_line = node.start_point[0] + 1
            end_line = node.end_point[0] + 1
            
            # Extract docstring
            docstring = self._extract_docstring(node, code)
            
            # Extract parameters
            parameters = self._extract_parameters(node, code)
            
            # Extract return type annotation
            return_type = self._extract_return_type(node, code)
            
            # Extract decorators
            decorators = self._extract_decorators(node, code)
            
            return FunctionInfo(
                name=name,
                full_name=full_name,
                start_line=start_line,
                end_line=end_line,
                docstring=docstring,
                parameters=parameters,
                return_type=return_type,
                is_method=class_name is not None,
                class_name=class_name,
                decorators=decorators
            )
        except Exception as e:
            logging.error(f"Failed to extract function info: {e}")
            return None
    
    def _extract_class_info(self, node: Node, code: str) -> Optional[ClassInfo]:
        """Extract detailed information about a class."""
        try:
            # Get class name - be more specific about which identifier we want
            # In a class_definition, the structure is: class <identifier> ...
            # So we want the identifier that comes right after the 'class' keyword
            name_node = None

            # Look for the pattern: class -> identifier
            for i, child in enumerate(node.children):
                if child.type == "class" and i + 1 < len(node.children):
                    next_child = node.children[i + 1]
                    if next_child.type == "identifier":
                        name_node = next_child
                        break

            # Fallback to the old method if the above doesn't work
            if not name_node:
                for child in node.children:
                    if child.type == "identifier":
                        name_node = child
                        break

            if not name_node:
                logging.warning(f"No class name found in class_definition node at line {node.start_point[0] + 1}")
                return None

            name = self._get_node_text(name_node, code)

            # Validate the extracted name
            if not name or not name.isidentifier():
                logging.warning(f"Invalid class name extracted: '{name}' at line {node.start_point[0] + 1}")
                return None
            
            # Get line numbers
            start_line = node.start_point[0] + 1
            end_line = node.end_point[0] + 1
            
            # Extract docstring
            docstring = self._extract_docstring(node, code)
            
            # Extract base classes
            base_classes = self._extract_base_classes(node, code)
            
            # Extract decorators
            decorators = self._extract_decorators(node, code)
            
            # Extract methods
            methods = self.extract_functions(node, code, class_name=name)
            
            return ClassInfo(
                name=name,
                start_line=start_line,
                end_line=end_line,
                docstring=docstring,
                methods=methods,
                base_classes=base_classes,
                decorators=decorators
            )
        except Exception as e:
            logging.error(f"Failed to extract class info: {e}")
            return None
    
    def _extract_import_info(self, node: Node, code: str) -> Optional[ImportInfo]:
        """Extract information about an import statement."""
        try:
            line = node.start_point[0] + 1
            
            if node.type == "import_statement":
                # Handle: import module [as alias]
                module_parts = []
                alias = None
                
                for child in node.children:
                    if child.type == "dotted_name":
                        module_parts.append(self._get_node_text(child, code))
                    elif child.type == "aliased_import":
                        # Handle aliased imports
                        for subchild in child.children:
                            if subchild.type == "dotted_name":
                                module_parts.append(self._get_node_text(subchild, code))
                            elif subchild.type == "identifier" and subchild.prev_sibling and self._get_node_text(subchild.prev_sibling, code) == "as":
                                alias = self._get_node_text(subchild, code)
                
                if module_parts:
                    return ImportInfo(
                        module=module_parts[0],
                        alias=alias,
                        line=line
                    )
            
            elif node.type == "import_from_statement":
                # Handle: from module import item1, item2
                module = None
                items = []
                
                for child in node.children:
                    if child.type == "dotted_name" and child.prev_sibling and self._get_node_text(child.prev_sibling, code) == "from":
                        module = self._get_node_text(child, code)
                    elif child.type == "import_list":
                        for item_child in child.children:
                            if item_child.type == "identifier":
                                items.append(self._get_node_text(item_child, code))
                            elif item_child.type == "aliased_import":
                                # Handle aliased from imports
                                for subchild in item_child.children:
                                    if subchild.type == "identifier" and (not subchild.next_sibling or self._get_node_text(subchild.next_sibling, code) != "as"):
                                        items.append(self._get_node_text(subchild, code))
                
                if module:
                    return ImportInfo(
                        module=module,
                        items=items,
                        line=line
                    )
            
            return None
        except Exception as e:
            logging.error(f"Failed to extract import info: {e}")
            return None
    
    def _get_node_text(self, node: Node, code: str) -> str:
        """Get the text content of a node."""
        return code[node.start_byte:node.end_byte]
    
    def _extract_docstring(self, node: Node, code: str) -> Optional[str]:
        """Extract docstring from a function or class node."""
        # Look for the first string literal in the body
        for child in node.children:
            if child.type == "block":
                for stmt in child.children:
                    if stmt.type == "expression_statement":
                        for expr_child in stmt.children:
                            if expr_child.type == "string":
                                # Remove quotes and clean up
                                docstring = self._get_node_text(expr_child, code)
                                return self._clean_docstring(docstring)
        return None
    
    def _clean_docstring(self, docstring: str) -> str:
        """Clean up a docstring by removing quotes and normalizing whitespace."""
        # Remove outer quotes
        if docstring.startswith('"""') and docstring.endswith('"""'):
            docstring = docstring[3:-3]
        elif docstring.startswith("'''") and docstring.endswith("'''"):
            docstring = docstring[3:-3]
        elif docstring.startswith('"') and docstring.endswith('"'):
            docstring = docstring[1:-1]
        elif docstring.startswith("'") and docstring.endswith("'"):
            docstring = docstring[1:-1]
        
        # Normalize whitespace
        return docstring.strip()
    
    def _extract_parameters(self, node: Node, code: str) -> List[str]:
        """Extract parameter names from a function definition."""
        parameters = []
        
        for child in node.children:
            if child.type == "parameters":
                for param_child in child.children:
                    if param_child.type == "identifier":
                        parameters.append(self._get_node_text(param_child, code))
                    elif param_child.type == "typed_parameter":
                        # Handle typed parameters
                        for typed_child in param_child.children:
                            if typed_child.type == "identifier":
                                param_name = self._get_node_text(typed_child, code)
                                # Look for type annotation
                                type_annotation = None
                                if typed_child.next_sibling and typed_child.next_sibling.type == ":":
                                    type_node = typed_child.next_sibling.next_sibling
                                    if type_node:
                                        type_annotation = self._get_node_text(type_node, code)
                                
                                if type_annotation:
                                    parameters.append(f"{param_name}: {type_annotation}")
                                else:
                                    parameters.append(param_name)
                                break
                    elif param_child.type == "default_parameter":
                        # Handle parameters with default values
                        for default_child in param_child.children:
                            if default_child.type == "identifier":
                                param_name = self._get_node_text(default_child, code)
                                # Get the full parameter with default
                                full_param = self._get_node_text(param_child, code)
                                parameters.append(full_param)
                                break
        
        return parameters
    
    def _extract_return_type(self, node: Node, code: str) -> Optional[str]:
        """Extract return type annotation from a function definition."""
        for child in node.children:
            if child.type == "type":
                return self._get_node_text(child, code)
        return None
    
    def _extract_decorators(self, node: Node, code: str) -> List[str]:
        """Extract decorators from a function or class definition."""
        decorators = []

        # Look for decorated_definition parent
        parent = node.parent
        if parent and parent.type == "decorated_definition":
            for child in parent.children:
                if child.type == "decorator":
                    decorator_text = self._get_node_text(child, code)
                    # Clean up the decorator text and validate it
                    decorator_text = decorator_text.strip()
                    if decorator_text and decorator_text.startswith('@'):
                        decorators.append(decorator_text)
                    else:
                        logging.warning(f"Invalid decorator text: '{decorator_text}'")

        return decorators
    
    def _extract_base_classes(self, node: Node, code: str) -> List[str]:
        """Extract base classes from a class definition."""
        base_classes = []
        
        for child in node.children:
            if child.type == "argument_list":
                for arg_child in child.children:
                    if arg_child.type == "identifier":
                        base_classes.append(self._get_node_text(arg_child, code))
                    elif arg_child.type == "attribute":
                        base_classes.append(self._get_node_text(arg_child, code))
        
        return base_classes

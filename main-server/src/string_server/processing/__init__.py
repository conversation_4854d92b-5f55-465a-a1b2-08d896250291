"""
Processing module for code analysis, chunking, and processing.

This module contains all code processing functionality including:
- Code chunking and parsing
- Metadata extraction
- Reference tracking
- Call graph building
- Dead code analysis
- Job completion system
"""

from .code_chunker import (
    CodeChunk,
    PythonCodeChunker,
    GenericCodeChunker,
    CodeChunkerFactory
)

# Import other processing modules
from . import code_indexer
from . import metadata_extractor
from . import reference_tracker
from . import call_graph_builder
from . import dead_code_analyzer
from . import job_completion

__all__ = [
    'CodeChunk',
    'PythonCodeChunker',
    'GenericCodeChunker',
    'CodeChunkerFactory',
    'code_indexer',
    'metadata_extractor',
    'reference_tracker',
    'call_graph_builder',
    'dead_code_analyzer',
    'job_completion'
]
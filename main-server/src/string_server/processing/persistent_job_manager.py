"""
Persistent Thread-Safe Job Completion Manager

Extends the job completion system with SQLite persistence and thread-safe operations
optimized for Railway container deployment with proper concurrent access handling.

Key Features:
- SQLite persistence for job state across container restarts
- Thread-safe operations with deadlock prevention
- WAL mode for better concurrency
- Atomic job state transitions
- Railway container optimization
"""

import asyncio
import json
import logging
import threading
import uuid
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Callable, Set
from dataclasses import asdict

# Import base job classes
from .job_completion import (
    Job, JobStatus, JobType, JobProgress, JobResult, JobCompletionManager
)

# Import thread-safe database manager
try:
    from ..database import get_railway_database_manager, ThreadSafeDatabaseManager
except ImportError:
    from src.database import get_railway_database_manager, ThreadSafeDatabaseManager

logger = logging.getLogger(__name__)


class PersistentJobCompletionManager(JobCompletionManager):
    """
    Thread-safe persistent job completion manager with SQLite storage.
    
    Extends the base JobCompletionManager with database persistence and
    thread-safe operations optimized for Railway deployment.
    """
    
    def __init__(self, 
                 db_path: str = "/data/jobs.db",
                 cleanup_interval_minutes: int = 60, 
                 max_job_age_hours: int = 24):
        """
        Initialize persistent job manager.
        
        Args:
            db_path: Path to SQLite database file
            cleanup_interval_minutes: How often to clean up old jobs
            max_job_age_hours: Maximum age of completed jobs before cleanup
        """
        # Initialize base class (but don't use its in-memory storage)
        super().__init__(cleanup_interval_minutes, max_job_age_hours)
        
        self.db_path = db_path
        self.db_manager = get_railway_database_manager(db_path)
        self._lock = threading.RLock()
        
        # Initialize database schema
        self._init_database()
        
        # Load existing jobs from database
        self._load_jobs_from_database()
        
        logger.info(f"Persistent job manager initialized with database: {db_path}")
    
    def _init_database(self):
        """Initialize database schema for job persistence."""
        schema_sql = """
            CREATE TABLE IF NOT EXISTS jobs (
                job_id TEXT PRIMARY KEY,
                job_type TEXT NOT NULL,
                user_id TEXT NOT NULL,
                status TEXT NOT NULL,
                created_at TEXT NOT NULL,
                started_at TEXT,
                completed_at TEXT,
                timeout_seconds INTEGER NOT NULL,
                metadata TEXT,  -- JSON
                progress_data TEXT,  -- JSON
                result_data TEXT,  -- JSON
                updated_at TEXT NOT NULL
            );
            
            -- Indexes for better performance
            CREATE INDEX IF NOT EXISTS idx_jobs_user_id ON jobs(user_id);
            CREATE INDEX IF NOT EXISTS idx_jobs_status ON jobs(status);
            CREATE INDEX IF NOT EXISTS idx_jobs_created_at ON jobs(created_at);
            CREATE INDEX IF NOT EXISTS idx_jobs_completed_at ON jobs(completed_at);
            CREATE INDEX IF NOT EXISTS idx_jobs_user_status ON jobs(user_id, status);
            
            -- Table for job completion callbacks (if needed for persistence)
            CREATE TABLE IF NOT EXISTS job_callbacks (
                job_id TEXT NOT NULL,
                callback_id TEXT NOT NULL,
                callback_data TEXT,  -- JSON
                created_at TEXT NOT NULL,
                PRIMARY KEY (job_id, callback_id),
                FOREIGN KEY (job_id) REFERENCES jobs(job_id) ON DELETE CASCADE
            );
        """
        
        self.db_manager.execute_script(schema_sql)
        logger.info("Job database schema initialized")
    
    def _load_jobs_from_database(self):
        """Load existing jobs from database into memory."""
        try:
            rows = self.db_manager.execute_query("""
                SELECT * FROM jobs 
                WHERE status IN ('pending', 'running')
                ORDER BY created_at DESC
            """)
            
            loaded_count = 0
            for row in rows:
                try:
                    job = self._row_to_job(row)
                    self.jobs[job.job_id] = job
                    
                    # Track user jobs
                    if job.user_id not in self.user_jobs:
                        self.user_jobs[job.user_id] = set()
                    self.user_jobs[job.user_id].add(job.job_id)
                    
                    loaded_count += 1
                    
                except Exception as e:
                    logger.error(f"Error loading job {row['job_id']}: {e}")
            
            logger.info(f"Loaded {loaded_count} active jobs from database")
            
        except Exception as e:
            logger.error(f"Error loading jobs from database: {e}")
    
    def _row_to_job(self, row) -> Job:
        """Convert database row to Job object."""
        # Parse JSON fields
        metadata = json.loads(row['metadata']) if row['metadata'] else {}
        progress_data = json.loads(row['progress_data']) if row['progress_data'] else {}
        result_data = json.loads(row['result_data']) if row['result_data'] else None
        
        # Create progress object
        progress = JobProgress(
            current_step=progress_data.get('current_step', 0),
            total_steps=progress_data.get('total_steps', 0),
            current_operation=progress_data.get('current_operation', ''),
            percentage=progress_data.get('percentage', 0.0),
            estimated_completion=datetime.fromisoformat(progress_data['estimated_completion']) 
                if progress_data.get('estimated_completion') else None
        )
        
        # Create result object if exists
        result = None
        if result_data:
            result = JobResult(
                success=result_data.get('success', False),
                data=result_data.get('data', {}),
                error_message=result_data.get('error_message'),
                warnings=result_data.get('warnings', []),
                metrics=result_data.get('metrics', {})
            )
        
        # Create job object
        job = Job(
            job_id=row['job_id'],
            job_type=JobType(row['job_type']),
            user_id=row['user_id'],
            status=JobStatus(row['status']),
            created_at=datetime.fromisoformat(row['created_at']),
            started_at=datetime.fromisoformat(row['started_at']) if row['started_at'] else None,
            completed_at=datetime.fromisoformat(row['completed_at']) if row['completed_at'] else None,
            progress=progress,
            result=result,
            metadata=metadata,
            timeout_seconds=row['timeout_seconds']
        )
        
        return job
    
    def _job_to_row_data(self, job: Job) -> tuple:
        """Convert Job object to database row data."""
        now = datetime.now(timezone.utc).isoformat()
        
        # Serialize complex objects to JSON
        metadata_json = json.dumps(job.metadata) if job.metadata else None
        
        progress_data = {
            'current_step': job.progress.current_step,
            'total_steps': job.progress.total_steps,
            'current_operation': job.progress.current_operation,
            'percentage': job.progress.percentage,
            'estimated_completion': job.progress.estimated_completion.isoformat() 
                if job.progress.estimated_completion else None
        }
        progress_json = json.dumps(progress_data)
        
        result_json = None
        if job.result:
            result_json = json.dumps(asdict(job.result))
        
        return (
            job.job_id,
            job.job_type.value,
            job.user_id,
            job.status.value,
            job.created_at.isoformat(),
            job.started_at.isoformat() if job.started_at else None,
            job.completed_at.isoformat() if job.completed_at else None,
            job.timeout_seconds,
            metadata_json,
            progress_json,
            result_json,
            now
        )
    
    def create_job(self, 
                   job_type: JobType, 
                   user_id: str, 
                   metadata: Dict[str, Any] = None,
                   timeout_seconds: int = 300) -> str:
        """Create a new job with database persistence."""
        with self._lock:
            # Create job in memory first
            job_id = super().create_job(job_type, user_id, metadata, timeout_seconds)
            job = self.jobs[job_id]
            
            # Persist to database atomically
            try:
                def insert_operation(conn):
                    row_data = self._job_to_row_data(job)
                    conn.execute("""
                        INSERT INTO jobs (
                            job_id, job_type, user_id, status, created_at, started_at, 
                            completed_at, timeout_seconds, metadata, progress_data, 
                            result_data, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, row_data)
                
                self.db_manager.execute_with_retry(insert_operation, immediate=True)
                logger.debug(f"Persisted job creation: {job_id}")
                
            except Exception as e:
                # Rollback in-memory state if database fails
                del self.jobs[job_id]
                if user_id in self.user_jobs:
                    self.user_jobs[user_id].discard(job_id)
                logger.error(f"Failed to persist job creation: {e}")
                raise
            
            return job_id
    
    def start_job(self, job_id: str, total_steps: int = 1) -> bool:
        """Start a job with database persistence."""
        with self._lock:
            # Update in memory first
            success = super().start_job(job_id, total_steps)
            if not success:
                return False
            
            # Persist to database
            job = self.jobs[job_id]
            try:
                self._update_job_in_database(job)
                logger.debug(f"Persisted job start: {job_id}")
                return True
                
            except Exception as e:
                logger.error(f"Failed to persist job start: {e}")
                # Could rollback in-memory state here if needed
                return False
    
    def update_progress(self, 
                       job_id: str, 
                       current_step: int = None, 
                       operation: str = None,
                       percentage: float = None) -> bool:
        """Update job progress with database persistence."""
        with self._lock:
            # Update in memory first
            success = super().update_progress(job_id, current_step, operation, percentage)
            if not success:
                return False
            
            # Persist to database (less frequent updates for performance)
            job = self.jobs[job_id]
            try:
                self._update_job_in_database(job)
                return True
                
            except Exception as e:
                logger.error(f"Failed to persist progress update: {e}")
                return False
    
    def complete_job(self, 
                    job_id: str, 
                    success: bool = True, 
                    result_data: Dict[str, Any] = None,
                    error_message: str = None,
                    warnings: List[str] = None,
                    metrics: Dict[str, Any] = None) -> bool:
        """Complete a job with database persistence."""
        with self._lock:
            # Update in memory first
            success_result = super().complete_job(
                job_id, success, result_data, error_message, warnings, metrics
            )
            if not success_result:
                return False
            
            # Persist to database
            job = self.jobs[job_id]
            try:
                self._update_job_in_database(job)
                logger.debug(f"Persisted job completion: {job_id}")
                return True
                
            except Exception as e:
                logger.error(f"Failed to persist job completion: {e}")
                return False
    
    def _update_job_in_database(self, job: Job):
        """Update job in database."""
        def update_operation(conn):
            row_data = self._job_to_row_data(job)
            conn.execute("""
                UPDATE jobs SET 
                    job_type = ?, user_id = ?, status = ?, created_at = ?,
                    started_at = ?, completed_at = ?, timeout_seconds = ?,
                    metadata = ?, progress_data = ?, result_data = ?, updated_at = ?
                WHERE job_id = ?
            """, row_data[1:] + (job.job_id,))
        
        self.db_manager.execute_with_retry(update_operation, immediate=True)
    
    async def _cleanup_old_jobs(self):
        """Remove old completed jobs from both memory and database."""
        # First do the in-memory cleanup
        await super()._cleanup_old_jobs()
        
        # Then clean up database
        try:
            cutoff_time = datetime.now(timezone.utc) - timedelta(seconds=self.max_job_age)
            
            def cleanup_operation(conn):
                cursor = conn.execute("""
                    DELETE FROM jobs 
                    WHERE status IN ('completed', 'failed', 'cancelled', 'timeout')
                    AND completed_at < ?
                """, (cutoff_time.isoformat(),))
                return cursor.rowcount
            
            deleted_count = self.db_manager.execute_with_retry(cleanup_operation, immediate=True)
            
            if deleted_count > 0:
                logger.info(f"Cleaned up {deleted_count} old jobs from database")
                
        except Exception as e:
            logger.error(f"Error cleaning up database jobs: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive job manager statistics including database stats."""
        # Get base stats
        stats = super().get_stats()
        
        # Add database stats
        try:
            db_stats = self.db_manager.get_stats()
            stats["database_stats"] = db_stats
            
            # Get database job counts
            rows = self.db_manager.execute_query("""
                SELECT status, COUNT(*) as count 
                FROM jobs 
                GROUP BY status
            """)
            
            db_status_counts = {row['status']: row['count'] for row in rows}
            stats["database_job_counts"] = db_status_counts
            
        except Exception as e:
            logger.error(f"Error getting database stats: {e}")
            stats["database_error"] = str(e)
        
        return stats


# Global persistent job manager instance
_persistent_job_manager: Optional[PersistentJobCompletionManager] = None


def get_persistent_job_manager(db_path: str = "/data/jobs.db") -> PersistentJobCompletionManager:
    """Get the global persistent job manager instance."""
    global _persistent_job_manager
    if _persistent_job_manager is None:
        _persistent_job_manager = PersistentJobCompletionManager(db_path)
    return _persistent_job_manager


async def initialize_persistent_job_manager(db_path: str = "/data/jobs.db"):
    """Initialize and start the global persistent job manager."""
    manager = get_persistent_job_manager(db_path)
    await manager.start()
    return manager

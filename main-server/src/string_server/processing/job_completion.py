"""
Job Completion and Tracking System for MCP Server

This module provides comprehensive job tracking, completion notifications,
and status APIs for async operations like file processing and vector storage.
"""

import asyncio
import logging
import uuid
from dataclasses import dataclass, field
from datetime import datetime, timedelta, timezone
from enum import Enum
from typing import Dict, Any, List, Optional, Set, Callable

logger = logging.getLogger(__name__)


class JobStatus(Enum):
    """Job execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


class JobType(Enum):
    """Types of jobs that can be tracked."""
    FILE_PROCESSING = "file_processing"
    CHUNK_PROCESSING = "chunk_processing"
    EMBEDDING_GENERATION = "embedding_generation"
    VECTOR_STORAGE = "vector_storage"
    BATCH_OPERATION = "batch_operation"


@dataclass
class JobProgress:
    """Progress tracking for jobs."""
    current_step: int = 0
    total_steps: int = 0
    current_operation: str = ""
    percentage: float = 0.0
    estimated_completion: Optional[datetime] = None


@dataclass
class JobResult:
    """Result data for completed jobs."""
    success: bool = False
    data: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    warnings: List[str] = field(default_factory=list)
    metrics: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Job:
    """Represents a trackable job in the system."""
    job_id: str
    job_type: JobType
    user_id: str
    status: JobStatus = JobStatus.PENDING
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    progress: JobProgress = field(default_factory=JobProgress)
    result: Optional[JobResult] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    timeout_seconds: int = 300  # 5 minutes default
    
    @property
    def duration_seconds(self) -> Optional[float]:
        """Calculate job duration in seconds."""
        if not self.started_at:
            return None
        end_time = self.completed_at or datetime.now(timezone.utc)
        return (end_time - self.started_at).total_seconds()
    
    @property
    def is_active(self) -> bool:
        """Check if job is currently active."""
        return self.status in [JobStatus.PENDING, JobStatus.RUNNING]
    
    @property
    def is_completed(self) -> bool:
        """Check if job has completed (success or failure)."""
        return self.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED, JobStatus.TIMEOUT]


class JobCompletionManager:
    """Manages job tracking, completion notifications, and cleanup."""
    
    def __init__(self, cleanup_interval_minutes: int = 60, max_job_age_hours: int = 24):
        self.jobs: Dict[str, Job] = {}
        self.user_jobs: Dict[str, Set[str]] = {}  # user_id -> set of job_ids
        self.completion_callbacks: Dict[str, List[Callable]] = {}  # job_id -> callbacks
        self.cleanup_interval = cleanup_interval_minutes * 60  # Convert to seconds
        self.max_job_age = max_job_age_hours * 3600  # Convert to seconds
        self._cleanup_task: Optional[asyncio.Task] = None
        self._running = False
    
    async def start(self):
        """Start the job manager and cleanup task."""
        if self._running:
            return
        
        self._running = True
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        logger.info("Job completion manager started")
    
    async def stop(self):
        """Stop the job manager and cleanup task."""
        self._running = False
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        logger.info("Job completion manager stopped")
    
    def create_job(
        self, 
        job_type: JobType, 
        user_id: str, 
        metadata: Dict[str, Any] = None,
        timeout_seconds: int = 300
    ) -> str:
        """
        Create a new job and return its ID.
        
        Args:
            job_type: Type of job being created
            user_id: User who owns this job
            metadata: Additional job metadata
            timeout_seconds: Job timeout in seconds
            
        Returns:
            Job ID string
        """
        job_id = str(uuid.uuid4())
        
        job = Job(
            job_id=job_id,
            job_type=job_type,
            user_id=user_id,
            metadata=metadata or {},
            timeout_seconds=timeout_seconds
        )
        
        self.jobs[job_id] = job
        
        # Track user jobs
        if user_id not in self.user_jobs:
            self.user_jobs[user_id] = set()
        self.user_jobs[user_id].add(job_id)
        
        logger.info(f"Created job {job_id} of type {job_type.value} for user {user_id}")
        return job_id
    
    def start_job(self, job_id: str, total_steps: int = 1) -> bool:
        """
        Mark a job as started.
        
        Args:
            job_id: Job identifier
            total_steps: Total number of steps for progress tracking
            
        Returns:
            True if job was started successfully
        """
        job = self.jobs.get(job_id)
        if not job:
            logger.error(f"Job {job_id} not found")
            return False
        
        if job.status != JobStatus.PENDING:
            logger.warning(f"Job {job_id} is not in pending status: {job.status}")
            return False
        
        job.status = JobStatus.RUNNING
        job.started_at = datetime.now(timezone.utc)
        job.progress.total_steps = total_steps
        
        logger.info(f"Started job {job_id}")
        return True
    
    def update_progress(
        self, 
        job_id: str, 
        current_step: int = None, 
        operation: str = None,
        percentage: float = None
    ) -> bool:
        """
        Update job progress.
        
        Args:
            job_id: Job identifier
            current_step: Current step number
            operation: Description of current operation
            percentage: Progress percentage (0-100)
            
        Returns:
            True if progress was updated successfully
        """
        job = self.jobs.get(job_id)
        if not job:
            return False
        
        if current_step is not None:
            job.progress.current_step = current_step
            if job.progress.total_steps > 0:
                job.progress.percentage = (current_step / job.progress.total_steps) * 100
        
        if operation is not None:
            job.progress.current_operation = operation
        
        if percentage is not None:
            job.progress.percentage = min(100.0, max(0.0, percentage))
        
        # Estimate completion time
        if job.started_at and job.progress.percentage > 0:
            elapsed = (datetime.now(timezone.utc) - job.started_at).total_seconds()
            estimated_total = elapsed / (job.progress.percentage / 100)
            job.progress.estimated_completion = job.started_at + timedelta(seconds=estimated_total)
        
        return True
    
    def complete_job(
        self, 
        job_id: str, 
        success: bool = True, 
        result_data: Dict[str, Any] = None,
        error_message: str = None,
        warnings: List[str] = None,
        metrics: Dict[str, Any] = None
    ) -> bool:
        """
        Mark a job as completed.
        
        Args:
            job_id: Job identifier
            success: Whether job completed successfully
            result_data: Job result data
            error_message: Error message if job failed
            warnings: List of warning messages
            metrics: Performance metrics
            
        Returns:
            True if job was completed successfully
        """
        job = self.jobs.get(job_id)
        if not job:
            logger.error(f"Job {job_id} not found")
            return False
        
        job.status = JobStatus.COMPLETED if success else JobStatus.FAILED
        job.completed_at = datetime.now(timezone.utc)
        # Only set percentage to 100% if job completed successfully
        if success:
            job.progress.percentage = 100.0
        
        job.result = JobResult(
            success=success,
            data=result_data or {},
            error_message=error_message,
            warnings=warnings or [],
            metrics=metrics or {}
        )
        
        # Trigger completion callbacks
        try:
            loop = asyncio.get_running_loop()
            loop.create_task(self._trigger_callbacks(job_id))
        except RuntimeError:
            # No event loop running, skip async callbacks
            pass
        
        logger.info(f"Completed job {job_id} with success={success}")
        return True
    
    def get_job(self, job_id: str) -> Optional[Job]:
        """Get job by ID."""
        return self.jobs.get(job_id)
    
    def get_user_jobs(self, user_id: str, status_filter: List[JobStatus] = None) -> List[Job]:
        """
        Get all jobs for a user.
        
        Args:
            user_id: User identifier
            status_filter: Optional list of statuses to filter by
            
        Returns:
            List of jobs for the user
        """
        job_ids = self.user_jobs.get(user_id, set())
        jobs = [self.jobs[job_id] for job_id in job_ids if job_id in self.jobs]
        
        if status_filter:
            jobs = [job for job in jobs if job.status in status_filter]
        
        return sorted(jobs, key=lambda j: j.created_at, reverse=True)
    
    def add_completion_callback(self, job_id: str, callback: Callable[[Job], None]):
        """Add a callback to be called when job completes."""
        if job_id not in self.completion_callbacks:
            self.completion_callbacks[job_id] = []
        self.completion_callbacks[job_id].append(callback)
    
    async def _trigger_callbacks(self, job_id: str):
        """Trigger completion callbacks for a job."""
        callbacks = self.completion_callbacks.get(job_id, [])
        job = self.jobs.get(job_id)
        
        if not job:
            return
        
        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(job)
                else:
                    callback(job)
            except Exception as e:
                logger.error(f"Error in completion callback for job {job_id}: {e}")
        
        # Clean up callbacks
        if job_id in self.completion_callbacks:
            del self.completion_callbacks[job_id]
    
    async def _cleanup_loop(self):
        """Background task to clean up old completed jobs."""
        while self._running:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_old_jobs()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
    
    async def _cleanup_old_jobs(self):
        """Remove old completed jobs to prevent memory leaks."""
        cutoff_time = datetime.now(timezone.utc) - timedelta(seconds=self.max_job_age)
        jobs_to_remove = []
        
        for job_id, job in self.jobs.items():
            if job.is_completed and job.completed_at and job.completed_at < cutoff_time:
                jobs_to_remove.append(job_id)
        
        for job_id in jobs_to_remove:
            job = self.jobs[job_id]
            
            # Remove from user jobs
            if job.user_id in self.user_jobs:
                self.user_jobs[job.user_id].discard(job_id)
                if not self.user_jobs[job.user_id]:
                    del self.user_jobs[job.user_id]
            
            # Remove job
            del self.jobs[job_id]
            
            # Clean up callbacks
            if job_id in self.completion_callbacks:
                del self.completion_callbacks[job_id]
        
        if jobs_to_remove:
            logger.info(f"Cleaned up {len(jobs_to_remove)} old jobs")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get job manager statistics."""
        total_jobs = len(self.jobs)
        status_counts = {}
        type_counts = {}
        
        for job in self.jobs.values():
            status_counts[job.status.value] = status_counts.get(job.status.value, 0) + 1
            type_counts[job.job_type.value] = type_counts.get(job.job_type.value, 0) + 1
        
        return {
            "total_jobs": total_jobs,
            "active_jobs": len([j for j in self.jobs.values() if j.is_active]),
            "completed_jobs": len([j for j in self.jobs.values() if j.is_completed]),
            "failed_jobs": len([j for j in self.jobs.values() if j.status == JobStatus.FAILED]),
            "pending_jobs": len([j for j in self.jobs.values() if j.status == JobStatus.PENDING]),
            "status_breakdown": status_counts,
            "type_breakdown": type_counts,
            "total_users": len(self.user_jobs),
            "cleanup_interval_minutes": self.cleanup_interval / 60,
            "max_job_age_hours": self.max_job_age / 3600
        }


# Global job manager instance
_job_manager: Optional[JobCompletionManager] = None


def get_job_manager() -> JobCompletionManager:
    """Get the global job manager instance."""
    global _job_manager
    if _job_manager is None:
        _job_manager = JobCompletionManager()
    return _job_manager


async def initialize_job_manager():
    """Initialize and start the global job manager."""
    manager = get_job_manager()
    await manager.start()
    return manager

"""
Dead code analyzer for identifying unused functions, classes, and variables.
This is essential for code cleanup and safe refactoring.
"""

from dataclasses import dataclass, field
from typing import List, Optional

try:
    from .metadata_extractor import ProjectMetadata
    from .reference_tracker import ReferenceTracker
    from .code_indexer import CodeIndexer, FunctionInfo, ClassInfo
except ImportError:
    from metadata_extractor import ProjectMetadata
    from reference_tracker import ReferenceTracker
    from code_indexer import CodeIndexer, FunctionInfo, ClassInfo


@dataclass
class DeadCodeItem:
    """An item of potentially dead code."""
    name: str
    item_type: str  # 'function', 'class', 'variable'
    file_path: str
    line: int
    reason: str  # Why it's considered dead
    confidence: str  # 'HIGH', 'MEDIUM', 'LOW'
    context_line: str


@dataclass
class DeadCodeResult:
    """Result of dead code analysis."""
    total_items: int
    high_confidence: List[DeadCodeItem] = field(default_factory=list)
    medium_confidence: List[DeadCodeItem] = field(default_factory=list)
    low_confidence: List[DeadCodeItem] = field(default_factory=list)
    entry_points: List[str] = field(default_factory=list)  # Functions that are entry points


class DeadCodeAnalyzer:
    """Analyzes code to identify potentially unused/dead code."""
    
    def __init__(self, indexer: CodeIndexer):
        self.indexer = indexer
        self.reference_tracker = ReferenceTracker(indexer)
        
        # Common entry point patterns
        self.entry_point_patterns = {
            'main', '__main__', '__init__', '__new__', '__call__',
            'setUp', 'tearDown', 'test_', 'setUp', 'setUpClass', 'tearDownClass',
            'run', 'execute', 'handle', 'process', 'start', 'stop',
            'get', 'post', 'put', 'delete', 'patch',  # HTTP methods
            'cli', 'command', 'parse_args'
        }
        
        # Common decorator patterns that indicate entry points
        self.entry_point_decorators = {
            'property', 'staticmethod', 'classmethod',
            'pytest.fixture', 'fixture', 'mock.patch',
            'app.route', 'route', 'api.route',
            'click.command', 'command',
            'mcp.tool', 'tool', '@mcp.tool()', '@tool()'
        }
    
    def analyze_dead_code(self, project_metadata: ProjectMetadata) -> DeadCodeResult:
        """Analyze the entire project for dead code."""
        result = DeadCodeResult(total_items=0)
        
        # Get all functions and classes
        all_functions = project_metadata.get_all_functions()
        all_classes = project_metadata.get_all_classes()
        
        # Identify entry points first
        entry_points = self._identify_entry_points(all_functions, project_metadata)
        result.entry_points = entry_points
        
        # Analyze functions
        for func in all_functions:
            dead_item = self._analyze_function_usage(func, project_metadata, entry_points)
            if dead_item:
                self._categorize_dead_item(dead_item, result)
        
        # Analyze classes
        for cls in all_classes:
            dead_item = self._analyze_class_usage(cls, project_metadata, entry_points)
            if dead_item:
                self._categorize_dead_item(dead_item, result)
        
        result.total_items = len(result.high_confidence) + len(result.medium_confidence) + len(result.low_confidence)
        return result
    
    def _identify_entry_points(self, functions: List[FunctionInfo], project_metadata: ProjectMetadata) -> List[str]:
        """Identify functions that are likely entry points."""
        entry_points = []
        
        for func in functions:
            # Check name patterns
            if any(pattern in func.name.lower() for pattern in self.entry_point_patterns):
                entry_points.append(func.full_name)
                continue
            
            # Check decorators - be more flexible in matching
            is_entry_point = False
            for decorator in func.decorators:
                # Check exact matches
                if decorator in self.entry_point_decorators:
                    is_entry_point = True
                    break
                # Check if decorator contains any of the patterns
                for pattern in self.entry_point_decorators:
                    if pattern in decorator:
                        is_entry_point = True
                        break
                if is_entry_point:
                    break

            if is_entry_point:
                entry_points.append(func.full_name)
                continue
            
            # Special cases
            if func.name.startswith('test_'):  # Test functions
                entry_points.append(func.full_name)
            elif func.name.startswith('_') and func.name.endswith('_'):  # Magic methods
                entry_points.append(func.full_name)
            elif func.full_name.endswith('.__init__'):  # Constructors
                entry_points.append(func.full_name)
        
        return entry_points
    
    def _analyze_function_usage(self, func: FunctionInfo, project_metadata: ProjectMetadata, 
                               entry_points: List[str]) -> Optional[DeadCodeItem]:
        """Analyze if a function is potentially dead code."""
        
        # Skip entry points
        if func.full_name in entry_points:
            return None
        
        # Find all references to this function
        references = self.reference_tracker.find_all_references(func.name, project_metadata)
        
        # Count actual usage (excluding definition)
        usage_count = len([ref for ref in references.references 
                          if ref.reference_type in ['function_call', 'attribute_access']])
        
        if usage_count == 0:
            confidence = self._determine_confidence(func, references)
            reason = self._determine_reason(func, references, usage_count)
            
            return DeadCodeItem(
                name=func.full_name,
                item_type='function',
                file_path=self._get_function_file(func, project_metadata),
                line=func.start_line,
                reason=reason,
                confidence=confidence,
                context_line=f"def {func.name}({', '.join(func.parameters)}):"
            )
        
        return None
    
    def _analyze_class_usage(self, cls: ClassInfo, project_metadata: ProjectMetadata,
                           entry_points: List[str]) -> Optional[DeadCodeItem]:
        """Analyze if a class is potentially dead code."""
        
        # Find all references to this class
        references = self.reference_tracker.find_all_references(cls.name, project_metadata)
        
        # Count actual usage (excluding definition)
        usage_count = len([ref for ref in references.references 
                          if ref.reference_type in ['class_instantiation', 'import', 'reference']])
        
        # Check if any methods are entry points
        has_entry_point_methods = any(f"{cls.name}.{method.name}" in entry_points 
                                     for method in cls.methods)
        
        if usage_count == 0 and not has_entry_point_methods:
            confidence = self._determine_class_confidence(cls, references)
            reason = f"No instantiations or imports found"
            
            return DeadCodeItem(
                name=cls.name,
                item_type='class',
                file_path=self._get_class_file(cls, project_metadata),
                line=cls.start_line,
                reason=reason,
                confidence=confidence,
                context_line=f"class {cls.name}({', '.join(cls.base_classes)}):"
            )
        
        return None
    
    def _determine_confidence(self, func: FunctionInfo, references) -> str:
        """Determine confidence level for dead function."""
        
        # High confidence: no references, not special name, not in __init__ file
        if (len(references.references) <= 1 and  # Only definition
            not func.name.startswith('_') and 
            not any(keyword in func.name.lower() for keyword in ['test', 'mock', 'setup', 'main'])):
            return 'HIGH'
        
        # Medium confidence: some indicators it might be used
        if func.name.startswith('_') or 'test' in func.name.lower():
            return 'MEDIUM'
        
        # Low confidence: likely has valid reasons to exist
        return 'LOW'
    
    def _determine_class_confidence(self, cls: ClassInfo, references) -> str:
        """Determine confidence level for dead class."""
        
        # High confidence: no references, no special patterns
        if (len(references.references) <= 1 and  # Only definition
            not cls.name.startswith('_') and
            not any(keyword in cls.name.lower() for keyword in ['test', 'mock', 'base', 'abstract'])):
            return 'HIGH'
        
        # Medium confidence
        if cls.name.startswith('_') or any(keyword in cls.name.lower() for keyword in ['test', 'mock']):
            return 'MEDIUM'
        
        return 'LOW'
    
    def _determine_reason(self, func: FunctionInfo, references, usage_count: int) -> str:
        """Determine why the function is considered dead."""
        if usage_count == 0:
            if len(references.references) == 0:
                return "No references found anywhere"
            else:
                return f"Only definition found, no actual calls"
        return f"Only {usage_count} references found"
    
    def _get_function_file(self, func: FunctionInfo, project_metadata: ProjectMetadata) -> str:
        """Get the file path containing a function."""
        for file_path, metadata in project_metadata.files.items():
            if func in metadata.functions:
                return file_path
            for cls in metadata.classes:
                if func in cls.methods:
                    return file_path
        return "unknown"
    
    def _get_class_file(self, cls: ClassInfo, project_metadata: ProjectMetadata) -> str:
        """Get the file path containing a class."""
        for file_path, metadata in project_metadata.files.items():
            if cls in metadata.classes:
                return file_path
        return "unknown"
    
    def _categorize_dead_item(self, item: DeadCodeItem, result: DeadCodeResult):
        """Categorize a dead code item by confidence."""
        if item.confidence == 'HIGH':
            result.high_confidence.append(item)
        elif item.confidence == 'MEDIUM':
            result.medium_confidence.append(item)
        else:
            result.low_confidence.append(item)
    
    def format_dead_code_report(self, result: DeadCodeResult) -> str:
        """Format dead code analysis results into a readable report."""
        if result.total_items == 0:
            return "✅ No dead code detected! Your codebase is clean."
        
        report = f"🧹 DEAD CODE ANALYSIS REPORT\n\n"
        report += f"📊 SUMMARY: {result.total_items} potentially unused items found\n"
        report += f"  🔴 High confidence: {len(result.high_confidence)}\n"
        report += f"  🟡 Medium confidence: {len(result.medium_confidence)}\n"
        report += f"  🟢 Low confidence: {len(result.low_confidence)}\n\n"
        
        # High confidence items (safe to remove)
        if result.high_confidence:
            report += "🔴 HIGH CONFIDENCE (Safe to remove):\n"
            for item in result.high_confidence:
                report += f"  📁 {item.file_path}:{item.line}\n"
                report += f"    {self._get_item_icon(item.item_type)} {item.name} - {item.reason}\n"
                report += f"    Code: {item.context_line}\n\n"
        
        # Medium confidence items (review before removing)
        if result.medium_confidence:
            report += "🟡 MEDIUM CONFIDENCE (Review before removing):\n"
            for item in result.medium_confidence:
                report += f"  📁 {item.file_path}:{item.line}\n"
                report += f"    {self._get_item_icon(item.item_type)} {item.name} - {item.reason}\n"
                report += f"    Code: {item.context_line}\n\n"
        
        # Low confidence items (likely false positives)
        if result.low_confidence:
            report += "🟢 LOW CONFIDENCE (Likely false positives):\n"
            for item in result.low_confidence[:5]:  # Show only first 5
                report += f"  📁 {item.file_path}:{item.line}\n"
                report += f"    {self._get_item_icon(item.item_type)} {item.name} - {item.reason}\n"
            
            if len(result.low_confidence) > 5:
                report += f"    ... and {len(result.low_confidence) - 5} more\n"
        
        report += f"\n🎯 RECOMMENDATIONS:\n"
        report += f"  1. Start with HIGH confidence items - safest to remove\n"
        report += f"  2. Review MEDIUM confidence items carefully\n"
        report += f"  3. Consider LOW confidence items as potential refactoring opportunities\n"
        
        if result.entry_points:
            report += f"\n🚀 Entry points detected: {len(result.entry_points)}\n"
            report += f"  (These are preserved as they might be called externally)\n"
        
        return report
    
    def _get_item_icon(self, item_type: str) -> str:
        """Get icon for item type."""
        icons = {
            'function': '🔧',
            'class': '🏗️',
            'variable': '📝'
        }
        return icons.get(item_type, '❓') 
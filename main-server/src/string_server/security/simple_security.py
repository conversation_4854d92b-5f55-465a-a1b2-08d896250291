"""
Simple Security Enhancement for MCP Server

Adds security headers and protections without middleware complexity.
Works with existing server setup and Railway health checks.
"""

import os
import time
import logging
from datetime import datetime
from typing import Dict, Any, Optional

# Simple rate limiting storage
_rate_limit_store = {}
_blocked_ips = set()

logger = logging.getLogger(__name__)


def add_security_headers(response_dict: Dict[str, Any], request_path: str = "/") -> Dict[str, Any]:
    """
    Add security headers to response dictionary.
    
    This works with existing JSON responses without breaking them.
    
    Args:
        response_dict: The response dictionary to enhance
        request_path: The request path for context
        
    Returns:
        Enhanced response with security metadata
    """
    # Don't modify health check responses
    if request_path in ["/health", "/status"]:
        return response_dict
    
    # Add security metadata to response
    enhanced_response = response_dict.copy()
    
    # Add security info to response
    enhanced_response["_security"] = {
        "server": "MCP-Server/1.0",
        "timestamp": datetime.utcnow().isoformat(),
        "secure": True
    }
    
    return enhanced_response


def check_rate_limit(client_ip: str, max_requests: int = 60, window_seconds: int = 60) -> bool:
    """
    Simple rate limiting check.
    
    Args:
        client_ip: Client IP address
        max_requests: Maximum requests per window
        window_seconds: Time window in seconds
        
    Returns:
        True if request is allowed, False if rate limited
    """
    if not os.getenv('SECURITY_RATE_LIMITING', 'false').lower() == 'true':
        return True  # Rate limiting disabled
    
    if client_ip in _blocked_ips:
        return False
    
    now = time.time()
    cutoff = now - window_seconds
    
    # Clean old entries
    if client_ip in _rate_limit_store:
        _rate_limit_store[client_ip] = [
            req_time for req_time in _rate_limit_store[client_ip] 
            if req_time > cutoff
        ]
    else:
        _rate_limit_store[client_ip] = []
    
    # Check limit
    if len(_rate_limit_store[client_ip]) >= max_requests:
        _blocked_ips.add(client_ip)
        logger.warning(f"Rate limit exceeded for {client_ip}")
        return False
    
    # Record this request
    _rate_limit_store[client_ip].append(now)
    return True


def validate_request_size(content_length: Optional[str], max_size: int = 10 * 1024 * 1024) -> bool:
    """
    Validate request content length.
    
    Args:
        content_length: Content-Length header value
        max_size: Maximum allowed size in bytes
        
    Returns:
        True if request size is valid, False otherwise
    """
    if not content_length:
        return True
    
    try:
        size = int(content_length)
        return size <= max_size
    except ValueError:
        return False


def log_security_event(event_type: str, details: Dict[str, Any]) -> None:
    """
    Log security-related events.
    
    Args:
        event_type: Type of security event
        details: Event details dictionary
    """
    if not os.getenv('SECURITY_LOGGING', 'false').lower() == 'true':
        return
    
    log_entry = {
        'timestamp': datetime.utcnow().isoformat(),
        'event_type': event_type,
        'details': details
    }
    
    if event_type in ['rate_limit', 'validation_failed', 'blocked_request']:
        logger.warning(f"Security event: {log_entry}")
    else:
        logger.debug(f"Security event: {log_entry}")


class SecurityHeaders:
    """HTTP security headers for different endpoint types."""
    
    @staticmethod
    def get_headers_for_path(path: str) -> Dict[str, str]:
        """Get appropriate security headers for a given path."""
        
        # No security headers for health checks
        if path in ["/health", "/status", "/favicon.ico"]:
            return {}
        
        # Basic security headers for all other endpoints
        headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY", 
            "X-XSS-Protection": "1; mode=block",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Server": "MCP-Server/1.0"
        }
        
        # Enhanced headers for MCP endpoints
        if path.startswith("/mcp/"):
            headers.update({
                "Content-Security-Policy": (
                    "default-src 'self'; "
                    "script-src 'none'; "
                    "object-src 'none'; "
                    "frame-ancestors 'none';"
                ),
                "X-Robots-Tag": "noindex, nofollow"
            })
        
        return headers


def secure_response(response_data: Any, request_path: str = "/", 
                   client_ip: str = "unknown") -> tuple[Any, Dict[str, str]]:
    """
    Secure a response with headers and metadata.
    
    Args:
        response_data: Original response data
        request_path: Request path
        client_ip: Client IP address
        
    Returns:
        Tuple of (secured_response_data, security_headers)
    """
    # Get security headers
    headers = SecurityHeaders.get_headers_for_path(request_path)
    
    # Enhance response data if it's a dictionary
    if isinstance(response_data, dict):
        secured_data = add_security_headers(response_data, request_path)
    else:
        secured_data = response_data
    
    # Log secure request
    log_security_event('secure_response', {
        'path': request_path,
        'client_ip': client_ip,
        'headers_added': len(headers)
    })
    
    return secured_data, headers


# Environment configuration
def get_security_config() -> Dict[str, Any]:
    """Get security configuration from environment variables."""
    return {
        'rate_limiting_enabled': os.getenv('SECURITY_RATE_LIMITING', 'false').lower() == 'true',
        'security_headers_enabled': os.getenv('SECURITY_HEADERS', 'true').lower() == 'true',
        'security_logging_enabled': os.getenv('SECURITY_LOGGING', 'false').lower() == 'true',
        'max_request_size': int(os.getenv('MAX_REQUEST_SIZE', str(10 * 1024 * 1024))),
        'rate_limit_requests': int(os.getenv('RATE_LIMIT_REQUESTS', '60')),
        'rate_limit_window': int(os.getenv('RATE_LIMIT_WINDOW', '60'))
    }


# Usage example:
"""
# In your route handlers:

from security.simple_security import check_rate_limit, secure_response, validate_request_size

@mcp.custom_route("/mcp/tools", methods=["POST"])
async def handle_tools(request):
    client_ip = request.headers.get('X-Forwarded-For', 'unknown')
    
    # Security checks
    if not check_rate_limit(client_ip):
        return JSONResponse({"error": "Rate limit exceeded"}, status_code=429)
    
    if not validate_request_size(request.headers.get('content-length')):
        return JSONResponse({"error": "Request too large"}, status_code=413)
    
    # Process request normally
    result = await process_tools_request(request)
    
    # Secure the response
    secured_data, security_headers = secure_response(result, "/mcp/tools", client_ip)
    
    return JSONResponse(secured_data, headers=security_headers)
""" 
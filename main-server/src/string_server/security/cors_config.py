"""
Enhanced CORS configuration for MCP server deployment.

Provides environment-based CORS configuration with proper origin validation
and security controls for production deployments.
"""

import os
import re
import logging
from typing import List, Optional, Union
from urllib.parse import urlparse

from fastapi.middleware.cors import CORSMiddleware


class CORSConfig:
    """Enhanced CORS configuration with security validation."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.environment = os.getenv('ENVIRONMENT', 'development')
        
        # Load configuration from environment
        self._load_config()
    
    def _load_config(self) -> None:
        """Load CORS configuration from environment variables."""
        # Get allowed origins from environment
        origins_env = os.getenv('CORS_ORIGINS', '')
        
        if self.environment == 'production':
            # Production: Strict CORS policy
            if not origins_env or origins_env == '*':
                self.logger.warning("Production deployment with wildcard CORS detected!")
                # Try to get Railway domain
                railway_domain = self._get_railway_domain()
                if railway_domain:
                    self.allowed_origins = [railway_domain]
                    self.logger.info(f"Using Railway domain for CORS: {railway_domain}")
                else:
                    # Fallback to localhost for development testing
                    self.allowed_origins = [
                        "http://localhost:3000",
                        "http://localhost:8000",
                        "https://localhost:3000",
                        "https://localhost:8000"
                    ]
                    self.logger.warning("No Railway domain found, using localhost origins")
            else:
                self.allowed_origins = self._parse_origins(origins_env)
        else:
            # Development: More permissive but still controlled
            if origins_env == '*':
                self.allowed_origins = ["*"]
            elif origins_env:
                self.allowed_origins = self._parse_origins(origins_env)
            else:
                # Default development origins
                self.allowed_origins = [
                    "http://localhost:3000",
                    "http://localhost:8000",
                    "http://127.0.0.1:3000",
                    "http://127.0.0.1:8000",
                    "https://localhost:3000",
                    "https://localhost:8000"
                ]
        
        # Other CORS settings
        self.allow_credentials = os.getenv('CORS_ALLOW_CREDENTIALS', 'true').lower() == 'true'
        self.allow_methods = self._parse_methods(os.getenv('CORS_ALLOW_METHODS', 'GET,POST,PUT,DELETE,OPTIONS'))
        self.allow_headers = self._parse_headers(os.getenv('CORS_ALLOW_HEADERS', 'Authorization,Content-Type,X-API-Key'))
        self.expose_headers = self._parse_headers(os.getenv('CORS_EXPOSE_HEADERS', 'X-Request-ID,X-Rate-Limit-Remaining'))
        self.max_age = int(os.getenv('CORS_MAX_AGE', '86400'))  # 24 hours
        
        self.logger.info(f"CORS configured for {self.environment} environment")
        self.logger.info(f"Allowed origins: {self.allowed_origins}")
    
    def _get_railway_domain(self) -> Optional[str]:
        """Get Railway deployment domain from environment."""
        # Railway provides these environment variables
        railway_public_domain = os.getenv('RAILWAY_PUBLIC_DOMAIN')
        if railway_public_domain:
            return f"https://{railway_public_domain}"
        
        # Alternative Railway domain patterns
        railway_service_name = os.getenv('RAILWAY_SERVICE_NAME')
        railway_project_id = os.getenv('RAILWAY_PROJECT_ID')
        
        if railway_service_name and railway_project_id:
            # Construct Railway domain (this is a common pattern)
            return f"https://{railway_service_name}-{railway_project_id}.up.railway.app"
        
        # Check for custom domain
        custom_domain = os.getenv('CUSTOM_DOMAIN')
        if custom_domain:
            if not custom_domain.startswith(('http://', 'https://')):
                custom_domain = f"https://{custom_domain}"
            return custom_domain
        
        return None
    
    def _parse_origins(self, origins_str: str) -> List[str]:
        """Parse and validate origins from string."""
        origins = [origin.strip() for origin in origins_str.split(',') if origin.strip()]
        validated_origins = []
        
        for origin in origins:
            if self._is_valid_origin(origin):
                validated_origins.append(origin)
            else:
                self.logger.warning(f"Invalid origin ignored: {origin}")
        
        return validated_origins
    
    def _is_valid_origin(self, origin: str) -> bool:
        """Validate origin format and security."""
        if origin == "*":
            return self.environment != 'production'
        
        try:
            parsed = urlparse(origin)
            
            # Must have scheme and netloc
            if not parsed.scheme or not parsed.netloc:
                return False
            
            # Only allow http/https
            if parsed.scheme not in ['http', 'https']:
                return False
            
            # In production, prefer HTTPS
            if self.environment == 'production' and parsed.scheme == 'http':
                # Allow localhost for development testing
                if not parsed.netloc.startswith(('localhost', '127.0.0.1', '0.0.0.0')):
                    self.logger.warning(f"HTTP origin in production: {origin}")
            
            # Validate hostname format
            hostname = parsed.netloc.split(':')[0]  # Remove port
            if not self._is_valid_hostname(hostname):
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating origin {origin}: {e}")
            return False
    
    def _is_valid_hostname(self, hostname: str) -> bool:
        """Validate hostname format."""
        # Allow localhost and IP addresses for development
        if hostname in ['localhost', '127.0.0.1', '0.0.0.0', '::1']:
            return True
        
        # Basic hostname validation
        if len(hostname) > 253:
            return False
        
        # Check for valid characters and format
        hostname_pattern = re.compile(
            r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?'
            r'(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        )
        
        return bool(hostname_pattern.match(hostname))
    
    def _parse_methods(self, methods_str: str) -> List[str]:
        """Parse allowed HTTP methods."""
        methods = [method.strip().upper() for method in methods_str.split(',')]
        valid_methods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'HEAD', 'PATCH']
        return [method for method in methods if method in valid_methods]
    
    def _parse_headers(self, headers_str: str) -> List[str]:
        """Parse allowed headers."""
        return [header.strip() for header in headers_str.split(',') if header.strip()]
    
    def get_middleware_kwargs(self) -> dict:
        """Get kwargs for CORSMiddleware."""
        return {
            'allow_origins': self.allowed_origins,
            'allow_credentials': self.allow_credentials,
            'allow_methods': self.allow_methods,
            'allow_headers': self.allow_headers,
            'expose_headers': self.expose_headers,
            'max_age': self.max_age
        }
    
    def is_origin_allowed(self, origin: str) -> bool:
        """Check if origin is allowed."""
        if "*" in self.allowed_origins:
            return True
        
        return origin in self.allowed_origins
    
    def add_origin(self, origin: str) -> bool:
        """Add a new allowed origin if valid."""
        if self._is_valid_origin(origin) and origin not in self.allowed_origins:
            self.allowed_origins.append(origin)
            self.logger.info(f"Added new allowed origin: {origin}")
            return True
        return False
    
    def remove_origin(self, origin: str) -> bool:
        """Remove an allowed origin."""
        if origin in self.allowed_origins:
            self.allowed_origins.remove(origin)
            self.logger.info(f"Removed allowed origin: {origin}")
            return True
        return False
    
    def get_security_report(self) -> dict:
        """Get CORS security configuration report."""
        return {
            'environment': self.environment,
            'allowed_origins': self.allowed_origins,
            'allow_credentials': self.allow_credentials,
            'allow_methods': self.allow_methods,
            'allow_headers': self.allow_headers,
            'security_level': self._get_security_level(),
            'recommendations': self._get_security_recommendations()
        }
    
    def _get_security_level(self) -> str:
        """Assess CORS security level."""
        if "*" in self.allowed_origins:
            return "LOW" if self.environment == 'production' else "MEDIUM"
        
        if self.environment == 'production':
            # Check for HTTPS origins
            https_origins = [o for o in self.allowed_origins if o.startswith('https://')]
            if len(https_origins) == len(self.allowed_origins):
                return "HIGH"
            else:
                return "MEDIUM"
        
        return "MEDIUM"
    
    def _get_security_recommendations(self) -> List[str]:
        """Get security recommendations."""
        recommendations = []
        
        if "*" in self.allowed_origins and self.environment == 'production':
            recommendations.append("Remove wildcard (*) origins in production")
        
        if self.environment == 'production':
            http_origins = [o for o in self.allowed_origins if o.startswith('http://') and not o.startswith('http://localhost')]
            if http_origins:
                recommendations.append("Use HTTPS origins in production")
        
        if self.allow_credentials and "*" in self.allowed_origins:
            recommendations.append("Cannot use credentials with wildcard origins")
        
        if len(self.allowed_origins) > 10:
            recommendations.append("Consider reducing the number of allowed origins")
        
        return recommendations


def create_cors_middleware(app, environment: str = None) -> CORSMiddleware:
    """
    Create and configure CORS middleware.
    
    Args:
        app: FastAPI application
        environment: Deployment environment override
        
    Returns:
        Configured CORS middleware
    """
    if environment:
        os.environ['ENVIRONMENT'] = environment
    
    cors_config = CORSConfig()
    
    # Log security report
    report = cors_config.get_security_report()
    logger = logging.getLogger(__name__)
    logger.info(f"CORS Security Report: {report}")
    
    # Warn about security issues
    if report['security_level'] == 'LOW':
        logger.warning("CORS security level is LOW - review configuration")
    
    for recommendation in report['recommendations']:
        logger.warning(f"CORS Recommendation: {recommendation}")
    
    return CORSMiddleware(app, **cors_config.get_middleware_kwargs())


def validate_cors_config() -> dict:
    """
    Validate current CORS configuration.
    
    Returns:
        Validation report
    """
    cors_config = CORSConfig()
    return cors_config.get_security_report()
"""
Comprehensive credential scanner for detecting sensitive information in code.
Implements pattern-based and entropy-based detection with automatic alerting.
"""

import re
import logging
import hashlib
import math
from enum import Enum
from dataclasses import dataclass
from typing import List, Dict, Set, Optional, Tuple, Any
from pathlib import Path


class ThreatLevel(Enum):
    """Security threat severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class SecurityThreat:
    """Represents a detected security threat."""
    threat_type: str
    threat_level: ThreatLevel
    file_path: str
    line_number: int
    line_content: str
    pattern_name: str
    confidence: float
    context: Dict[str, Any]


class CredentialScanner:
    """
    Advanced credential scanner with pattern-based and entropy detection.
    
    Features:
    - Comprehensive regex patterns for known credential types
    - Entropy-based detection for unknown credential formats
    - File type filtering and exclusions
    - Automatic threat classification and alerting
    """
    
    def __init__(self):
        """Initialize the credential scanner with detection patterns."""
        self.logger = logging.getLogger(__name__)
        self._setup_patterns()
        self._setup_file_filters()
        
    def _setup_patterns(self) -> None:
        """Setup comprehensive credential detection patterns."""
        self.credential_patterns = {
            # API Keys and Tokens
            'aws_access_key': {
                'pattern': r'AKIA[0-9A-Z]{16}',
                'level': ThreatLevel.CRITICAL,
                'description': 'AWS Access Key ID'
            },
            'aws_secret_key': {
                'pattern': r'[A-Za-z0-9/+=]{40}',
                'level': ThreatLevel.CRITICAL,
                'description': 'AWS Secret Access Key',
                'context_required': ['aws', 'secret']
            },
            'github_token': {
                'pattern': r'ghp_[A-Za-z0-9]{36}',
                'level': ThreatLevel.HIGH,
                'description': 'GitHub Personal Access Token'
            },
            'github_oauth': {
                'pattern': r'gho_[A-Za-z0-9]{36}',
                'level': ThreatLevel.HIGH,
                'description': 'GitHub OAuth Token'
            },
            'slack_token': {
                'pattern': r'xox[baprs]-[A-Za-z0-9-]+',
                'level': ThreatLevel.HIGH,
                'description': 'Slack Token'
            },
            'discord_token': {
                'pattern': r'[MN][A-Za-z\d]{23}\.[\w-]{6}\.[\w-]{27}',
                'level': ThreatLevel.HIGH,
                'description': 'Discord Bot Token'
            },
            'stripe_key': {
                'pattern': r'sk_live_[A-Za-z0-9]{24}',
                'level': ThreatLevel.CRITICAL,
                'description': 'Stripe Live Secret Key'
            },
            'stripe_test_key': {
                'pattern': r'sk_test_[A-Za-z0-9]{24}',
                'level': ThreatLevel.MEDIUM,
                'description': 'Stripe Test Secret Key'
            },
            'twilio_sid': {
                'pattern': r'AC[a-z0-9]{32}',
                'level': ThreatLevel.HIGH,
                'description': 'Twilio Account SID'
            },
            'mailgun_key': {
                'pattern': r'key-[A-Za-z0-9]{32}',
                'level': ThreatLevel.HIGH,
                'description': 'Mailgun API Key'
            },
            'sendgrid_key': {
                'pattern': r'SG\.[A-Za-z0-9_-]{22}\.[A-Za-z0-9_-]{43}',
                'level': ThreatLevel.HIGH,
                'description': 'SendGrid API Key'
            },
            
            # Database Connection Strings
            'postgres_url': {
                'pattern': r'postgres(?:ql)?://[^\s]+',
                'level': ThreatLevel.HIGH,
                'description': 'PostgreSQL Connection String'
            },
            'mysql_url': {
                'pattern': r'mysql://[^\s]+',
                'level': ThreatLevel.HIGH,
                'description': 'MySQL Connection String'
            },
            'mongodb_url': {
                'pattern': r'mongodb(?:\+srv)?://[^\s]+',
                'level': ThreatLevel.HIGH,
                'description': 'MongoDB Connection String'
            },
            'redis_url': {
                'pattern': r'redis://[^\s]+',
                'level': ThreatLevel.MEDIUM,
                'description': 'Redis Connection String'
            },
            
            # Private Keys
            'rsa_private_key': {
                'pattern': r'-----BEGIN RSA PRIVATE KEY-----',
                'level': ThreatLevel.CRITICAL,
                'description': 'RSA Private Key'
            },
            'openssh_private_key': {
                'pattern': r'-----BEGIN OPENSSH PRIVATE KEY-----',
                'level': ThreatLevel.CRITICAL,
                'description': 'OpenSSH Private Key'
            },
            'ec_private_key': {
                'pattern': r'-----BEGIN EC PRIVATE KEY-----',
                'level': ThreatLevel.CRITICAL,
                'description': 'EC Private Key'
            },
            'dsa_private_key': {
                'pattern': r'-----BEGIN DSA PRIVATE KEY-----',
                'level': ThreatLevel.CRITICAL,
                'description': 'DSA Private Key'
            },
            
            # Generic Patterns
            'generic_api_key': {
                'pattern': r'(?i)(?:api[_-]?key|apikey)\s*[:=]\s*["\']?([A-Za-z0-9_-]{20,})["\']?',
                'level': ThreatLevel.MEDIUM,
                'description': 'Generic API Key'
            },
            'generic_secret': {
                'pattern': r'(?i)(?:secret|password|passwd|pwd)\s*[:=]\s*["\']?([A-Za-z0-9_@#$%^&*!-]{8,})["\']?',
                'level': ThreatLevel.MEDIUM,
                'description': 'Generic Secret/Password'
            },
            'generic_token': {
                'pattern': r'(?i)(?:token|auth[_-]?token)\s*[:=]\s*["\']?([A-Za-z0-9_-]{20,})["\']?',
                'level': ThreatLevel.MEDIUM,
                'description': 'Generic Token'
            },
            
            # Cloud Provider Keys
            'azure_key': {
                'pattern': r'[A-Za-z0-9+/]{88}==',
                'level': ThreatLevel.HIGH,
                'description': 'Azure Storage Key',
                'context_required': ['azure', 'storage']
            },
            'gcp_key': {
                'pattern': r'"type":\s*"service_account"',
                'level': ThreatLevel.CRITICAL,
                'description': 'Google Cloud Service Account Key'
            },
            'cloudflare_key': {
                'pattern': r'[A-Za-z0-9_-]{40}',
                'level': ThreatLevel.HIGH,
                'description': 'Cloudflare API Key',
                'context_required': ['cloudflare', 'api']
            }
        }
        
        # Compile regex patterns for performance
        self.compiled_patterns = {}
        for name, config in self.credential_patterns.items():
            try:
                self.compiled_patterns[name] = re.compile(config['pattern'])
            except re.error as e:
                self.logger.warning(f"Invalid regex pattern for {name}: {e}")
    
    def _setup_file_filters(self) -> None:
        """Setup file type filters and exclusions."""
        # File extensions to scan
        self.scannable_extensions = {
            '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.go', '.rs',
            '.php', '.rb', '.cs', '.cpp', '.c', '.h', '.hpp',
            '.yaml', '.yml', '.json', '.xml', '.toml', '.ini', '.cfg',
            '.env', '.config', '.conf', '.properties',
            '.sh', '.bash', '.zsh', '.fish', '.ps1', '.bat', '.cmd',
            '.sql', '.dockerfile', '.docker-compose.yml',
            '.tf', '.tfvars', '.hcl'
        }
        
        # Directories to exclude
        self.excluded_dirs = {
            '.git', '.svn', '.hg', '.bzr',
            'node_modules', '__pycache__', '.pytest_cache',
            'venv', 'env', '.venv', '.env',
            'build', 'dist', 'target', 'out',
            '.idea', '.vscode', '.vs',
            'logs', 'log', 'tmp', 'temp'
        }
        
        # Files to exclude
        self.excluded_files = {
            '.gitignore', '.dockerignore', '.eslintignore',
            'package-lock.json', 'yarn.lock', 'poetry.lock',
            'requirements.txt', 'Pipfile.lock'
        }
    
    def calculate_entropy(self, text: str) -> float:
        """
        Calculate Shannon entropy of a string.
        
        Args:
            text: String to analyze
            
        Returns:
            Entropy value (higher = more random)
        """
        if not text:
            return 0.0
            
        # Count character frequencies
        char_counts = {}
        for char in text:
            char_counts[char] = char_counts.get(char, 0) + 1
        
        # Calculate entropy
        entropy = 0.0
        text_len = len(text)
        for count in char_counts.values():
            probability = count / text_len
            if probability > 0:
                entropy -= probability * math.log2(probability)
        
        return entropy
    
    def is_high_entropy_string(self, text: str, min_length: int = 20,
                              entropy_threshold: float = 4.5) -> bool:
        """
        Check if a string has high entropy (likely a credential).
        
        Args:
            text: String to analyze
            min_length: Minimum length to consider
            entropy_threshold: Minimum entropy threshold
            
        Returns:
            True if string appears to be high-entropy credential
        """
        if len(text) < min_length:
            return False
            
        entropy = self.calculate_entropy(text)
        return entropy >= entropy_threshold
    
    def extract_strings_from_line(self, line: str) -> List[str]:
        """
        Extract potential credential strings from a line of code.
        
        Args:
            line: Line of code to analyze
            
        Returns:
            List of extracted strings
        """
        strings = []
        
        # Extract quoted strings
        quote_patterns = [
            r'"([^"\\]|\\.)*"',  # Double quotes
            r"'([^'\\]|\\.)*'",  # Single quotes
            r'`([^`\\]|\\.)*`'   # Backticks
        ]
        
        for pattern in quote_patterns:
            matches = re.findall(pattern, line)
            for match in matches:
                # Remove quotes
                clean_string = match[1:-1] if len(match) >= 2 else match
                if clean_string:
                    strings.append(clean_string)
        
        # Extract assignment values
        assignment_pattern = r'[:=]\s*([A-Za-z0-9+/=_-]{10,})'
        matches = re.findall(assignment_pattern, line)
        strings.extend(matches)
        
        return strings
    
    def scan_line(self, line: str, line_number: int, 
                  file_path: str) -> List[SecurityThreat]:
        """
        Scan a single line for credential patterns.
        
        Args:
            line: Line content to scan
            line_number: Line number in file
            file_path: Path to the file being scanned
            
        Returns:
            List of detected security threats
        """
        threats = []
        line_lower = line.lower()
        
        # Skip comments and obvious false positives
        if (line.strip().startswith('#') or 
            line.strip().startswith('//') or
            'example' in line_lower or
            'placeholder' in line_lower or
            'your_key_here' in line_lower or
            'xxx' in line_lower):
            return threats
        
        # Pattern-based detection
        for pattern_name, config in self.credential_patterns.items():
            if pattern_name not in self.compiled_patterns:
                continue
                
            pattern = self.compiled_patterns[pattern_name]
            matches = pattern.finditer(line)
            
            for match in matches:
                # Check context requirements
                if 'context_required' in config:
                    context_found = any(ctx in line_lower 
                                      for ctx in config['context_required'])
                    if not context_found:
                        continue
                
                threat = SecurityThreat(
                    threat_type='credential_pattern',
                    threat_level=config['level'],
                    file_path=file_path,
                    line_number=line_number,
                    line_content=line.strip(),
                    pattern_name=pattern_name,
                    confidence=0.9,
                    context={
                        'description': config['description'],
                        'matched_text': match.group(0),
                        'match_start': match.start(),
                        'match_end': match.end()
                    }
                )
                threats.append(threat)
        
        # Entropy-based detection
        extracted_strings = self.extract_strings_from_line(line)
        for string in extracted_strings:
            if self.is_high_entropy_string(string):
                # Additional checks to reduce false positives
                if (len(string) >= 20 and 
                    not string.isdigit() and
                    not all(c in 'abcdefghijklmnopqrstuvwxyz' for c in string.lower())):
                    
                    threat = SecurityThreat(
                        threat_type='high_entropy_string',
                        threat_level=ThreatLevel.MEDIUM,
                        file_path=file_path,
                        line_number=line_number,
                        line_content=line.strip(),
                        pattern_name='entropy_detection',
                        confidence=0.7,
                        context={
                            'description': 'High entropy string (possible credential)',
                            'entropy': self.calculate_entropy(string),
                            'string_length': len(string),
                            'detected_string': string[:20] + '...' if len(string) > 20 else string
                        }
                    )
                    threats.append(threat)
        
        return threats
    
    def should_scan_file(self, file_path: Path) -> bool:
        """
        Determine if a file should be scanned.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if file should be scanned
        """
        # Check if file extension is scannable
        if file_path.suffix.lower() not in self.scannable_extensions:
            # Special case for files without extensions
            if file_path.suffix == '' and file_path.name not in {
                'Dockerfile', 'Makefile', 'Jenkinsfile'
            }:
                return False
        
        # Check excluded files
        if file_path.name in self.excluded_files:
            return False
        
        # Check excluded directories
        for part in file_path.parts:
            if part in self.excluded_dirs:
                return False
        
        return True
    
    def scan_file(self, file_path: str) -> List[SecurityThreat]:
        """
        Scan a single file for credentials.
        
        Args:
            file_path: Path to file to scan
            
        Returns:
            List of detected security threats
        """
        threats = []
        path_obj = Path(file_path)
        
        if not self.should_scan_file(path_obj):
            return threats
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line_number, line in enumerate(f, 1):
                    line_threats = self.scan_line(line, line_number, file_path)
                    threats.extend(line_threats)
                    
        except (IOError, OSError) as e:
            self.logger.warning(f"Could not read file {file_path}: {e}")
        
        return threats
    
    def scan_directory(self, directory_path: str, 
                      recursive: bool = True) -> List[SecurityThreat]:
        """
        Scan a directory for credentials.
        
        Args:
            directory_path: Path to directory to scan
            recursive: Whether to scan subdirectories
            
        Returns:
            List of detected security threats
        """
        threats = []
        dir_path = Path(directory_path)
        
        if not dir_path.exists() or not dir_path.is_dir():
            self.logger.error(f"Directory does not exist: {directory_path}")
            return threats
        
        try:
            if recursive:
                for file_path in dir_path.rglob('*'):
                    if file_path.is_file():
                        file_threats = self.scan_file(str(file_path))
                        threats.extend(file_threats)
            else:
                for file_path in dir_path.iterdir():
                    if file_path.is_file():
                        file_threats = self.scan_file(str(file_path))
                        threats.extend(file_threats)
                        
        except (IOError, OSError) as e:
            self.logger.error(f"Error scanning directory {directory_path}: {e}")
        
        return threats
    
    def generate_threat_report(self, threats: List[SecurityThreat]) -> Dict[str, Any]:
        """
        Generate a comprehensive threat report.
        
        Args:
            threats: List of detected threats
            
        Returns:
            Detailed threat report
        """
        if not threats:
            return {
                'status': 'clean',
                'total_threats': 0,
                'threat_summary': {},
                'recommendations': ['No security threats detected.']
            }
        
        # Categorize threats by level
        threat_counts = {level.value: 0 for level in ThreatLevel}
        threat_types = {}
        affected_files = set()
        
        for threat in threats:
            threat_counts[threat.threat_level.value] += 1
            affected_files.add(threat.file_path)
            
            if threat.threat_type not in threat_types:
                threat_types[threat.threat_type] = 0
            threat_types[threat.threat_type] += 1
        
        # Generate recommendations
        recommendations = []
        if threat_counts['critical'] > 0:
            recommendations.append(
                "CRITICAL: Immediately remove or secure critical credentials!"
            )
        if threat_counts['high'] > 0:
            recommendations.append(
                "HIGH: Review and secure high-risk credentials."
            )
        if threat_counts['medium'] > 0:
            recommendations.append(
                "MEDIUM: Verify and potentially secure medium-risk items."
            )
        
        recommendations.extend([
            "Use environment variables for sensitive configuration.",
            "Implement proper secrets management (e.g., HashiCorp Vault).",
            "Add credential scanning to your CI/CD pipeline.",
            "Review .gitignore to prevent accidental commits."
        ])
        
        return {
            'status': 'threats_detected',
            'total_threats': len(threats),
            'affected_files': len(affected_files),
            'threat_summary': {
                'by_level': threat_counts,
                'by_type': threat_types
            },
            'threats': [
                {
                    'type': threat.threat_type,
                    'level': threat.threat_level.value,
                    'file': threat.file_path,
                    'line': threat.line_number,
                    'pattern': threat.pattern_name,
                    'confidence': threat.confidence,
                    'description': threat.context.get('description', ''),
                    'line_content': threat.line_content
                }
                for threat in threats
            ],
            'recommendations': recommendations
        }
    
    def auto_delete_threat_files(self, threats: List[SecurityThreat], 
                                dry_run: bool = True) -> Dict[str, Any]:
        """
        Automatically delete files containing critical threats.
        
        Args:
            threats: List of detected threats
            dry_run: If True, only simulate deletion
            
        Returns:
            Deletion report
        """
        critical_threats = [t for t in threats 
                          if t.threat_level == ThreatLevel.CRITICAL]
        
        if not critical_threats:
            return {
                'action': 'no_deletion_needed',
                'critical_threats': 0,
                'files_processed': 0
            }
        
        files_to_delete = set(t.file_path for t in critical_threats)
        deleted_files = []
        failed_deletions = []
        
        for file_path in files_to_delete:
            try:
                if not dry_run:
                    Path(file_path).unlink()
                    self.logger.critical(
                        f"SECURITY: Deleted file with critical threats: {file_path}"
                    )
                deleted_files.append(file_path)
            except (IOError, OSError) as e:
                failed_deletions.append({
                    'file': file_path,
                    'error': str(e)
                })
                self.logger.error(f"Failed to delete {file_path}: {e}")
        
        return {
            'action': 'deletion_attempted' if not dry_run else 'dry_run',
            'critical_threats': len(critical_threats),
            'files_to_delete': len(files_to_delete),
            'deleted_files': deleted_files,
            'failed_deletions': failed_deletions
        }
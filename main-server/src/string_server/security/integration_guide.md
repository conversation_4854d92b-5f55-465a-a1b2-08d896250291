# 🛡️ Safe Security Integration Guide

## Overview

This guide shows how to add security to your MCP server **WITHOUT breaking Railway health checks** or interfering with FastMCP functionality.

## ✅ **APPROACH 1: Simple Function-Based Security (RECOMMENDED)**

This approach adds security through function calls in your existing routes, requiring minimal changes.

### Step 1: Import Security Functions

```python
# Add to your server.py imports
from security.simple_security import (
    check_rate_limit, 
    secure_response, 
    validate_request_size,
    get_security_config
)
```

### Step 2: Add Security to MCP Routes Only

```python
# Example: Secure your MCP tools endpoint
@mcp.custom_route("/mcp/tools", methods=["POST"])
async def handle_tools(request):
    # Get client IP (Railway compatible)
    client_ip = request.headers.get('X-Forwarded-For', 
                                   request.headers.get('X-Real-IP', 'unknown'))
    
    # Security checks (only if enabled via env vars)
    if not check_rate_limit(client_ip):
        return JSONResponse({"error": "Rate limit exceeded"}, status_code=429)
    
    if not validate_request_size(request.headers.get('content-length')):
        return JSONResponse({"error": "Request too large"}, status_code=413)
    
    # YOUR EXISTING CODE STAYS THE SAME
    result = await process_tools_request(request)
    
    # Add security headers to response
    secured_data, security_headers = secure_response(result, "/mcp/tools", client_ip)
    
    return JSONResponse(secured_data, headers=security_headers)
```

### Step 3: Keep Health Checks Untouched

```python
# Health check routes stay EXACTLY the same
@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.get("/status") 
async def status():
    return {"status": "ok"}
```

## ✅ **APPROACH 2: Optional Route Wrapper (IF NEEDED)**

If you want to secure multiple routes easily:

```python
from security.simple_security import secure_response, check_rate_limit

def secure_route(path: str):
    """Decorator to add security to specific routes."""
    def decorator(func):
        async def wrapper(request):
            # Skip security for health checks
            if path in ["/health", "/status"]:
                return await func(request)
            
            client_ip = request.headers.get('X-Forwarded-For', 'unknown')
            
            # Apply security checks
            if not check_rate_limit(client_ip):
                return JSONResponse({"error": "Rate limit exceeded"}, status_code=429)
            
            # Call original function
            result = await func(request)
            
            # Secure the response if it's JSON
            if hasattr(result, 'body'):
                return result  # Return as-is for non-JSON responses
            
            secured_data, headers = secure_response(result, path, client_ip)
            return JSONResponse(secured_data, headers=headers)
        
        return wrapper
    return decorator

# Usage:
@mcp.custom_route("/mcp/tools", methods=["POST"])
@secure_route("/mcp/tools")
async def handle_tools(request):
    # Your existing code
    return await process_tools_request(request)
```

## 🔧 **Environment Configuration**

Control security features via environment variables:

```bash
# Railway Environment Variables (all optional)
SECURITY_RATE_LIMITING=true        # Enable rate limiting
SECURITY_HEADERS=true              # Enable security headers  
SECURITY_LOGGING=false             # Enable security event logging
MAX_REQUEST_SIZE=10485760          # 10MB request limit
RATE_LIMIT_REQUESTS=60             # 60 requests per minute
RATE_LIMIT_WINDOW=60               # 60 second window
```

## 🚀 **Railway Deployment Configuration**

Add these to your Railway environment variables:

```bash
# Production security settings
SECURITY_HEADERS=true
SECURITY_RATE_LIMITING=true
SECURITY_LOGGING=false

# Development settings (lighter security)
SECURITY_HEADERS=true
SECURITY_RATE_LIMITING=false
SECURITY_LOGGING=false
```

## ✅ **Benefits of This Approach**

1. **Railway Compatible**: Health checks remain fast and simple
2. **Zero Breaking Changes**: Existing code continues to work
3. **Selective Security**: Only protect endpoints that need it
4. **Environment Controlled**: Enable/disable via env vars
5. **Performance Friendly**: No middleware overhead for health checks
6. **FastMCP Compliant**: Doesn't interfere with MCP protocol

## 🛠️ **Implementation Steps**

### Minimal Integration (5 minutes):

1. Copy the security files to your project
2. Add one import to server.py
3. Add 3 lines to your MCP routes
4. Set environment variables in Railway
5. Deploy!

### Example Minimal Change:

```python
# Before
@mcp.custom_route("/mcp/info", methods=["GET"])
async def mcp_info(request):
    return {"name": "String MCP Server", "version": "1.0.0"}

# After (just 4 lines added)
@mcp.custom_route("/mcp/info", methods=["GET"])  
async def mcp_info(request):
    from security.simple_security import secure_response
    client_ip = request.headers.get('X-Forwarded-For', 'unknown')
    result = {"name": "String MCP Server", "version": "1.0.0"}
    secured_data, headers = secure_response(result, "/mcp/info", client_ip)
    return JSONResponse(secured_data, headers=headers)
```

## 🔍 **Testing**

```bash
# Test health check (should be fast and unchanged)
curl https://your-app.railway.app/health

# Test MCP endpoint (should have security headers)
curl -I https://your-app.railway.app/mcp/info
```

## 🎯 **Next Steps**

1. Start with **Approach 1** (recommended)
2. Test with health checks first
3. Gradually add security to MCP routes
4. Monitor performance in Railway
5. Enable rate limiting in production

This approach gives you **enterprise-grade security** without risking your **Railway deployment stability**. 
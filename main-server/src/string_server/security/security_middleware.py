"""
Security middleware for enhanced MCP server security.

Provides comprehensive security headers, HTTPS enforcement, rate limiting,
and security monitoring for production deployments.
"""

import os
import time
import logging
import hashlib
import secrets
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from collections import defaultdict, deque
from dataclasses import dataclass, field

from fastapi import Request, Response, HTTPException
from fastapi.middleware.base import BaseHTTPMiddleware
from fastapi.responses import JSONResponse
from starlette.types import ASGIApp


@dataclass
class SecurityConfig:
    """Enhanced security configuration."""
    
    # HTTPS and SSL/TLS
    force_https: bool = True
    hsts_max_age: int = 31536000  # 1 year
    hsts_include_subdomains: bool = True
    hsts_preload: bool = True
    
    # Security Headers
    enable_security_headers: bool = True
    csp_policy: str = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:; frame-ancestors 'none';"
    x_frame_options: str = "DENY"
    x_content_type_options: str = "nosniff"
    x_xss_protection: str = "1; mode=block"
    referrer_policy: str = "strict-origin-when-cross-origin"
    permissions_policy: str = "geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()"
    
    # Rate Limiting
    enable_rate_limiting: bool = True
    requests_per_minute: int = 60
    requests_per_hour: int = 1000
    burst_limit: int = 10
    
    # Request Validation
    max_request_size: int = 10 * 1024 * 1024  # 10MB
    max_header_size: int = 8192  # 8KB
    max_url_length: int = 2048
    
    # Security Monitoring
    enable_security_logging: bool = True
    log_suspicious_requests: bool = True
    block_suspicious_requests: bool = True
    
    # IP Security
    trusted_proxies: List[str] = field(default_factory=lambda: ['127.0.0.1', '::1'])
    blocked_ips: List[str] = field(default_factory=list)
    
    # Cookie Security
    secure_cookies: bool = True
    samesite_cookies: str = "Strict"
    httponly_cookies: bool = True


class RateLimiter:
    """Advanced rate limiter with sliding window and burst protection."""
    
    def __init__(self, config: SecurityConfig):
        self.config = config
        self.requests: Dict[str, deque] = defaultdict(deque)
        self.blocked_ips: Dict[str, datetime] = {}
        self.logger = logging.getLogger(__name__)
    
    def is_allowed(self, client_ip: str) -> Tuple[bool, Optional[str]]:
        """Check if request is allowed based on rate limits."""
        now = datetime.utcnow()
        
        # Check if IP is temporarily blocked
        if client_ip in self.blocked_ips:
            if now < self.blocked_ips[client_ip]:
                return False, "IP temporarily blocked due to rate limit violation"
            else:
                del self.blocked_ips[client_ip]
        
        # Clean old requests
        minute_ago = now - timedelta(minutes=1)
        hour_ago = now - timedelta(hours=1)
        
        requests = self.requests[client_ip]
        
        # Remove old requests
        while requests and requests[0] < hour_ago:
            requests.popleft()
        
        # Count recent requests
        minute_requests = sum(1 for req_time in requests if req_time > minute_ago)
        hour_requests = len(requests)
        
        # Check limits
        if minute_requests >= self.config.requests_per_minute:
            self._block_ip(client_ip, minutes=5)
            return False, f"Rate limit exceeded: {minute_requests} requests in last minute"
        
        if hour_requests >= self.config.requests_per_hour:
            self._block_ip(client_ip, minutes=60)
            return False, f"Rate limit exceeded: {hour_requests} requests in last hour"
        
        # Check burst limit (last 10 seconds)
        ten_seconds_ago = now - timedelta(seconds=10)
        burst_requests = sum(1 for req_time in requests if req_time > ten_seconds_ago)
        
        if burst_requests >= self.config.burst_limit:
            self._block_ip(client_ip, minutes=1)
            return False, f"Burst limit exceeded: {burst_requests} requests in last 10 seconds"
        
        # Record this request
        requests.append(now)
        return True, None
    
    def _block_ip(self, client_ip: str, minutes: int) -> None:
        """Temporarily block an IP address."""
        block_until = datetime.utcnow() + timedelta(minutes=minutes)
        self.blocked_ips[client_ip] = block_until
        self.logger.warning(f"Temporarily blocked IP {client_ip} until {block_until}")


class SecurityMonitor:
    """Security event monitoring and alerting."""
    
    def __init__(self, config: SecurityConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.security_events: deque = deque(maxlen=1000)
        self.suspicious_patterns = [
            r'\.\./',  # Path traversal
            r'<script',  # XSS attempts
            r'union\s+select',  # SQL injection
            r'exec\s*\(',  # Code execution
            r'eval\s*\(',  # Code evaluation
            r'system\s*\(',  # System commands
            r'base64_decode',  # Encoded payloads
            r'javascript:',  # JavaScript URLs
            r'vbscript:',  # VBScript URLs
        ]
    
    def log_security_event(self, event_type: str, client_ip: str, 
                          details: Dict[str, Any]) -> None:
        """Log a security event."""
        event = {
            'timestamp': datetime.utcnow().isoformat(),
            'type': event_type,
            'client_ip': client_ip,
            'details': details
        }
        
        self.security_events.append(event)
        
        if self.config.enable_security_logging:
            self.logger.warning(f"Security event: {event_type} from {client_ip} - {details}")
    
    def is_suspicious_request(self, request: Request) -> Tuple[bool, Optional[str]]:
        """Check if request contains suspicious patterns."""
        if not self.config.log_suspicious_requests:
            return False, None
        
        # Check URL
        url_str = str(request.url)
        for pattern in self.suspicious_patterns:
            if pattern.lower() in url_str.lower():
                return True, f"Suspicious pattern in URL: {pattern}"
        
        # Check headers
        for header_name, header_value in request.headers.items():
            header_str = f"{header_name}: {header_value}".lower()
            for pattern in self.suspicious_patterns:
                if pattern.lower() in header_str:
                    return True, f"Suspicious pattern in header {header_name}: {pattern}"
        
        return False, None


class SecurityMiddleware(BaseHTTPMiddleware):
    """Comprehensive security middleware for FastAPI applications."""
    
    def __init__(self, app: ASGIApp, config: Optional[SecurityConfig] = None):
        super().__init__(app)
        self.config = config or SecurityConfig()
        self.rate_limiter = RateLimiter(self.config)
        self.security_monitor = SecurityMonitor(self.config)
        self.logger = logging.getLogger(__name__)
        
        # Load configuration from environment
        self._load_env_config()
    
    def _load_env_config(self) -> None:
        """Load configuration from environment variables."""
        env = os.getenv('ENVIRONMENT', 'development')
        
        # Production security settings
        if env == 'production':
            self.config.force_https = os.getenv('SECURITY_FORCE_HTTPS', 'true').lower() == 'true'
            self.config.enable_security_headers = True
            self.config.enable_rate_limiting = True
            self.config.block_suspicious_requests = True
        
        # Rate limiting configuration
        self.config.requests_per_minute = int(os.getenv('SECURITY_REQUESTS_PER_MINUTE', '60'))
        self.config.requests_per_hour = int(os.getenv('SECURITY_REQUESTS_PER_HOUR', '1000'))
        self.config.burst_limit = int(os.getenv('SECURITY_BURST_LIMIT', '10'))
        
        # Security headers configuration
        csp_env = os.getenv('SECURITY_CSP_POLICY')
        if csp_env:
            self.config.csp_policy = csp_env
        
        # Trusted proxies (for Railway deployment)
        proxies_env = os.getenv('SECURITY_TRUSTED_PROXIES')
        if proxies_env:
            self.config.trusted_proxies = [ip.strip() for ip in proxies_env.split(',')]
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """Process request through security middleware."""
        start_time = time.time()
        client_ip = self._get_client_ip(request)
        
        try:
            # 1. HTTPS Enforcement
            if self.config.force_https and not self._is_https_request(request):
                return self._redirect_to_https(request)
            
            # 2. Request Size Validation
            if not self._validate_request_size(request):
                self.security_monitor.log_security_event(
                    'request_too_large', client_ip,
                    {'url': str(request.url), 'method': request.method}
                )
                raise HTTPException(status_code=413, detail="Request too large")
            
            # 3. Rate Limiting
            if self.config.enable_rate_limiting:
                allowed, reason = self.rate_limiter.is_allowed(client_ip)
                if not allowed:
                    self.security_monitor.log_security_event(
                        'rate_limit_exceeded', client_ip,
                        {'reason': reason, 'url': str(request.url)}
                    )
                    return JSONResponse(
                        status_code=429,
                        content={'error': 'Rate limit exceeded', 'message': reason}
                    )
            
            # 4. Suspicious Request Detection
            is_suspicious, suspicious_reason = self.security_monitor.is_suspicious_request(request)
            if is_suspicious:
                self.security_monitor.log_security_event(
                    'suspicious_request', client_ip,
                    {'reason': suspicious_reason, 'url': str(request.url), 'method': request.method}
                )
                
                if self.config.block_suspicious_requests:
                    return JSONResponse(
                        status_code=400,
                        content={'error': 'Suspicious request blocked', 'message': 'Request contains suspicious patterns'}
                    )
            
            # 5. Process Request
            response = await call_next(request)
            
            # 6. Add Security Headers
            if self.config.enable_security_headers:
                self._add_security_headers(response)
            
            # 7. Log Request
            processing_time = time.time() - start_time
            self._log_request(request, response, client_ip, processing_time)
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Security middleware error: {e}")
            self.security_monitor.log_security_event(
                'middleware_error', client_ip,
                {'error': str(e), 'url': str(request.url)}
            )
            raise HTTPException(status_code=500, detail="Internal security error")
    
    def _get_client_ip(self, request: Request) -> str:
        """Get the real client IP address."""
        # Check for forwarded headers (Railway, Cloudflare, etc.)
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            # Take the first IP in the chain
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            return real_ip
        
        # Fallback to direct connection
        if hasattr(request, 'client') and request.client:
            return request.client.host
        
        return 'unknown'
    
    def _is_https_request(self, request: Request) -> bool:
        """Check if request is using HTTPS."""
        # Check scheme
        if request.url.scheme == 'https':
            return True
        
        # Check forwarded protocol (for proxies)
        forwarded_proto = request.headers.get('X-Forwarded-Proto')
        if forwarded_proto == 'https':
            return True
        
        # Check if running in development
        if os.getenv('ENVIRONMENT') == 'development':
            return True
        
        return False
    
    def _redirect_to_https(self, request: Request) -> Response:
        """Redirect HTTP requests to HTTPS."""
        https_url = request.url.replace(scheme='https')
        return JSONResponse(
            status_code=301,
            content={'error': 'HTTPS required', 'redirect_url': str(https_url)},
            headers={'Location': str(https_url)}
        )
    
    def _validate_request_size(self, request: Request) -> bool:
        """Validate request size limits."""
        # Check content length
        content_length = request.headers.get('content-length')
        if content_length:
            try:
                size = int(content_length)
                if size > self.config.max_request_size:
                    return False
            except ValueError:
                return False
        
        # Check URL length
        if len(str(request.url)) > self.config.max_url_length:
            return False
        
        # Check header size
        total_header_size = sum(len(f"{k}: {v}") for k, v in request.headers.items())
        if total_header_size > self.config.max_header_size:
            return False
        
        return True
    
    def _add_security_headers(self, response: Response) -> None:
        """Add comprehensive security headers."""
        # HSTS (HTTP Strict Transport Security)
        if self.config.force_https:
            hsts_value = f"max-age={self.config.hsts_max_age}"
            if self.config.hsts_include_subdomains:
                hsts_value += "; includeSubDomains"
            if self.config.hsts_preload:
                hsts_value += "; preload"
            response.headers["Strict-Transport-Security"] = hsts_value
        
        # Content Security Policy
        response.headers["Content-Security-Policy"] = self.config.csp_policy
        
        # X-Frame-Options
        response.headers["X-Frame-Options"] = self.config.x_frame_options
        
        # X-Content-Type-Options
        response.headers["X-Content-Type-Options"] = self.config.x_content_type_options
        
        # X-XSS-Protection
        response.headers["X-XSS-Protection"] = self.config.x_xss_protection
        
        # Referrer Policy
        response.headers["Referrer-Policy"] = self.config.referrer_policy
        
        # Permissions Policy
        response.headers["Permissions-Policy"] = self.config.permissions_policy
        
        # Additional security headers
        response.headers["X-Permitted-Cross-Domain-Policies"] = "none"
        response.headers["Cross-Origin-Embedder-Policy"] = "require-corp"
        response.headers["Cross-Origin-Opener-Policy"] = "same-origin"
        response.headers["Cross-Origin-Resource-Policy"] = "same-origin"
        
        # Server identification
        response.headers["Server"] = "MCP-Server/1.0"
        
        # Cache control for sensitive endpoints
        if any(path in str(response.headers.get('location', '')) for path in ['/api/', '/admin/']):
            response.headers["Cache-Control"] = "no-store, no-cache, must-revalidate, private"
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"
    
    def _log_request(self, request: Request, response: Response, 
                    client_ip: str, processing_time: float) -> None:
        """Log request details for security monitoring."""
        if not self.config.enable_security_logging:
            return
        
        log_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'client_ip': client_ip,
            'method': request.method,
            'url': str(request.url),
            'status_code': response.status_code,
            'processing_time': round(processing_time, 3),
            'user_agent': request.headers.get('User-Agent', 'unknown'),
            'referer': request.headers.get('Referer', 'none')
        }
        
        # Log as JSON for structured logging
        self.logger.info(f"Request: {log_data}")


def create_security_middleware(app: ASGIApp, 
                             environment: str = 'production') -> SecurityMiddleware:
    """
    Create and configure security middleware.
    
    Args:
        app: FastAPI application
        environment: Deployment environment
        
    Returns:
        Configured security middleware
    """
    config = SecurityConfig()
    
    # Environment-specific configuration
    if environment == 'development':
        config.force_https = False
        config.enable_rate_limiting = False
        config.block_suspicious_requests = False
    elif environment == 'production':
        config.force_https = True
        config.enable_rate_limiting = True
        config.block_suspicious_requests = True
        config.enable_security_logging = True
    
    return SecurityMiddleware(app, config)
"""
Safe Security Integration for MCP Server

Provides optional security enhancement without breaking existing functionality.
Can be enabled/disabled via environment variables.
"""

import os
import logging
from typing import Optional

from mcp.server.fastmcp import FastMC<PERSON>


def enhance_server_security(mcp_server: FastMCP, environment: str = 'production') -> bool:
    """
    Safely enhance MCP server with security middleware.
    
    This function adds security without breaking:
    - Railway health checks
    - FastMCP core functionality  
    - Existing endpoints
    
    Args:
        mcp_server: FastMCP server instance
        environment: Deployment environment
        
    Returns:
        True if security was enhanced, False if skipped
    """
    logger = logging.getLogger(__name__)
    
    # Check if security enhancement is enabled
    enable_security = os.getenv('ENABLE_SECURITY_MIDDLEWARE', 'false').lower() == 'true'
    
    if not enable_security:
        logger.info("Security middleware disabled (ENABLE_SECURITY_MIDDLEWARE=false)")
        return False
    
    try:
        # Import here to avoid dependency issues if not needed
        from .selective_middleware import create_selective_middleware
        
        # Create selective middleware that preserves health checks
        middleware = create_selective_middleware(mcp_server, environment)
        
        # Try to add middleware to the underlying FastAPI/Starlette app
        if hasattr(mcp_server, 'app') and hasattr(mcp_server.app, 'add_middleware'):
            mcp_server.app.add_middleware(type(middleware), **middleware.config)
            logger.info("✅ Security middleware successfully added to underlying app")
            return True
        else:
            logger.warning("Cannot add middleware - using route-level security instead")
            return False
            
    except Exception as e:
        logger.error(f"Failed to enhance security: {e}")
        logger.info("Continuing without security middleware")
        return False


def add_security_to_custom_routes(mcp_server: FastMCP) -> None:
    """
    Add security headers to custom routes manually.
    
    This is a fallback when middleware can't be added.
    """
    logger = logging.getLogger(__name__)
    
    try:
        # Override the existing /mcp/info route with security headers
        @mcp_server.custom_route("/mcp/info", methods=["GET"])
        async def secure_mcp_info(request):
            from starlette.responses import JSONResponse
            
            response = JSONResponse({
                "name": "String MCP Server",
                "version": "1.0.0",
                "transport": "streamable-http",
                "capabilities": ["tools", "resources", "prompts"],
                "mount_path": "/mcp",
                "stateless": True,
                "json_response": True,
                "force_json": True,
                "security_enhanced": True
            }, headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "*",
                # Security headers
                "X-Content-Type-Options": "nosniff",
                "X-Frame-Options": "DENY",
                "X-XSS-Protection": "1; mode=block",
                "Server": "MCP-Server/1.0"
            })
            
            return response
        
        logger.info("✅ Security headers added to custom routes")
        
    except Exception as e:
        logger.error(f"Failed to add route-level security: {e}")


def configure_environment_security() -> dict:
    """
    Configure security settings based on environment variables.
    
    Environment Variables:
    - ENABLE_SECURITY_MIDDLEWARE: Enable/disable middleware
    - SECURITY_RATE_LIMITING: Enable rate limiting  
    - SECURITY_HEADERS: Enable security headers
    - SECURITY_LOGGING: Enable security logging
    - ENVIRONMENT: production/development/test
    
    Returns:
        Security configuration dictionary
    """
    config = {
        'middleware_enabled': os.getenv('ENABLE_SECURITY_MIDDLEWARE', 'false').lower() == 'true',
        'environment': os.getenv('ENVIRONMENT', 'production'),
        'rate_limiting': os.getenv('SECURITY_RATE_LIMITING', 'false').lower() == 'true',
        'security_headers': os.getenv('SECURITY_HEADERS', 'true').lower() == 'true',
        'security_logging': os.getenv('SECURITY_LOGGING', 'false').lower() == 'true'
    }
    
    logger = logging.getLogger(__name__)
    logger.info(f"Security configuration: {config}")
    
    return config


# Usage example for server.py integration:
"""
# In server.py, add this after creating the FastMCP server:

from security.safe_integration import enhance_server_security, add_security_to_custom_routes

# Try to enhance with middleware first
security_added = enhance_server_security(mcp)

# Fallback to route-level security if middleware fails
if not security_added:
    add_security_to_custom_routes(mcp)
""" 
"""
Selective Security Middleware for MCP Server

Applies security measures only to specific endpoints while preserving:
- Railway health check functionality
- FastMCP core operations  
- Performance for critical endpoints
"""

import os
import time
import logging
from typing import List, Optional, Callable, Awaitable
from datetime import datetime

from starlette.requests import Request
from starlette.responses import Response, JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware


class SelectiveSecurityMiddleware(BaseHTTPMiddleware):
    """
    Security middleware that selectively applies protections.
    
    Skips security for:
    - Health check endpoints (/health, /status)
    - FastMCP internal operations
    - Railway infrastructure endpoints
    
    Applies security to:
    - MCP tool endpoints (/mcp/*)
    - Custom API endpoints
    """
    
    def __init__(self, app, config: Optional[dict] = None):
        super().__init__(app)
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Paths that SKIP security (for Railway compatibility)
        self.skip_security_paths = [
            "/health",
            "/status", 
            "/favicon.ico",
            "/_railway",
            "/metrics"
        ]
        
        # Paths that GET security (MCP endpoints)
        self.secure_paths = [
            "/mcp/"
        ]
        
        # Security features (can be enabled/disabled)
        self.enable_rate_limiting = self.config.get('rate_limiting', False)
        self.enable_security_headers = self.config.get('security_headers', True)
        self.enable_request_logging = self.config.get('request_logging', True)
        
        # Rate limiting storage (simple in-memory)
        self.request_counts = {}
        self.blocked_ips = set()
        
        self.logger.info(f"Selective security middleware initialized")
        self.logger.info(f"Skip security: {self.skip_security_paths}")
        self.logger.info(f"Apply security: {self.secure_paths}")
    
    async def dispatch(self, request: Request, call_next: Callable[[Request], Awaitable[Response]]) -> Response:
        """Apply selective security based on request path."""
        start_time = time.time()
        path = request.url.path
        
        # Check if this path should skip security
        if self._should_skip_security(path):
            # Fast path for health checks and infrastructure
            return await call_next(request)
        
        # Apply security for protected paths
        if self._should_apply_security(path):
            # Get client IP
            client_ip = self._get_client_ip(request)
            
            # 1. Rate limiting (if enabled)
            if self.enable_rate_limiting:
                if not self._check_rate_limit(client_ip):
                    self.logger.warning(f"Rate limit exceeded for {client_ip}")
                    return JSONResponse(
                        status_code=429,
                        content={"error": "Rate limit exceeded"}
                    )
            
            # 2. Request validation
            if not self._validate_request(request):
                self.logger.warning(f"Invalid request from {client_ip}: {path}")
                return JSONResponse(
                    status_code=400,
                    content={"error": "Invalid request"}
                )
            
            # 3. Process request
            response = await call_next(request)
            
            # 4. Add security headers (if enabled)
            if self.enable_security_headers:
                self._add_security_headers(response, path)
            
            # 5. Log request (if enabled)
            if self.enable_request_logging:
                processing_time = time.time() - start_time
                self._log_secure_request(request, response, client_ip, processing_time)
            
            return response
        
        # Default: no security applied
        return await call_next(request)
    
    def _should_skip_security(self, path: str) -> bool:
        """Check if path should skip all security measures."""
        return any(path.startswith(skip_path) for skip_path in self.skip_security_paths)
    
    def _should_apply_security(self, path: str) -> bool:
        """Check if path should have security applied."""
        return any(path.startswith(secure_path) for secure_path in self.secure_paths)
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address with proxy support."""
        # Check Railway headers
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            return real_ip
        
        # Fallback
        if hasattr(request, 'client') and request.client:
            return request.client.host
        
        return 'unknown'
    
    def _check_rate_limit(self, client_ip: str) -> bool:
        """Simple rate limiting check."""
        if client_ip in self.blocked_ips:
            return False
        
        now = time.time()
        minute_window = 60  # 60 seconds
        max_requests = 60   # 60 requests per minute
        
        # Clean old entries
        cutoff = now - minute_window
        if client_ip in self.request_counts:
            self.request_counts[client_ip] = [
                req_time for req_time in self.request_counts[client_ip] 
                if req_time > cutoff
            ]
        else:
            self.request_counts[client_ip] = []
        
        # Check limit
        if len(self.request_counts[client_ip]) >= max_requests:
            self.blocked_ips.add(client_ip)
            # Auto-unblock after 5 minutes
            return False
        
        # Record this request
        self.request_counts[client_ip].append(now)
        return True
    
    def _validate_request(self, request: Request) -> bool:
        """Validate request parameters."""
        # Check request size
        content_length = request.headers.get('content-length')
        if content_length:
            try:
                size = int(content_length)
                if size > 10 * 1024 * 1024:  # 10MB limit
                    return False
            except ValueError:
                return False
        
        # Check URL length
        if len(str(request.url)) > 2048:
            return False
        
        return True
    
    def _add_security_headers(self, response: Response, path: str) -> None:
        """Add security headers to response."""
        # Basic security headers for MCP endpoints
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # CSP for MCP endpoints
        if path.startswith("/mcp/"):
            response.headers["Content-Security-Policy"] = (
                "default-src 'self'; "
                "script-src 'none'; "
                "object-src 'none'; "
                "frame-ancestors 'none';"
            )
        
        # Server identification
        response.headers["Server"] = "MCP-Server/1.0"
    
    def _log_secure_request(self, request: Request, response: Response, 
                          client_ip: str, processing_time: float) -> None:
        """Log security-relevant requests."""
        if not self.enable_request_logging:
            return
        
        log_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'client_ip': client_ip,
            'method': request.method,
            'path': request.url.path,
            'status_code': response.status_code,
            'processing_time': round(processing_time, 3),
            'user_agent': request.headers.get('User-Agent', 'unknown')[:100]  # Truncate
        }
        
        # Log as structured data
        if response.status_code >= 400:
            self.logger.warning(f"Security event: {log_data}")
        else:
            self.logger.debug(f"Secure request: {log_data}")


def create_selective_middleware(app, environment: str = 'production') -> SelectiveSecurityMiddleware:
    """
    Create selective security middleware with environment-specific config.
    
    Args:
        app: FastAPI/Starlette application
        environment: Deployment environment
        
    Returns:
        Configured selective security middleware
    """
    config = {
        'rate_limiting': environment == 'production',
        'security_headers': True,
        'request_logging': environment == 'production'
    }
    
    # Override with environment variables
    rate_limiting_env = os.getenv('SECURITY_RATE_LIMITING')
    if rate_limiting_env:
        config['rate_limiting'] = rate_limiting_env.lower() == 'true'
    
    security_headers_env = os.getenv('SECURITY_HEADERS')
    if security_headers_env:
        config['security_headers'] = security_headers_env.lower() == 'true'
    
    security_logging_env = os.getenv('SECURITY_LOGGING')
    if security_logging_env:
        config['request_logging'] = security_logging_env.lower() == 'true'
    
    return SelectiveSecurityMiddleware(app, config) 
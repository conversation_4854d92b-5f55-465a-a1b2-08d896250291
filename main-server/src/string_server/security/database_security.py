"""
Database security enhancements for MCP server.

Provides encryption, secure connections, backup strategies,
and security monitoring for database operations.
"""

import os
import sqlite3
import hashlib
import secrets
import logging
import threading
import time
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from contextlib import contextmanager
from dataclasses import dataclass


@dataclass
class DatabaseSecurityConfig:
    """Database security configuration."""
    
    # Encryption settings
    enable_encryption: bool = True
    encryption_key_env: str = "DB_ENCRYPTION_KEY"
    key_rotation_days: int = 90
    
    # Connection security
    connection_timeout: int = 30
    max_connections: int = 10
    idle_timeout: int = 300  # 5 minutes
    
    # File permissions
    db_file_mode: int = 0o600  # Read/write for owner only
    backup_file_mode: int = 0o600
    
    # Backup settings
    enable_backups: bool = True
    backup_interval_hours: int = 6
    backup_retention_days: int = 30
    backup_directory: str = "/data/backups"
    
    # Security monitoring
    log_all_queries: bool = False
    log_sensitive_operations: bool = True
    monitor_failed_connections: bool = True
    
    # Integrity checks
    enable_integrity_checks: bool = True
    integrity_check_interval_hours: int = 24


class DatabaseEncryption:
    """Database encryption utilities using SQLCipher-like approach."""
    
    def __init__(self, config: DatabaseSecurityConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self._encryption_key: Optional[bytes] = None
        
    def get_encryption_key(self) -> Optional[bytes]:
        """Get or generate encryption key."""
        if not self.config.enable_encryption:
            return None
            
        if self._encryption_key:
            return self._encryption_key
            
        # Try to get key from environment
        key_env = os.getenv(self.config.encryption_key_env)
        if key_env:
            try:
                # Decode hex key
                self._encryption_key = bytes.fromhex(key_env)
                self.logger.info("Loaded encryption key from environment")
                return self._encryption_key
            except ValueError:
                self.logger.error("Invalid encryption key format in environment")
        
        # Generate new key
        self._encryption_key = self._generate_key()
        self.logger.warning("Generated new encryption key - store securely!")
        self.logger.warning(f"Set {self.config.encryption_key_env}={self._encryption_key.hex()}")
        
        return self._encryption_key
    
    def _generate_key(self) -> bytes:
        """Generate a new encryption key."""
        return secrets.token_bytes(32)  # 256-bit key
    
    def encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data (placeholder - use proper encryption library)."""
        if not self.config.enable_encryption:
            return data
            
        # TODO: Implement proper encryption with cryptography library
        # This is a placeholder implementation
        key = self.get_encryption_key()
        if not key:
            return data
            
        # Simple XOR encryption (NOT secure - use proper encryption in production)
        encrypted = bytearray()
        data_bytes = data.encode('utf-8')
        for i, byte in enumerate(data_bytes):
            encrypted.append(byte ^ key[i % len(key)])
        
        return encrypted.hex()
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data (placeholder)."""
        if not self.config.enable_encryption:
            return encrypted_data
            
        # TODO: Implement proper decryption
        key = self.get_encryption_key()
        if not key:
            return encrypted_data
            
        try:
            encrypted_bytes = bytes.fromhex(encrypted_data)
            decrypted = bytearray()
            for i, byte in enumerate(encrypted_bytes):
                decrypted.append(byte ^ key[i % len(key)])
            
            return decrypted.decode('utf-8')
        except Exception as e:
            self.logger.error(f"Decryption failed: {e}")
            return encrypted_data


class SecureConnectionPool:
    """Secure database connection pool with monitoring."""
    
    def __init__(self, db_path: str, config: DatabaseSecurityConfig):
        self.db_path = db_path
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        self._connections: List[Tuple[sqlite3.Connection, datetime]] = []
        self._lock = threading.Lock()
        self._connection_count = 0
        self._failed_connections = 0
        
        # Ensure database directory exists with proper permissions
        self._setup_database_security()
    
    def _setup_database_security(self) -> None:
        """Setup database file security."""
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
        
        # Set directory permissions (readable/writable by owner only)
        try:
            os.chmod(db_dir, 0o700)
        except OSError as e:
            self.logger.warning(f"Could not set directory permissions: {e}")
        
        # Set database file permissions if it exists
        if Path(self.db_path).exists():
            try:
                os.chmod(self.db_path, self.config.db_file_mode)
            except OSError as e:
                self.logger.warning(f"Could not set database file permissions: {e}")
    
    @contextmanager
    def get_connection(self):
        """Get a secure database connection."""
        connection = None
        try:
            connection = self._acquire_connection()
            yield connection
        except Exception as e:
            self._failed_connections += 1
            if self.config.monitor_failed_connections:
                self.logger.error(f"Database connection failed: {e}")
            raise
        finally:
            if connection:
                self._release_connection(connection)
    
    def _acquire_connection(self) -> sqlite3.Connection:
        """Acquire a connection from the pool."""
        with self._lock:
            # Check for available connection
            now = datetime.utcnow()
            for i, (conn, last_used) in enumerate(self._connections):
                if (now - last_used).total_seconds() < self.config.idle_timeout:
                    # Update last used time
                    self._connections[i] = (conn, now)
                    return conn
                else:
                    # Close idle connection
                    try:
                        conn.close()
                    except Exception:
                        pass
                    self._connections.pop(i)
                    break
            
            # Create new connection if under limit
            if self._connection_count < self.config.max_connections:
                conn = self._create_secure_connection()
                self._connection_count += 1
                self._connections.append((conn, now))
                return conn
            
            # Pool exhausted
            raise Exception("Database connection pool exhausted")
    
    def _release_connection(self, connection: sqlite3.Connection) -> None:
        """Release a connection back to the pool."""
        with self._lock:
            # Update last used time
            now = datetime.utcnow()
            for i, (conn, _) in enumerate(self._connections):
                if conn is connection:
                    self._connections[i] = (conn, now)
                    break
    
    def _create_secure_connection(self) -> sqlite3.Connection:
        """Create a secure database connection."""
        try:
            # Create connection with timeout
            conn = sqlite3.connect(
                self.db_path,
                timeout=self.config.connection_timeout,
                check_same_thread=False
            )
            
            # Enable foreign key constraints
            conn.execute("PRAGMA foreign_keys = ON")
            
            # Set secure pragmas
            conn.execute("PRAGMA journal_mode = WAL")  # Write-Ahead Logging
            conn.execute("PRAGMA synchronous = FULL")  # Full synchronous mode
            conn.execute("PRAGMA temp_store = MEMORY")  # Store temp data in memory
            conn.execute("PRAGMA secure_delete = ON")  # Secure deletion
            
            # Set database file permissions
            try:
                os.chmod(self.db_path, self.config.db_file_mode)
            except OSError:
                pass
            
            self.logger.debug("Created secure database connection")
            return conn
            
        except Exception as e:
            self.logger.error(f"Failed to create database connection: {e}")
            raise
    
    def close_all(self) -> None:
        """Close all connections in the pool."""
        with self._lock:
            for conn, _ in self._connections:
                try:
                    conn.close()
                except Exception:
                    pass
            self._connections.clear()
            self._connection_count = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics."""
        with self._lock:
            return {
                'active_connections': len(self._connections),
                'max_connections': self.config.max_connections,
                'failed_connections': self._failed_connections,
                'connection_count': self._connection_count
            }


class DatabaseBackupManager:
    """Automated database backup with encryption and rotation."""
    
    def __init__(self, db_path: str, config: DatabaseSecurityConfig):
        self.db_path = db_path
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.encryption = DatabaseEncryption(config)
        
        if config.enable_backups:
            self._setup_backup_directory()
            self._start_backup_scheduler()
    
    def _setup_backup_directory(self) -> None:
        """Setup backup directory with proper permissions."""
        backup_dir = Path(self.config.backup_directory)
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            os.chmod(backup_dir, 0o700)  # Owner read/write/execute only
        except OSError as e:
            self.logger.warning(f"Could not set backup directory permissions: {e}")
    
    def _start_backup_scheduler(self) -> None:
        """Start background backup scheduler."""
        def backup_scheduler():
            while True:
                try:
                    time.sleep(self.config.backup_interval_hours * 3600)
                    self.create_backup()
                    self.cleanup_old_backups()
                except Exception as e:
                    self.logger.error(f"Backup scheduler error: {e}")
        
        backup_thread = threading.Thread(target=backup_scheduler, daemon=True)
        backup_thread.start()
        self.logger.info("Started backup scheduler")
    
    def create_backup(self) -> Optional[str]:
        """Create a database backup."""
        if not self.config.enable_backups:
            return None
        
        try:
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"api_keys_backup_{timestamp}.db"
            backup_path = Path(self.config.backup_directory) / backup_filename
            
            # Create backup using SQLite backup API
            source_conn = sqlite3.connect(self.db_path)
            backup_conn = sqlite3.connect(str(backup_path))
            
            source_conn.backup(backup_conn)
            
            source_conn.close()
            backup_conn.close()
            
            # Set backup file permissions
            try:
                os.chmod(backup_path, self.config.backup_file_mode)
            except OSError:
                pass
            
            self.logger.info(f"Created database backup: {backup_path}")
            return str(backup_path)
            
        except Exception as e:
            self.logger.error(f"Failed to create backup: {e}")
            return None
    
    def cleanup_old_backups(self) -> int:
        """Clean up old backup files."""
        if not self.config.enable_backups:
            return 0
        
        try:
            backup_dir = Path(self.config.backup_directory)
            cutoff_date = datetime.utcnow() - timedelta(days=self.config.backup_retention_days)
            
            cleaned_count = 0
            for backup_file in backup_dir.glob("api_keys_backup_*.db"):
                try:
                    file_time = datetime.fromtimestamp(backup_file.stat().st_mtime)
                    if file_time < cutoff_date:
                        backup_file.unlink()
                        cleaned_count += 1
                        self.logger.info(f"Removed old backup: {backup_file}")
                except Exception as e:
                    self.logger.error(f"Error removing backup {backup_file}: {e}")
            
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup old backups: {e}")
            return 0
    
    def restore_backup(self, backup_path: str) -> bool:
        """Restore database from backup."""
        try:
            if not Path(backup_path).exists():
                self.logger.error(f"Backup file not found: {backup_path}")
                return False
            
            # Create backup of current database
            current_backup = self.create_backup()
            if current_backup:
                self.logger.info(f"Created safety backup: {current_backup}")
            
            # Restore from backup
            backup_conn = sqlite3.connect(backup_path)
            restore_conn = sqlite3.connect(self.db_path)
            
            backup_conn.backup(restore_conn)
            
            backup_conn.close()
            restore_conn.close()
            
            self.logger.info(f"Restored database from backup: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to restore backup: {e}")
            return False


class DatabaseSecurityManager:
    """Comprehensive database security management."""
    
    def __init__(self, db_path: str, config: Optional[DatabaseSecurityConfig] = None):
        self.db_path = db_path
        self.config = config or DatabaseSecurityConfig()
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.encryption = DatabaseEncryption(self.config)
        self.connection_pool = SecureConnectionPool(db_path, self.config)
        self.backup_manager = DatabaseBackupManager(db_path, self.config)
        
        # Load configuration from environment
        self._load_env_config()
        
        # Start integrity checker
        if self.config.enable_integrity_checks:
            self._start_integrity_checker()
    
    def _load_env_config(self) -> None:
        """Load configuration from environment variables."""
        self.config.enable_encryption = os.getenv('DB_ENABLE_ENCRYPTION', 'true').lower() == 'true'
        self.config.connection_timeout = int(os.getenv('DB_CONNECTION_TIMEOUT', '30'))
        self.config.max_connections = int(os.getenv('DB_MAX_CONNECTIONS', '10'))
        self.config.enable_backups = os.getenv('DB_ENABLE_BACKUPS', 'true').lower() == 'true'
        self.config.backup_interval_hours = int(os.getenv('DB_BACKUP_INTERVAL_HOURS', '6'))
        self.config.backup_retention_days = int(os.getenv('DB_BACKUP_RETENTION_DAYS', '30'))
        
        backup_dir = os.getenv('DB_BACKUP_DIRECTORY')
        if backup_dir:
            self.config.backup_directory = backup_dir
    
    def _start_integrity_checker(self) -> None:
        """Start background integrity checker."""
        def integrity_checker():
            while True:
                try:
                    time.sleep(self.config.integrity_check_interval_hours * 3600)
                    self.check_integrity()
                except Exception as e:
                    self.logger.error(f"Integrity checker error: {e}")
        
        integrity_thread = threading.Thread(target=integrity_checker, daemon=True)
        integrity_thread.start()
        self.logger.info("Started integrity checker")
    
    def check_integrity(self) -> bool:
        """Check database integrity."""
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("PRAGMA integrity_check")
                result = cursor.fetchone()
                
                if result and result[0] == 'ok':
                    self.logger.info("Database integrity check passed")
                    return True
                else:
                    self.logger.error(f"Database integrity check failed: {result}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Integrity check error: {e}")
            return False
    
    def get_security_status(self) -> Dict[str, Any]:
        """Get comprehensive security status."""
        return {
            'encryption_enabled': self.config.enable_encryption,
            'backups_enabled': self.config.enable_backups,
            'connection_pool': self.connection_pool.get_stats(),
            'integrity_checks_enabled': self.config.enable_integrity_checks,
            'database_exists': Path(self.db_path).exists(),
            'database_size': Path(self.db_path).stat().st_size if Path(self.db_path).exists() else 0,
            'last_integrity_check': datetime.utcnow().isoformat(),
            'security_level': self._assess_security_level()
        }
    
    def _assess_security_level(self) -> str:
        """Assess overall database security level."""
        score = 0
        
        if self.config.enable_encryption:
            score += 3
        if self.config.enable_backups:
            score += 2
        if self.config.enable_integrity_checks:
            score += 2
        if self.config.db_file_mode == 0o600:
            score += 1
        if self.config.connection_timeout <= 30:
            score += 1
        if self.config.max_connections <= 10:
            score += 1
        
        if score >= 8:
            return "HIGH"
        elif score >= 5:
            return "MEDIUM"
        else:
            return "LOW"
    
    def close(self) -> None:
        """Close all database connections and cleanup."""
        self.connection_pool.close_all()
        self.logger.info("Database security manager closed")


def create_secure_database_manager(db_path: str, 
                                 environment: str = 'production') -> DatabaseSecurityManager:
    """
    Create a secure database manager with environment-specific configuration.
    
    Args:
        db_path: Path to database file
        environment: Deployment environment
        
    Returns:
        Configured database security manager
    """
    config = DatabaseSecurityConfig()
    
    # Environment-specific settings
    if environment == 'production':
        config.enable_encryption = True
        config.enable_backups = True
        config.enable_integrity_checks = True
        config.log_sensitive_operations = True
    elif environment == 'development':
        config.enable_encryption = False
        config.enable_backups = False
        config.log_all_queries = True
    
    return DatabaseSecurityManager(db_path, config)
"""
Database Package for Thread-Safe SQLite Operations

Provides comprehensive database management for Railway container deployment
with focus on concurrent access safety, deadlock prevention, and performance.

Key Components:
- ThreadSafeDatabaseManager: Main database interface
- ConnectionPool: Thread-safe connection pooling
- DatabaseConfig: Configuration management
- Railway-optimized SQLite settings

Usage:
    from src.database import get_database_manager, DatabaseConfig
    
    # Get a thread-safe database manager
    db_manager = get_database_manager("my_database.db")
    
    # Execute queries safely
    results = db_manager.execute_query("SELECT * FROM users WHERE id = ?", (user_id,))
    
    # Execute updates atomically
    rows_affected = db_manager.execute_update("UPDATE users SET last_used = ? WHERE id = ?", (now, user_id))
"""

from .thread_safe_manager import (
    ThreadSafeDatabaseManager,
    ConnectionPool,
    DatabaseConfig,
    get_database_manager,
    close_all_database_managers
)

# Railway-optimized default configuration
RAILWAY_CONFIG = DatabaseConfig(
    max_connections=8,  # Conservative for Railway container
    connection_timeout=30.0,
    busy_timeout=30.0,
    enable_wal_mode=True,
    enable_railway_optimizations=True,
    sync_mode="NORMAL",  # Faster than FULL, safe for Railway
    cache_size=-32000,   # 32MB cache for Railway
    temp_store="MEMORY",
    max_retries=3,
    retry_delay_base=0.1,
    enable_performance_monitoring=True,
    log_slow_queries=True,
    slow_query_threshold=1.0
)

# Development configuration
DEVELOPMENT_CONFIG = DatabaseConfig(
    max_connections=5,
    connection_timeout=10.0,
    busy_timeout=10.0,
    enable_wal_mode=True,
    enable_railway_optimizations=False,
    sync_mode="FULL",
    cache_size=-16000,   # 16MB cache
    temp_store="MEMORY",
    max_retries=2,
    retry_delay_base=0.05,
    enable_performance_monitoring=True,
    log_slow_queries=True,
    slow_query_threshold=0.5
)


def get_railway_database_manager(db_path: str) -> ThreadSafeDatabaseManager:
    """
    Get a database manager optimized for Railway deployment.
    
    Args:
        db_path: Path to the SQLite database
        
    Returns:
        Thread-safe database manager with Railway optimizations
    """
    return get_database_manager(db_path, RAILWAY_CONFIG)


def get_development_database_manager(db_path: str) -> ThreadSafeDatabaseManager:
    """
    Get a database manager optimized for development.
    
    Args:
        db_path: Path to the SQLite database
        
    Returns:
        Thread-safe database manager with development settings
    """
    return get_database_manager(db_path, DEVELOPMENT_CONFIG)


# Import integration components
from .integration import (
    DatabaseIntegration,
    get_database_integration,
    initialize_database_integration,
    patch_existing_components,
    verify_database_safety,
    health_check,
    get_auth_database,
    get_jobs_database,
    get_cache_database
)

from .railway_config import (
    RailwayDatabaseConfig,
    setup_railway_database_environment,
    RAILWAY_AUTH_DB,
    RAILWAY_JOBS_DB,
    RAILWAY_CACHE_DB
)

__all__ = [
    # Core classes
    'ThreadSafeDatabaseManager',
    'ConnectionPool',
    'DatabaseConfig',

    # Factory functions
    'get_database_manager',
    'get_railway_database_manager',
    'get_development_database_manager',

    # Configuration presets
    'RAILWAY_CONFIG',
    'DEVELOPMENT_CONFIG',

    # Integration
    'DatabaseIntegration',
    'get_database_integration',
    'initialize_database_integration',
    'patch_existing_components',
    'verify_database_safety',
    'health_check',
    'get_auth_database',
    'get_jobs_database',
    'get_cache_database',

    # Railway configuration
    'RailwayDatabaseConfig',
    'setup_railway_database_environment',
    'RAILWAY_AUTH_DB',
    'RAILWAY_JOBS_DB',
    'RAILWAY_CACHE_DB',

    # Cleanup
    'close_all_database_managers'
]

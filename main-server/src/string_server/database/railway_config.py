"""
Railway Container Database Configuration

Optimized SQLite configuration for Railway's containerized environment
with focus on concurrent access, persistence, and performance.

Key Features:
- Railway volume mounting optimization
- Container restart resilience
- Concurrent access safety
- Performance tuning for Railway infrastructure
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any

from .thread_safe_manager import DatabaseConfig

logger = logging.getLogger(__name__)


class RailwayDatabaseConfig:
    """Railway-specific database configuration and optimization."""
    
    # Railway volume paths
    RAILWAY_DATA_PATH = "/data"
    RAILWAY_TMP_PATH = "/tmp"
    
    # Database file locations
    AUTH_DB_PATH = "/data/auth.db"
    JOBS_DB_PATH = "/data/jobs.db"
    CACHE_DB_PATH = "/tmp/cache.db"  # Temporary cache, not persisted
    
    @classmethod
    def get_railway_config(cls) -> DatabaseConfig:
        """Get optimized database configuration for Railway deployment."""
        return DatabaseConfig(
            # Connection settings optimized for Railway
            max_connections=8,  # Conservative for Railway container limits
            connection_timeout=30.0,  # Longer timeout for Railway network
            busy_timeout=30.0,  # Handle Railway I/O delays
            
            # WAL mode for better concurrency
            enable_wal_mode=True,
            wal_checkpoint_interval=1000,
            
            # Deadlock prevention
            max_retries=5,  # More retries for Railway environment
            retry_delay_base=0.1,
            lock_timeout=15.0,
            
            # Railway container optimizations
            enable_railway_optimizations=True,
            sync_mode="NORMAL",  # Balance between safety and performance
            cache_size=-32000,   # 32MB cache (reasonable for Railway)
            temp_store="MEMORY",
            
            # Monitoring
            enable_performance_monitoring=True,
            log_slow_queries=True,
            slow_query_threshold=2.0  # Higher threshold for Railway
        )
    
    @classmethod
    def setup_railway_directories(cls):
        """Ensure Railway data directories exist with proper permissions."""
        try:
            # Create data directory if it doesn't exist
            data_path = Path(cls.RAILWAY_DATA_PATH)
            data_path.mkdir(exist_ok=True, mode=0o755)
            
            # Create tmp directory if it doesn't exist
            tmp_path = Path(cls.RAILWAY_TMP_PATH)
            tmp_path.mkdir(exist_ok=True, mode=0o755)
            
            logger.info(f"Railway directories initialized: {data_path}, {tmp_path}")
            
        except Exception as e:
            logger.error(f"Failed to setup Railway directories: {e}")
            raise
    
    @classmethod
    def get_database_path(cls, db_name: str, persistent: bool = True) -> str:
        """
        Get the appropriate database path for Railway deployment.
        
        Args:
            db_name: Name of the database file
            persistent: Whether the database should persist across restarts
            
        Returns:
            Full path to the database file
        """
        if persistent:
            return os.path.join(cls.RAILWAY_DATA_PATH, db_name)
        else:
            return os.path.join(cls.RAILWAY_TMP_PATH, db_name)
    
    @classmethod
    def validate_railway_environment(cls) -> Dict[str, Any]:
        """
        Validate Railway environment for database operations.
        
        Returns:
            Dictionary with validation results
        """
        validation = {
            "railway_detected": False,
            "data_directory_writable": False,
            "tmp_directory_writable": False,
            "volume_mounted": False,
            "disk_space_available": False,
            "recommendations": []
        }
        
        try:
            # Check if running in Railway
            railway_env = os.getenv('RAILWAY_ENVIRONMENT')
            if railway_env:
                validation["railway_detected"] = True
                logger.info(f"Railway environment detected: {railway_env}")
            
            # Check data directory
            data_path = Path(cls.RAILWAY_DATA_PATH)
            if data_path.exists() and os.access(data_path, os.W_OK):
                validation["data_directory_writable"] = True
                validation["volume_mounted"] = True
            else:
                validation["recommendations"].append(
                    "Mount a persistent volume to /data for database persistence"
                )
            
            # Check tmp directory
            tmp_path = Path(cls.RAILWAY_TMP_PATH)
            if tmp_path.exists() and os.access(tmp_path, os.W_OK):
                validation["tmp_directory_writable"] = True
            
            # Check disk space (at least 100MB free)
            try:
                import shutil
                free_space = shutil.disk_usage(cls.RAILWAY_DATA_PATH).free
                if free_space > 100 * 1024 * 1024:  # 100MB
                    validation["disk_space_available"] = True
                else:
                    validation["recommendations"].append(
                        f"Low disk space: {free_space / 1024 / 1024:.1f}MB available"
                    )
            except Exception:
                validation["recommendations"].append("Could not check disk space")
            
            # Railway-specific recommendations
            if not validation["railway_detected"]:
                validation["recommendations"].append(
                    "Not running in Railway environment - some optimizations may not apply"
                )
            
            if not validation["volume_mounted"]:
                validation["recommendations"].append(
                    "Consider mounting a Railway volume for database persistence"
                )
            
        except Exception as e:
            logger.error(f"Error validating Railway environment: {e}")
            validation["error"] = str(e)
        
        return validation
    
    @classmethod
    def apply_railway_optimizations(cls):
        """Apply Railway-specific system optimizations."""
        try:
            # Set environment variables for SQLite optimization
            os.environ.setdefault('SQLITE_TMPDIR', cls.RAILWAY_TMP_PATH)
            
            # Railway-specific SQLite settings
            railway_settings = {
                'SQLITE_ENABLE_FTS5': '1',
                'SQLITE_ENABLE_JSON1': '1',
                'SQLITE_ENABLE_RTREE': '1',
                'SQLITE_MAX_MMAP_SIZE': '268435456',  # 256MB mmap
            }
            
            for key, value in railway_settings.items():
                os.environ.setdefault(key, value)
            
            logger.info("Railway SQLite optimizations applied")
            
        except Exception as e:
            logger.error(f"Failed to apply Railway optimizations: {e}")
    
    @classmethod
    def get_connection_string_for_service(cls, service_name: str) -> str:
        """
        Get optimized connection string for specific services.
        
        Args:
            service_name: Name of the service (auth, jobs, cache, etc.)
            
        Returns:
            Database file path for the service
        """
        service_paths = {
            'auth': cls.AUTH_DB_PATH,
            'jobs': cls.JOBS_DB_PATH,
            'cache': cls.CACHE_DB_PATH,
            'simple_auth': cls.AUTH_DB_PATH,  # Alias
            'job_completion': cls.JOBS_DB_PATH,  # Alias
        }
        
        return service_paths.get(service_name, cls.get_database_path(f"{service_name}.db"))
    
    @classmethod
    def initialize_railway_databases(cls):
        """Initialize all Railway databases with proper configuration."""
        try:
            # Setup directories
            cls.setup_railway_directories()
            
            # Apply optimizations
            cls.apply_railway_optimizations()
            
            # Validate environment
            validation = cls.validate_railway_environment()
            
            if validation["recommendations"]:
                logger.warning("Railway environment recommendations:")
                for rec in validation["recommendations"]:
                    logger.warning(f"  - {rec}")
            
            # Initialize database managers for each service
            from . import get_database_manager
            
            config = cls.get_railway_config()
            
            # Initialize auth database
            auth_manager = get_database_manager(cls.AUTH_DB_PATH, config)
            logger.info(f"Auth database initialized: {cls.AUTH_DB_PATH}")
            
            # Initialize jobs database
            jobs_manager = get_database_manager(cls.JOBS_DB_PATH, config)
            logger.info(f"Jobs database initialized: {cls.JOBS_DB_PATH}")
            
            # Initialize cache database (temporary)
            cache_config = DatabaseConfig(
                max_connections=4,
                enable_wal_mode=True,
                sync_mode="OFF",  # Faster for cache
                temp_store="MEMORY"
            )
            cache_manager = get_database_manager(cls.CACHE_DB_PATH, cache_config)
            logger.info(f"Cache database initialized: {cls.CACHE_DB_PATH}")
            
            logger.info("All Railway databases initialized successfully")
            
            return {
                'auth': auth_manager,
                'jobs': jobs_manager,
                'cache': cache_manager,
                'validation': validation
            }
            
        except Exception as e:
            logger.error(f"Failed to initialize Railway databases: {e}")
            raise


def setup_railway_database_environment():
    """
    Setup complete Railway database environment.
    
    Call this function during application startup to ensure proper
    database configuration for Railway deployment.
    """
    try:
        logger.info("Setting up Railway database environment...")
        
        # Initialize Railway database configuration
        result = RailwayDatabaseConfig.initialize_railway_databases()
        
        # Log validation results
        validation = result['validation']
        logger.info(f"Railway environment validation:")
        logger.info(f"  - Railway detected: {validation['railway_detected']}")
        logger.info(f"  - Data directory writable: {validation['data_directory_writable']}")
        logger.info(f"  - Volume mounted: {validation['volume_mounted']}")
        logger.info(f"  - Disk space available: {validation['disk_space_available']}")
        
        if validation['recommendations']:
            logger.warning("Recommendations for optimal Railway deployment:")
            for rec in validation['recommendations']:
                logger.warning(f"  - {rec}")
        
        logger.info("Railway database environment setup complete")
        return result
        
    except Exception as e:
        logger.error(f"Failed to setup Railway database environment: {e}")
        raise


# Export Railway configuration for easy access
RAILWAY_CONFIG = RailwayDatabaseConfig.get_railway_config()
RAILWAY_AUTH_DB = RailwayDatabaseConfig.AUTH_DB_PATH
RAILWAY_JOBS_DB = RailwayDatabaseConfig.JOBS_DB_PATH
RAILWAY_CACHE_DB = RailwayDatabaseConfig.CACHE_DB_PATH

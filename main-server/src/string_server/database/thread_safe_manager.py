"""
Thread-Safe SQLite Database Manager for Railway Container

Provides robust, concurrent-safe SQLite operations optimized for Railway's
containerized environment with proper deadlock prevention and WAL mode.

Key Features:
- Thread-safe connection pooling
- WAL (Write-Ahead Logging) mode for better concurrency
- Deadlock prevention with consistent lock ordering
- Atomic operations with proper transaction management
- Railway container optimization
- Automatic retry mechanisms for transient failures
"""

import sqlite3
import threading
import time
import logging
import os
import contextlib
from typing import Optional, Dict, Any, List, Callable, TypeVar, Generic
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
import uuid
import weakref

logger = logging.getLogger(__name__)

T = TypeVar('T')


@dataclass
class DatabaseConfig:
    """Configuration for thread-safe database operations."""
    
    # Connection settings
    max_connections: int = 10
    connection_timeout: float = 30.0
    busy_timeout: float = 30.0
    
    # WAL mode settings
    enable_wal_mode: bool = True
    wal_checkpoint_interval: int = 1000  # pages
    
    # Deadlock prevention
    max_retries: int = 3
    retry_delay_base: float = 0.1  # exponential backoff base
    lock_timeout: float = 10.0
    
    # Railway optimization
    enable_railway_optimizations: bool = True
    sync_mode: str = "NORMAL"  # NORMAL for Railway (faster than FULL)
    cache_size: int = -64000  # 64MB cache
    temp_store: str = "MEMORY"
    
    # Monitoring
    enable_performance_monitoring: bool = True
    log_slow_queries: bool = True
    slow_query_threshold: float = 1.0  # seconds


class ConnectionPool:
    """Thread-safe SQLite connection pool with WAL mode optimization."""
    
    def __init__(self, db_path: str, config: DatabaseConfig):
        self.db_path = db_path
        self.config = config
        self._pool: List[sqlite3.Connection] = []
        self._pool_lock = threading.RLock()
        self._active_connections: Dict[int, sqlite3.Connection] = {}
        self._connection_count = 0
        self._stats = {
            'total_connections': 0,
            'active_connections': 0,
            'pool_hits': 0,
            'pool_misses': 0,
            'deadlocks_prevented': 0,
            'retries_performed': 0
        }
        
        # Ensure database directory exists
        os.makedirs(os.path.dirname(os.path.abspath(db_path)), exist_ok=True)
        
        # Initialize WAL mode on the database
        self._initialize_wal_mode()
        
        logger.info(f"Connection pool initialized for {db_path} with max_connections={config.max_connections}")
    
    def _initialize_wal_mode(self):
        """Initialize WAL mode and Railway optimizations."""
        try:
            conn = sqlite3.connect(
                self.db_path,
                timeout=self.config.connection_timeout,
                check_same_thread=False
            )
            
            # Enable WAL mode for better concurrency
            if self.config.enable_wal_mode:
                conn.execute("PRAGMA journal_mode = WAL")
                logger.info("WAL mode enabled for better concurrency")
            
            # Railway container optimizations
            if self.config.enable_railway_optimizations:
                conn.execute(f"PRAGMA synchronous = {self.config.sync_mode}")
                conn.execute(f"PRAGMA cache_size = {self.config.cache_size}")
                conn.execute(f"PRAGMA temp_store = {self.config.temp_store}")
                conn.execute("PRAGMA foreign_keys = ON")
                conn.execute(f"PRAGMA busy_timeout = {int(self.config.busy_timeout * 1000)}")
                logger.info("Railway optimizations applied")
            
            conn.close()
            
        except Exception as e:
            logger.error(f"Failed to initialize WAL mode: {e}")
            raise
    
    @contextlib.contextmanager
    def get_connection(self):
        """Get a connection from the pool with automatic cleanup."""
        connection = None
        thread_id = threading.get_ident()
        
        try:
            connection = self._acquire_connection()
            self._active_connections[thread_id] = connection
            yield connection
            
        except Exception as e:
            if connection:
                try:
                    connection.rollback()
                except Exception:
                    pass
            raise
            
        finally:
            if thread_id in self._active_connections:
                del self._active_connections[thread_id]
            if connection:
                self._release_connection(connection)
    
    def _acquire_connection(self) -> sqlite3.Connection:
        """Acquire a connection from the pool or create a new one."""
        with self._pool_lock:
            # Try to get from pool first
            if self._pool:
                conn = self._pool.pop()
                self._stats['pool_hits'] += 1
                return conn
            
            # Create new connection if under limit
            if self._connection_count < self.config.max_connections:
                conn = self._create_connection()
                self._connection_count += 1
                self._stats['total_connections'] += 1
                self._stats['pool_misses'] += 1
                return conn
            
            # Pool exhausted - wait and retry
            raise sqlite3.OperationalError("Connection pool exhausted")
    
    def _release_connection(self, connection: sqlite3.Connection):
        """Return a connection to the pool."""
        try:
            # Ensure connection is in a clean state
            connection.rollback()
            
            with self._pool_lock:
                if len(self._pool) < self.config.max_connections:
                    self._pool.append(connection)
                else:
                    connection.close()
                    self._connection_count -= 1
                    
        except Exception as e:
            logger.warning(f"Error releasing connection: {e}")
            try:
                connection.close()
            except Exception:
                pass
            with self._pool_lock:
                self._connection_count -= 1
    
    def _create_connection(self) -> sqlite3.Connection:
        """Create a new optimized SQLite connection."""
        conn = sqlite3.connect(
            self.db_path,
            timeout=self.config.connection_timeout,
            check_same_thread=False
        )
        
        # Set row factory for easier data access
        conn.row_factory = sqlite3.Row
        
        # Apply optimizations
        conn.execute(f"PRAGMA busy_timeout = {int(self.config.busy_timeout * 1000)}")
        conn.execute("PRAGMA foreign_keys = ON")
        
        if self.config.enable_railway_optimizations:
            conn.execute(f"PRAGMA synchronous = {self.config.sync_mode}")
            conn.execute(f"PRAGMA cache_size = {self.config.cache_size}")
            conn.execute(f"PRAGMA temp_store = {self.config.temp_store}")
        
        return conn
    
    def get_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics."""
        with self._pool_lock:
            return {
                **self._stats,
                'pool_size': len(self._pool),
                'active_connections': len(self._active_connections),
                'max_connections': self.config.max_connections
            }
    
    def close_all(self):
        """Close all connections in the pool."""
        with self._pool_lock:
            for conn in self._pool:
                try:
                    conn.close()
                except Exception:
                    pass
            self._pool.clear()
            
            for conn in self._active_connections.values():
                try:
                    conn.close()
                except Exception:
                    pass
            self._active_connections.clear()
            
            self._connection_count = 0


class ThreadSafeDatabaseManager:
    """
    Thread-safe database manager with deadlock prevention and Railway optimization.
    
    Provides atomic operations, proper transaction management, and concurrent access
    safety for SQLite databases in Railway container environment.
    """
    
    def __init__(self, db_path: str, config: Optional[DatabaseConfig] = None):
        self.db_path = db_path
        self.config = config or DatabaseConfig()
        self.pool = ConnectionPool(db_path, self.config)
        self._operation_lock = threading.RLock()
        self._query_stats = {}
        
        logger.info(f"Thread-safe database manager initialized: {db_path}")
    
    @contextlib.contextmanager
    def transaction(self, immediate: bool = False):
        """
        Context manager for atomic transactions with deadlock prevention.
        
        Args:
            immediate: Use IMMEDIATE transaction for write operations
        """
        with self.pool.get_connection() as conn:
            transaction_type = "IMMEDIATE" if immediate else "DEFERRED"
            
            try:
                conn.execute(f"BEGIN {transaction_type}")
                yield conn
                conn.commit()
                
            except Exception as e:
                try:
                    conn.rollback()
                except Exception:
                    pass
                raise
    
    def execute_with_retry(self, 
                          operation: Callable[[sqlite3.Connection], T], 
                          immediate: bool = False) -> T:
        """
        Execute database operation with automatic retry on deadlock/busy.
        
        Args:
            operation: Function that takes a connection and returns a result
            immediate: Use IMMEDIATE transaction for write operations
            
        Returns:
            Result from the operation
        """
        last_exception = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                with self.transaction(immediate=immediate) as conn:
                    return operation(conn)
                    
            except (sqlite3.OperationalError, sqlite3.DatabaseError) as e:
                last_exception = e
                error_msg = str(e).lower()
                
                # Check if it's a retryable error
                if any(keyword in error_msg for keyword in ['locked', 'busy', 'deadlock']):
                    if attempt < self.config.max_retries:
                        delay = self.config.retry_delay_base * (2 ** attempt)
                        logger.warning(f"Database operation failed (attempt {attempt + 1}), retrying in {delay}s: {e}")
                        time.sleep(delay)
                        self.pool._stats['retries_performed'] += 1
                        continue
                
                # Non-retryable error or max retries exceeded
                raise
        
        # Should never reach here, but just in case
        raise last_exception or Exception("Unknown database error")
    
    def execute_query(self, query: str, params: tuple = ()) -> List[sqlite3.Row]:
        """Execute a SELECT query safely."""
        def operation(conn):
            cursor = conn.execute(query, params)
            return cursor.fetchall()
        
        return self.execute_with_retry(operation, immediate=False)
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """Execute an INSERT/UPDATE/DELETE query safely."""
        def operation(conn):
            cursor = conn.execute(query, params)
            return cursor.rowcount
        
        return self.execute_with_retry(operation, immediate=True)
    
    def execute_script(self, script: str):
        """Execute a SQL script safely."""
        def operation(conn):
            conn.executescript(script)
        
        self.execute_with_retry(operation, immediate=True)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive database statistics."""
        pool_stats = self.pool.get_stats()
        
        return {
            'database_path': self.db_path,
            'config': {
                'max_connections': self.config.max_connections,
                'wal_mode_enabled': self.config.enable_wal_mode,
                'railway_optimizations': self.config.enable_railway_optimizations
            },
            'pool_stats': pool_stats,
            'query_stats': dict(self._query_stats)
        }
    
    def close(self):
        """Close the database manager and all connections."""
        self.pool.close_all()
        logger.info(f"Database manager closed: {self.db_path}")


# Global database managers registry
_database_managers: Dict[str, ThreadSafeDatabaseManager] = {}
_managers_lock = threading.Lock()


def get_database_manager(db_path: str, 
                        config: Optional[DatabaseConfig] = None) -> ThreadSafeDatabaseManager:
    """
    Get or create a thread-safe database manager for the given path.
    
    Args:
        db_path: Path to the SQLite database
        config: Optional database configuration
        
    Returns:
        Thread-safe database manager instance
    """
    with _managers_lock:
        if db_path not in _database_managers:
            _database_managers[db_path] = ThreadSafeDatabaseManager(db_path, config)
        return _database_managers[db_path]


def close_all_database_managers():
    """Close all database managers (for cleanup)."""
    with _managers_lock:
        for manager in _database_managers.values():
            manager.close()
        _database_managers.clear()

"""
Analysis-related tools for the MCP server.
Handles call graph analysis, dead code detection, and impact analysis.
"""

import json
import logging
from typing import Optional, TYPE_CHECKING
from pydantic import Field

from .base import get_metadata_extractor, _get_or_auto_index_project

if TYPE_CHECKING:
    from mcp.server.fastmcp import FastMCP

try:
    from ..processing.dead_code_analyzer import DeadCodeAnalyzer
except ImportError:
    from processing.dead_code_analyzer import DeadCodeAnalyzer


def analyze_impact(symbol_name: str, project_path: Optional[str] = None) -> str:
    """
    Analyze the impact of changing or removing a symbol.
    Will auto-index the project if not already indexed.
    
    Args:
        symbol_name: Name of the symbol to analyze impact for
        project_path: Optional project path to search in
    
    Returns:
        JSON with impact analysis showing what would be affected by changes
    """
    try:
        # Get or auto-index project
        project_metadata, status_msg = _get_or_auto_index_project(project_path)
        if not project_metadata:
            return json.dumps({
                "error": status_msg.replace("Auto-indexing complete!\n", "")
            })
        
        # Get the metadata extractor and create reference tracker
        extractor = get_metadata_extractor()
        try:
            from ..processing.reference_tracker import ReferenceTracker
        except ImportError:
            from processing.reference_tracker import ReferenceTracker
        
        reference_tracker = ReferenceTracker(extractor.indexer)
        
        # Find all references to understand impact
        result = reference_tracker.find_all_references(symbol_name, 
                                                      project_metadata)
        
        if result.total_references == 0:
            return json.dumps({
                "status": "success",
                "symbol_name": symbol_name,
                "impact_level": "none",
                "safe_to_remove": True,
                "message": f"No references found for '{symbol_name}'. "
                          f"It appears to be unused and can be safely removed."
            })
        
        # Calculate impact metrics
        files_affected = list(set(ref.file_path for ref in result.references))
        
        # Categorize reference types
        ref_types = {}
        for ref in result.references:
            ref_type = ref.reference_type
            ref_types[ref_type] = ref_types.get(ref_type, 0) + 1
        
        # Determine risk level
        if result.total_references <= 5:
            risk_level = "low"
            risk_message = "Few references, relatively safe to modify"
        elif result.total_references <= 20:
            risk_level = "medium"
            risk_message = "Moderate number of references, test thoroughly"
        else:
            risk_level = "high"
            risk_message = "Many references, requires careful planning"
        
        # Find test files
        test_files = list(set(ref.file_path for ref in result.references 
                             if 'test' in ref.file_path.lower()))
        
        # Create files summary
        files_summary = {}
        for ref in result.references:
            if ref.file_path not in files_summary:
                files_summary[ref.file_path] = 0
            files_summary[ref.file_path] += 1
        
        impact_result = {
            "status": "success",
            "symbol_name": symbol_name,
            "symbol_type": result.symbol_type,
            "impact_level": risk_level,
            "risk_message": risk_message,
            "safe_to_remove": False,
            "blast_radius": {
                "total_references": result.total_references,
                "files_affected": len(files_affected),
                "test_files_affected": len(test_files)
            },
            "reference_types": ref_types,
            "affected_files": files_summary,
            "test_files": test_files,
            "references": [{
                "file_path": ref.file_path,
                "line_number": ref.line,
                "reference_type": ref.reference_type,
                "context": ref.context_line
            } for ref in result.references]
        }
        
        return json.dumps(impact_result, indent=2)
        
    except Exception as e:
        logging.error(f"Failed to analyze impact for {symbol_name}: {e}")
        return json.dumps({"error": f"Failed to analyze impact: {str(e)}"})


def analyze_call_graph(function_name: Optional[str] = None,
                      project_path: Optional[str] = None,
                      max_depth: int = 3) -> str:
    """
    Analyze the call graph for a function or the entire project.
    Will auto-index the project if not already indexed.
    
    Args:
        function_name: Optional function name to focus analysis on
        project_path: Optional project path
        max_depth: Maximum depth for call chain analysis
    
    Returns:
        JSON with call graph analysis results
    """
    try:
        # Get or auto-index project
        project_metadata, status_msg = _get_or_auto_index_project(project_path)
        if not project_metadata:
            return json.dumps({
                "error": status_msg.replace("Auto-indexing complete!\n", "")
            })
        
        if function_name:
            # Analyze specific function
            function_info = project_metadata.find_function(function_name)
            if not function_info:
                return json.dumps({
                    "error": f"Function '{function_name}' not found"
                })
            
            # Get function context
            extractor = get_metadata_extractor()
            context = extractor.get_function_context(function_name, 
                                                    project_metadata, max_depth)
            related = context.get("related_functions", {})
            
            result = {
                "status": "success",
                "analysis_type": "function",
                "function_name": function_name,
                "max_depth": max_depth,
                "direct_calls": related.get("direct_calls", []),
                "called_by": related.get("direct_callers", []),
                "indirect_relationships": [{
                    "function": rel["function"],
                    "distance": rel["distance"]
                } for rel in related.get("indirect_related", [])[:20]]
            }
            
        else:
            # Analyze entire project
            all_functions = project_metadata.get_all_functions()
            
            # Calculate call statistics
            total_calls = 0
            function_call_counts = {}
            
            for file_metadata in project_metadata.files.values():
                call_graph_data = file_metadata.call_graph_data
                edges = call_graph_data.get("edges", [])
                total_calls += len(edges)
                
                # Count calls per function
                for edge in edges:
                    source = edge.get("source", "")
                    target = edge.get("target", "")
                    
                    if source not in function_call_counts:
                        function_call_counts[source] = {
                            "calls_made": 0, "called_by": 0
                        }
                    if target not in function_call_counts:
                        function_call_counts[target] = {
                            "calls_made": 0, "called_by": 0
                        }
                    
                    function_call_counts[source]["calls_made"] += 1
                    function_call_counts[target]["called_by"] += 1
            
            # Find most connected functions
            most_calling = sorted(function_call_counts.items(), 
                                key=lambda x: x[1]["calls_made"], 
                                reverse=True)[:10]
            most_called = sorted(function_call_counts.items(), 
                               key=lambda x: x[1]["called_by"], 
                               reverse=True)[:10]
            
            avg_calls = round(total_calls / len(all_functions) 
                            if all_functions else 0, 2)
            
            result = {
                "status": "success",
                "analysis_type": "project",
                "total_functions": len(all_functions),
                "total_function_calls": total_calls,
                "average_calls_per_function": avg_calls,
                "most_calling_functions": [
                    {"name": name, "calls_made": data["calls_made"]} 
                    for name, data in most_calling
                ],
                "most_called_functions": [
                    {"name": name, "called_by": data["called_by"]} 
                    for name, data in most_called
                ]
            }
        
        return json.dumps(result, indent=2)
        
    except Exception as e:
        logging.error(f"Failed to analyze call graph: {e}")
        return json.dumps({"error": f"Failed to analyze call graph: {str(e)}"})


def analyze_dead_code(project_path: Optional[str] = None) -> str:
    """
    Analyze the project for potentially unused/dead code.
    Will auto-index the project if not already indexed.
    
    Args:
        project_path: Optional project path to analyze
    
    Returns:
        JSON with report of potentially dead code with confidence levels
    """
    try:
        # Get or auto-index project
        project_metadata, status_msg = _get_or_auto_index_project(project_path)
        if not project_metadata:
            return json.dumps({
                "error": status_msg.replace("Auto-indexing complete!\n", "")
            })
        
        # Create and run dead code analyzer
        extractor = get_metadata_extractor()
        analyzer = DeadCodeAnalyzer(extractor.indexer)
        dead_code_result = analyzer.analyze_dead_code(project_metadata)
        
        # Collect all dead items from all confidence levels
        all_dead_items = (dead_code_result.high_confidence + 
                         dead_code_result.medium_confidence + 
                         dead_code_result.low_confidence)
        
        # Convert to JSON format
        dead_items = []
        for item in all_dead_items:
            dead_items.append({
                "name": item.name,
                "type": item.item_type,
                "file_path": item.file_path,
                "line_number": item.line,
                "confidence": item.confidence.lower(),
                "reason": item.reason,
                "code_snippet": item.context_line
            })
        
        # Group by confidence level
        confidence_summary = {
            "high": len(dead_code_result.high_confidence),
            "medium": len(dead_code_result.medium_confidence),
            "low": len(dead_code_result.low_confidence)
        }
        
        # Group by file
        files_summary = {}
        for item in all_dead_items:
            if item.file_path not in files_summary:
                files_summary[item.file_path] = []
            files_summary[item.file_path].append({
                "name": item.name,
                "type": item.item_type,
                "line": item.line,
                "confidence": item.confidence.lower()
            })
        
        result = {
            "status": "success",
            "summary": {
                "total_unused_items": len(all_dead_items),
                "confidence_breakdown": confidence_summary,
                "files_with_dead_code": len(files_summary),
                "entry_points_detected": len(dead_code_result.entry_points)
            },
            "unused_items": dead_items,
            "files_summary": files_summary,
            "entry_points": list(dead_code_result.entry_points),
            "recommendations": {
                "safe_to_remove": confidence_summary["high"],
                "review_carefully": confidence_summary["medium"],
                "likely_false_positives": confidence_summary["low"]
            }
        }
        
        return json.dumps(result, indent=2)

    except Exception as e:
        logging.error(f"Failed to analyze dead code: {e}")
        return json.dumps({"error": f"Failed to analyze dead code: {str(e)}"})


def analyze_code_structure(code: str, language: str = "python", include_metrics: bool = True) -> str:
    """
    Analyze code structure and extract metadata.

    Args:
        code: Source code to analyze
        language: Programming language (default: python)
        include_metrics: Whether to include complexity metrics

    Returns:
        JSON string containing analysis results
    """
    try:
        # Get metadata extractor
        extractor = get_metadata_extractor()

        # Create a temporary file-like object for analysis
        from io import StringIO
        import tempfile
        import os

        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix=f'.{language}', delete=False) as f:
            f.write(code)
            temp_path = f.name

        try:
            # Extract metadata from the code
            metadata = extractor.extract_metadata(temp_path, code)

            result = {
                "status": "success",
                "language": language,
                "analysis": {
                    "functions": [
                        {
                            "name": func.name,
                            "start_line": func.start_line,
                            "end_line": func.end_line,
                            "parameters": func.parameters,
                            "return_type": func.return_type,
                            "decorators": func.decorators,
                            "docstring": func.docstring
                        }
                        for func in metadata.functions
                    ],
                    "classes": [
                        {
                            "name": cls.name,
                            "start_line": cls.start_line,
                            "end_line": cls.end_line,
                            "base_classes": cls.base_classes,
                            "methods": [
                                {
                                    "name": method.name,
                                    "start_line": method.start_line,
                                    "parameters": method.parameters
                                }
                                for method in cls.methods
                            ]
                        }
                        for cls in metadata.classes
                    ],
                    "imports": [
                        {
                            "module": imp.module,
                            "items": imp.items,
                            "alias": imp.alias
                        }
                        for imp in metadata.imports
                    ]
                }
            }

            if include_metrics:
                lines = code.split('\n')
                result["metrics"] = {
                    "total_lines": len(lines),
                    "non_empty_lines": len([line for line in lines if line.strip()]),
                    "function_count": len(metadata.functions),
                    "class_count": len(metadata.classes),
                    "import_count": len(metadata.imports)
                }

            return json.dumps(result, indent=2)

        finally:
            # Clean up temporary file
            os.unlink(temp_path)

    except Exception as e:
        logging.error(f"Failed to analyze code structure: {e}")
        return json.dumps({"error": f"Failed to analyze code structure: {str(e)}"})


def build_call_graph(code: str, language: str = "python", include_external: bool = False) -> str:
    """
    Build call graph from source code.

    Args:
        code: Source code to analyze
        language: Programming language (default: python)
        include_external: Whether to include external dependencies

    Returns:
        JSON string containing call graph data
    """
    try:
        # Get metadata extractor
        extractor = get_metadata_extractor()

        # Create temporary file
        import tempfile
        import os

        with tempfile.NamedTemporaryFile(mode='w', suffix=f'.{language}', delete=False) as f:
            f.write(code)
            temp_path = f.name

        try:
            # Extract metadata and call graph
            metadata = extractor.extract_metadata(temp_path, code)
            call_graph_data = metadata.call_graph_data

            result = {
                "status": "success",
                "language": language,
                "call_graph": {
                    "nodes": call_graph_data.get("nodes", []),
                    "edges": call_graph_data.get("edges", []),
                    "external_calls": call_graph_data.get("external_calls", []) if include_external else []
                },
                "statistics": {
                    "total_functions": len(metadata.functions),
                    "total_calls": len(call_graph_data.get("edges", [])),
                    "external_calls": len(call_graph_data.get("external_calls", []))
                }
            }

            return json.dumps(result, indent=2)

        finally:
            # Clean up temporary file
            os.unlink(temp_path)

    except Exception as e:
        logging.error(f"Failed to build call graph: {e}")
        return json.dumps({"error": f"Failed to build call graph: {str(e)}"})


def register_analysis_tools(mcp_server: "FastMCP") -> None:
    """Register analysis-related tools with the MCP server."""

    # Import sandbox protection
    try:
        from . import sandbox_protect
    except ImportError:
        from __init__ import sandbox_protect

    @mcp_server.tool()
    def analyze_impact_tool(symbol_name: str,
                           project_path: Optional[str] = None) -> str:
        """
        Analyze the impact of changing or removing a symbol.
        Will auto-index the project if not already indexed.

        Args:
            symbol_name: Name of the symbol to analyze impact for
            project_path: Optional project path to search in

        Returns:
            JSON with impact analysis showing what would be affected by changes
        """
        # Apply sandbox protection as a protective glove
        protected_function = sandbox_protect(analyze_impact)
        return protected_function(symbol_name, project_path)
    
    @mcp_server.tool()
    def analyze_call_graph_tool(function_name: Optional[str] = None,
                               project_path: Optional[str] = None,
                               max_depth: int = Field(default=3, 
                                                    description="Maximum depth for call chain analysis")) -> str:
        """
        Analyze the call graph for a function or the entire project.
        Will auto-index the project if not already indexed.
        
        Args:
            function_name: Optional function name to focus analysis on
            project_path: Optional project path
            max_depth: Maximum depth for call chain analysis
        
        Returns:
            JSON with call graph analysis results
        """
        # Apply sandbox protection as a protective glove
        protected_function = sandbox_protect(analyze_call_graph)
        return protected_function(function_name, project_path, max_depth)
    
    @mcp_server.tool()
    def analyze_dead_code_tool(project_path: Optional[str] = None) -> str:
        """
        Analyze the project for potentially unused/dead code.
        Will auto-index the project if not already indexed.
        
        Args:
            project_path: Optional project path to analyze
        
        Returns:
            JSON with report of potentially dead code with confidence levels
        """
        # Apply sandbox protection as a protective glove
        protected_function = sandbox_protect(analyze_dead_code)
        return protected_function(project_path)
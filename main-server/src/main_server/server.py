"""
String MCP Main Server - Pure FastAPI Application

A standalone FastAPI server that handles all business logic, data processing,
authentication, and vector store operations. Provides HTTP API endpoints
for the MCP Gateway to consume.

This server is completely separated from MCP protocol handling and focuses
solely on business logic and data operations.
"""

import json
import logging
import os
import sys
from contextlib import asynccontextmanager
from datetime import datetime

import structlog
import uvicorn
from fastapi import FastAPI, HTTPException, Depends, Request, Body
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# Import existing components
from .api.fastapi_endpoints import register_simple_api_endpoints
from .auth.simple_auth_manager import get_simple_auth_manager, extract_user_id_from_api_key
from .cache.smart_redis_manager import SmartRedisManager
from .database.integration import initialize_database
from .integrations.vscode import VSCodeIntegration
from .processing.persistent_job_manager import PersistentJobManager
from .tools.analysis_tools import analyze_code_structure, build_call_graph, analyze_dead_code, analyze_impact
from .tools.reference_tools import find_all_references
# Import tool functions for direct execution
from .tools.search_tools import find_function, find_class, list_functions
from .vector.openai_qdrant_client import OpenAIQdrantClient, OpenAIQdrantConfig

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


@asynccontextmanager
async def app_lifespan(app: FastAPI):
    """Lifespan management for the main server."""
    logger.info("Starting Main String MCP Server", version="1.0.0")
    
    # Initialize database
    try:
        initialize_database()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error("Database initialization failed", error=str(e))
        raise
    
    # Initialize Redis cache
    try:
        redis_manager = SmartRedisManager()
        app.state.redis_manager = redis_manager
        logger.info("Redis cache initialized successfully")
    except Exception as e:
        logger.warning("Redis cache initialization failed", error=str(e))
        app.state.redis_manager = None
    
    # Initialize vector store
    try:
        qdrant_config = OpenAIQdrantConfig()
        vector_client = OpenAIQdrantClient(qdrant_config)
        app.state.vector_client = vector_client
        logger.info("Vector store initialized successfully")
    except Exception as e:
        logger.error("Vector store initialization failed", error=str(e))
        app.state.vector_client = None
    
    # Initialize job manager
    try:
        job_manager = PersistentJobManager()
        app.state.job_manager = job_manager
        logger.info("Job manager initialized successfully")
    except Exception as e:
        logger.error("Job manager initialization failed", error=str(e))
        app.state.job_manager = None
    
    # Initialize VSCode integration
    try:
        auth_manager = get_simple_auth_manager()
        vscode_integration = VSCodeIntegration(None, auth_manager)  # No MCP server needed
        app.state.vscode_integration = vscode_integration
        logger.info("VSCode integration initialized successfully")
    except Exception as e:
        logger.error("VSCode integration initialization failed", error=str(e))
        app.state.vscode_integration = None
    
    yield
    
    # Cleanup
    logger.info("Shutting down Main String MCP Server")
    
    # Close Redis connection
    if hasattr(app.state, 'redis_manager') and app.state.redis_manager:
        try:
            await app.state.redis_manager.close()
            logger.info("Redis connection closed")
        except Exception as e:
            logger.error("Error closing Redis connection", error=str(e))


def create_main_server() -> FastAPI:
    """Create and configure the main FastAPI server."""
    
    app = FastAPI(
        title="String MCP Main Server",
        description="Main server handling business logic and data operations for String MCP system",
        version="1.0.0",
        lifespan=app_lifespan
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Register API endpoints
    register_api_endpoints(app)
    
    logger.info("Main server created successfully")
    return app


def register_api_endpoints(app: FastAPI):
    """Register all API endpoints for the main server."""
    
    # Health check endpoint
    @app.get("/api/v1/health")
    async def health_check():
        """Health check endpoint for Railway and monitoring."""
        return {
            "status": "healthy",
            "service": "string-mcp-main-server",
            "version": "1.0.0",
            "timestamp": datetime.utcnow().isoformat()
        }
    
    # Authentication dependency
    async def get_current_user(request: Request):
        """Extract and validate user from API key."""
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Missing or invalid authorization header")
        
        api_key = auth_header.replace("Bearer ", "")
        user_id = extract_user_id_from_api_key(api_key)
        if not user_id:
            raise HTTPException(status_code=401, detail="Invalid API key")
        
        return user_id
    
    # Register existing API endpoints (reuse from current server)
    register_simple_api_endpoints(app)
    
    # Tool execution endpoints
    register_tool_endpoints(app, get_current_user)
    
    # Vector store endpoints
    register_vector_endpoints(app, get_current_user)
    
    # VSCode integration endpoints
    register_vscode_endpoints(app, get_current_user)
    
    # Job management endpoints
    register_job_endpoints(app, get_current_user)


def register_tool_endpoints(app: FastAPI, get_current_user):
    """Register tool execution endpoints."""
    
    @app.post("/api/v1/tools/{tool_name}/execute")
    async def execute_tool(
        tool_name: str,
        request: Request,
        user_id: str = Depends(get_current_user)
    ):
        """Execute a tool with the given arguments."""
        try:
            body = await request.json()
            arguments = body.get("arguments", {})
            
            # Execute the appropriate tool
            result = await execute_tool_by_name(tool_name, arguments, user_id)
            
            return {"result": result, "status": "success"}
            
        except Exception as e:
            logger.error("Tool execution failed", tool=tool_name, error=str(e))
            raise HTTPException(status_code=500, detail=f"Tool execution failed: {str(e)}")


def register_vector_endpoints(app: FastAPI, get_current_user):
    """Register vector store endpoints."""
    
    @app.post("/api/v1/vector/search")
    async def search_code(
        request: Request,
        user_id: str = Depends(get_current_user)
    ):
        """Search code in vector store."""
        try:
            body = await request.json()
            query = body.get("query")
            limit = body.get("limit", 10)
            filters = body.get("filters")
            
            if not query:
                raise HTTPException(status_code=400, detail="Query is required")
            
            vector_client = request.app.state.vector_client
            if not vector_client:
                raise HTTPException(status_code=503, detail="Vector store not available")
            
            results = vector_client.search_code(user_id, query, limit, filters)
            
            return {"results": results, "count": len(results)}
            
        except Exception as e:
            logger.error("Vector search failed", error=str(e))
            raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


def register_vscode_endpoints(app: FastAPI, get_current_user):
    """Register VSCode integration endpoints."""
    
    @app.post("/api/v1/vscode/process-chunks")
    async def process_vscode_chunks(
        request: Request,
        user_id: str = Depends(get_current_user)
    ):
        """Process file chunks from VSCode extension."""
        try:
            body = await request.json()
            chunks = body.get("chunks", [])
            
            vscode_integration = request.app.state.vscode_integration
            if not vscode_integration:
                raise HTTPException(status_code=503, detail="VSCode integration not available")
            
            # Process chunks
            result = await process_chunks_for_user(chunks, user_id, vscode_integration)
            
            return {"result": result, "status": "success"}
            
        except Exception as e:
            logger.error("VSCode chunk processing failed", error=str(e))
            raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")


def register_job_endpoints(app: FastAPI, get_current_user):
    """Register job management endpoints."""
    
    @app.get("/api/v1/jobs/{job_id}")
    async def get_job_status(
        job_id: str,
        request: Request,
        user_id: str = Depends(get_current_user)
    ):
        """Get job status."""
        try:
            job_manager = request.app.state.job_manager
            if not job_manager:
                raise HTTPException(status_code=503, detail="Job manager not available")
            
            job = job_manager.get_job(job_id)
            if not job:
                raise HTTPException(status_code=404, detail="Job not found")
            
            # Verify job belongs to user
            if job.user_id != user_id:
                raise HTTPException(status_code=403, detail="Access denied")
            
            return {
                "job_id": job.job_id,
                "status": job.status.value,
                "progress": job.progress,
                "result": job.result,
                "created_at": job.created_at.isoformat(),
                "completed_at": job.completed_at.isoformat() if job.completed_at else None
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error("Job status retrieval failed", error=str(e))
            raise HTTPException(status_code=500, detail=f"Status retrieval failed: {str(e)}")


async def execute_tool_by_name(tool_name: str, arguments: dict, user_id: str) -> str:
    """Execute a tool by name with the given arguments."""
    try:
        # Map tool names to their corresponding functions
        tool_map = {
            "find_function_tool": lambda args: find_function(
                args.get("function_name"),
                args.get("project_path")
            ),
            "find_class_tool": lambda args: find_class(
                args.get("class_name"),
                args.get("project_path")
            ),
            "list_functions_tool": lambda args: list_functions(
                args.get("project_path"),
                args.get("class_name"),
                args.get("limit", 50)
            ),
            "analyze_code_structure": lambda args: analyze_code_structure(
                args.get("code"),
                args.get("language", "python"),
                args.get("include_metrics", True)
            ),
            "build_call_graph": lambda args: build_call_graph(
                args.get("code"),
                args.get("language", "python"),
                args.get("include_external", False)
            ),
            "analyze_dead_code": lambda args: analyze_dead_code(
                args.get("project_path"),
                args.get("entry_points")
            ),
            "analyze_impact": lambda args: analyze_impact(
                args.get("symbol_name"),
                args.get("project_path")
            ),
            "find_all_references_tool": lambda args: find_all_references(
                args.get("symbol_name"),
                args.get("project_path"),
                args.get("include_definitions", True)
            )
        }

        if tool_name not in tool_map:
            raise ValueError(f"Unknown tool: {tool_name}")

        # Execute the tool function
        result = tool_map[tool_name](arguments)

        # If result is already a string (JSON), return it
        if isinstance(result, str):
            return result

        # Otherwise, convert to JSON string
        return json.dumps(result, indent=2)

    except Exception as e:
        logger.error("Tool execution error", tool=tool_name, error=str(e))
        error_response = {
            "error": str(e),
            "code": "TOOL_EXECUTION_ERROR",
            "tool": tool_name
        }
        return json.dumps(error_response)


async def process_chunks_for_user(chunks: list, user_id: str, vscode_integration) -> dict:
    """Process VSCode chunks for a specific user."""
    try:
        # Use the existing VSCode integration to process chunks
        # This would call the existing chunk processing pipeline
        result = await vscode_integration.process_chunks_async(chunks, user_id)

        return {
            "processed_chunks": len(chunks),
            "status": "success",
            "result": result
        }

    except Exception as e:
        logger.error("Chunk processing error", error=str(e))
        return {
            "processed_chunks": 0,
            "status": "error",
            "error": str(e)
        }


def main():
    """Main entry point for the server."""
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Get configuration
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))

    # Create and run server
    app = create_main_server()

    try:
        logger.info(f"Starting Main String MCP Server on {host}:{port}")
        uvicorn.run(
            app,
            host=host,
            port=port,
            log_level="info",
            access_log=True
        )
    except KeyboardInterrupt:
        logger.info("Server shutdown requested")
    except Exception as e:
        logger.error("Server startup failed", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    main()

"""
Main String RabTune Server - Business Logic and Data Operations

A FastAPI application that handles all business logic, data processing,
authentication, vector store operations, and provides HTTP API endpoints
for the RabTune Gateway to consume.
"""

__version__ = "1.0.0"
__author__ = "String RabTune Team"

# Main module exports
from . import tools
from . import vector
from . import security
from . import api
from . import integrations

__all__ = [
    "tools",
    "vector",
    "security",
    "api",
    "integrations",
]

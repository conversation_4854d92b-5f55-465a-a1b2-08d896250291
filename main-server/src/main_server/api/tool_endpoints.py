"""
Tool Execution HTTP API Endpoints

Provides HTTP endpoints for executing all tools that the MCP Gateway can call.
These endpoints handle tool execution and return JSON responses.
"""

import json
import logging
from typing import Dict, Any, Optional

from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel

from ..tools.search_tools import find_function, find_class, list_functions
from ..tools.analysis_tools import analyze_code_structure, build_call_graph, analyze_dead_code, analyze_impact
from ..tools.reference_tools import find_all_references

logger = logging.getLogger(__name__)


class ToolExecutionRequest(BaseModel):
    """Request model for tool execution."""
    arguments: Dict[str, Any]


class ToolExecutionResponse(BaseModel):
    """Response model for tool execution."""
    result: str
    status: str
    tool_name: str


def create_tool_router(get_current_user) -> APIRouter:
    """Create router for tool execution endpoints."""
    router = APIRouter(prefix="/api/v1/tools", tags=["tools"])
    
    @router.post("/find_function_tool/execute", response_model=ToolExecutionResponse)
    async def execute_find_function_tool(
        request: ToolExecutionRequest,
        user_id: str = Depends(get_current_user)
    ):
        """Execute find_function_tool."""
        try:
            args = request.arguments
            result = find_function(
                args.get("function_name"),
                args.get("project_path")
            )
            
            return ToolExecutionResponse(
                result=result,
                status="success",
                tool_name="find_function_tool"
            )
            
        except Exception as e:
            logger.error("find_function_tool execution failed", error=str(e))
            raise HTTPException(status_code=500, detail=f"Tool execution failed: {str(e)}")
    
    @router.post("/find_class_tool/execute", response_model=ToolExecutionResponse)
    async def execute_find_class_tool(
        request: ToolExecutionRequest,
        user_id: str = Depends(get_current_user)
    ):
        """Execute find_class_tool."""
        try:
            args = request.arguments
            result = find_class(
                args.get("class_name"),
                args.get("project_path")
            )
            
            return ToolExecutionResponse(
                result=result,
                status="success",
                tool_name="find_class_tool"
            )
            
        except Exception as e:
            logger.error("find_class_tool execution failed", error=str(e))
            raise HTTPException(status_code=500, detail=f"Tool execution failed: {str(e)}")
    
    @router.post("/list_functions_tool/execute", response_model=ToolExecutionResponse)
    async def execute_list_functions_tool(
        request: ToolExecutionRequest,
        user_id: str = Depends(get_current_user)
    ):
        """Execute list_functions_tool."""
        try:
            args = request.arguments
            result = list_functions(
                args.get("project_path"),
                args.get("class_name"),
                args.get("limit", 50)
            )
            
            return ToolExecutionResponse(
                result=result,
                status="success",
                tool_name="list_functions_tool"
            )
            
        except Exception as e:
            logger.error("list_functions_tool execution failed", error=str(e))
            raise HTTPException(status_code=500, detail=f"Tool execution failed: {str(e)}")
    
    @router.post("/analyze_code_structure/execute", response_model=ToolExecutionResponse)
    async def execute_analyze_code_structure(
        request: ToolExecutionRequest,
        user_id: str = Depends(get_current_user)
    ):
        """Execute analyze_code_structure."""
        try:
            args = request.arguments
            result = analyze_code_structure(
                args.get("code"),
                args.get("language", "python"),
                args.get("include_metrics", True)
            )
            
            return ToolExecutionResponse(
                result=result,
                status="success",
                tool_name="analyze_code_structure"
            )
            
        except Exception as e:
            logger.error("analyze_code_structure execution failed", error=str(e))
            raise HTTPException(status_code=500, detail=f"Tool execution failed: {str(e)}")
    
    @router.post("/build_call_graph/execute", response_model=ToolExecutionResponse)
    async def execute_build_call_graph(
        request: ToolExecutionRequest,
        user_id: str = Depends(get_current_user)
    ):
        """Execute build_call_graph."""
        try:
            args = request.arguments
            result = build_call_graph(
                args.get("code"),
                args.get("language", "python"),
                args.get("include_external", False)
            )
            
            return ToolExecutionResponse(
                result=result,
                status="success",
                tool_name="build_call_graph"
            )
            
        except Exception as e:
            logger.error("build_call_graph execution failed", error=str(e))
            raise HTTPException(status_code=500, detail=f"Tool execution failed: {str(e)}")
    
    @router.post("/analyze_dead_code/execute", response_model=ToolExecutionResponse)
    async def execute_analyze_dead_code(
        request: ToolExecutionRequest,
        user_id: str = Depends(get_current_user)
    ):
        """Execute analyze_dead_code."""
        try:
            args = request.arguments
            result = analyze_dead_code(
                args.get("project_path"),
                args.get("entry_points")
            )
            
            return ToolExecutionResponse(
                result=result,
                status="success",
                tool_name="analyze_dead_code"
            )
            
        except Exception as e:
            logger.error("analyze_dead_code execution failed", error=str(e))
            raise HTTPException(status_code=500, detail=f"Tool execution failed: {str(e)}")
    
    @router.post("/analyze_impact/execute", response_model=ToolExecutionResponse)
    async def execute_analyze_impact(
        request: ToolExecutionRequest,
        user_id: str = Depends(get_current_user)
    ):
        """Execute analyze_impact."""
        try:
            args = request.arguments
            result = analyze_impact(
                args.get("symbol_name"),
                args.get("project_path")
            )
            
            return ToolExecutionResponse(
                result=result,
                status="success",
                tool_name="analyze_impact"
            )
            
        except Exception as e:
            logger.error("analyze_impact execution failed", error=str(e))
            raise HTTPException(status_code=500, detail=f"Tool execution failed: {str(e)}")
    
    @router.post("/find_all_references_tool/execute", response_model=ToolExecutionResponse)
    async def execute_find_all_references_tool(
        request: ToolExecutionRequest,
        user_id: str = Depends(get_current_user)
    ):
        """Execute find_all_references_tool."""
        try:
            args = request.arguments
            result = find_all_references(
                args.get("symbol_name"),
                args.get("project_path"),
                args.get("include_definitions", True)
            )
            
            return ToolExecutionResponse(
                result=result,
                status="success",
                tool_name="find_all_references_tool"
            )
            
        except Exception as e:
            logger.error("find_all_references_tool execution failed", error=str(e))
            raise HTTPException(status_code=500, detail=f"Tool execution failed: {str(e)}")
    
    # Generic tool execution endpoint (fallback)
    @router.post("/{tool_name}/execute", response_model=ToolExecutionResponse)
    async def execute_generic_tool(
        tool_name: str,
        request: ToolExecutionRequest,
        user_id: str = Depends(get_current_user)
    ):
        """Execute any tool by name (generic endpoint)."""
        try:
            # Map tool names to their corresponding functions
            tool_map = {
                "find_function_tool": lambda args: find_function(
                    args.get("function_name"),
                    args.get("project_path")
                ),
                "find_class_tool": lambda args: find_class(
                    args.get("class_name"),
                    args.get("project_path")
                ),
                "list_functions_tool": lambda args: list_functions(
                    args.get("project_path"),
                    args.get("class_name"),
                    args.get("limit", 50)
                ),
                "analyze_code_structure": lambda args: analyze_code_structure(
                    args.get("code"),
                    args.get("language", "python"),
                    args.get("include_metrics", True)
                ),
                "build_call_graph": lambda args: build_call_graph(
                    args.get("code"),
                    args.get("language", "python"),
                    args.get("include_external", False)
                ),
                "analyze_dead_code": lambda args: analyze_dead_code(
                    args.get("project_path"),
                    args.get("entry_points")
                ),
                "analyze_impact": lambda args: analyze_impact(
                    args.get("symbol_name"),
                    args.get("project_path")
                ),
                "find_all_references_tool": lambda args: find_all_references(
                    args.get("symbol_name"),
                    args.get("project_path"),
                    args.get("include_definitions", True)
                )
            }
            
            if tool_name not in tool_map:
                raise HTTPException(status_code=404, detail=f"Unknown tool: {tool_name}")
            
            # Execute the tool function
            result = tool_map[tool_name](request.arguments)
            
            return ToolExecutionResponse(
                result=result,
                status="success",
                tool_name=tool_name
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error("Generic tool execution failed", tool=tool_name, error=str(e))
            raise HTTPException(status_code=500, detail=f"Tool execution failed: {str(e)}")
    
    return router

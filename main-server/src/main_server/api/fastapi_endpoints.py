"""
FastAPI-compatible API Endpoints for Connection Management

RESTful endpoints following API best practices for managing mcp_ API keys
and VSCode extension connection identification.
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, Request, Body
from fastapi.responses import JSONResponse

try:
    from ..auth.simple_auth_manager import get_simple_auth_manager
except ImportError:
    from auth.simple_auth_manager import get_simple_auth_manager

logger = logging.getLogger(__name__)


def create_api_response(
    data: Dict[str, Any], 
    success: bool = True, 
    status_code: int = 200
) -> JSONResponse:
    """Create a standardized API response."""
    response = {
        "success": success,
        "timestamp": datetime.utcnow().isoformat(),
        "data" if success else "error": data
    }
    
    # Standard CORS headers
    headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers": "*",
    }
    
    return JSONResponse(response, status_code=status_code, headers=headers)


def register_simple_api_endpoints(app: FastAPI):
    """Register standardized API endpoints with the FastAPI app."""
    logger.info("Registering FastAPI endpoints...")
    auth_manager = get_simple_auth_manager()
    
    # Check if auth manager is available
    if auth_manager is None:
        logger.warning("Auth manager not available - API endpoints will return errors")
    
    @app.post("/api/v1/connections")
    async def create_connection(request: Request):
        """
        Create a new connection (user + API key) for VSCode extension.
        
        This is the main endpoint for setting up a new VSCode extension connection.
        """
        try:
            body = await request.body()
            data = json.loads(body)
            
            identifier = data.get("identifier")  # email, username, etc.
            name = data.get("name")  # display name
            
            if not identifier or not name:
                error_data = {
                    "code": "MISSING_REQUIRED_FIELDS",
                    "message": "identifier and name are required",
                    "details": {"required_fields": ["identifier", "name"]}
                }
                return create_api_response(error_data, success=False, status_code=400)
            
            # Check if user already exists
            existing_user = auth_manager.get_user_by_identifier(identifier)
            if existing_user:
                error_data = {
                    "code": "CONNECTION_ALREADY_EXISTS",
                    "message": f"User '{identifier}' already has an API key",
                    "details": {
                        "existing_key_prefix": existing_user.api_key[:8] + "..." if existing_user.api_key else None
                    }
                }
                return create_api_response(error_data, success=False, status_code=409)
            
            # Create new user with API key
            user = auth_manager.create_user_with_key(identifier, name)
            if user:
                response_data = {
                    "connection": {
                        "user_id": user.id,
                        "identifier": user.identifier,
                        "name": user.name,
                        "api_key": user.api_key,
                        "created_at": user.created_at.isoformat()
                    },
                    "setup_instructions": {
                        "vscode_settings": {
                            "mcp.url": "https://mcp.rabtune.com",
                            "mcp.apiKey": user.api_key,
                            "mcp.maxChunkSize": 2000
                        },
                        "note": "Add these settings to your VSCode settings.json"
                    }
                }
                return create_api_response(response_data, status_code=201)
            else:
                error_data = {
                    "code": "CONNECTION_CREATION_FAILED",
                    "message": "Failed to create connection",
                    "details": {}
                }
                return create_api_response(error_data, success=False, status_code=500)
        
        except json.JSONDecodeError:
            error_data = {
                "code": "INVALID_JSON",
                "message": "Request body must be valid JSON",
                "details": {}
            }
            return create_api_response(error_data, success=False, status_code=400)
        except Exception as e:
            logger.error(f"Error creating connection: {e}")
            error_data = {
                "code": "INTERNAL_SERVER_ERROR",
                "message": "An unexpected error occurred",
                "details": {"error": str(e)}
            }
            return create_api_response(error_data, success=False, status_code=500)
    
    @app.post("/api/v1/connections/validate")
    async def validate_connection(request: Request):
        """Validate a connection API key."""
        try:
            body = await request.body()
            data = json.loads(body)
            
            api_key = data.get("api_key")
            if not api_key:
                error_data = {
                    "code": "MISSING_API_KEY",
                    "message": "api_key is required",
                    "details": {"required_fields": ["api_key"]}
                }
                return create_api_response(error_data, success=False, status_code=400)
            
            # Validate the connection
            user = auth_manager.validate_api_key(api_key)
            if user:
                response_data = {
                    "valid": True,
                    "user": {
                        "user_id": user.id,
                        "identifier": user.identifier,
                        "name": user.name,
                        "created_at": user.created_at.isoformat(),
                        "last_used": user.last_used.isoformat() if user.last_used else None,
                        "usage_count": user.usage_count
                    }
                }
                return create_api_response(response_data)
            else:
                error_data = {
                    "code": "INVALID_API_KEY",
                    "message": "API key not found or invalid format",
                    "details": {"api_key_prefix": api_key[:8] + "..." if len(api_key) > 8 else api_key}
                }
                return create_api_response(error_data, success=False, status_code=401)
        
        except json.JSONDecodeError:
            error_data = {
                "code": "INVALID_JSON",
                "message": "Request body must be valid JSON",
                "details": {}
            }
            return create_api_response(error_data, success=False, status_code=400)
        except Exception as e:
            logger.error(f"Error validating connection: {e}")
            error_data = {
                "code": "INTERNAL_SERVER_ERROR",
                "message": "An unexpected error occurred",
                "details": {"error": str(e)}
            }
            return create_api_response(error_data, success=False, status_code=500)
    
    @app.get("/api/v1/connections")
    async def list_connections():
        """List all connections (for admin purposes)."""
        try:
            users = auth_manager.get_all_users()
            
            connections = []
            for user in users:
                connections.append({
                    "user_id": user.id,
                    "identifier": user.identifier,
                    "name": user.name,
                    "created_at": user.created_at.isoformat(),
                    "last_used": user.last_used.isoformat() if user.last_used else None,
                    "usage_count": user.usage_count,
                    "api_key_prefix": user.api_key[:8] + "..." if user.api_key else None
                })
            
            response_data = {
                "connections": connections,
                "total_count": len(connections)
            }
            return create_api_response(response_data)
        
        except Exception as e:
            logger.error(f"Error listing connections: {e}")
            error_data = {
                "code": "INTERNAL_SERVER_ERROR",
                "message": "An unexpected error occurred",
                "details": {"error": str(e)}
            }
            return create_api_response(error_data, success=False, status_code=500)
    
    @app.get("/api/v1/connections/stats")
    async def get_connection_stats():
        """Get connection statistics."""
        try:
            stats = auth_manager.get_stats()
            
            response_data = {
                "stats": stats,
                "server_info": {
                    "service": "string-mcp-main-server",
                    "version": "1.0.0",
                    "auth_type": "simple_identification"
                }
            }
            return create_api_response(response_data)
        
        except Exception as e:
            logger.error(f"Error getting stats: {e}")
            error_data = {
                "code": "INTERNAL_SERVER_ERROR",
                "message": "An unexpected error occurred",
                "details": {"error": str(e)}
            }
            return create_api_response(error_data, success=False, status_code=500)
    
    logger.info("FastAPI endpoints registered successfully")
    return auth_manager

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "string-mcp-main-server"
version = "1.0.0"
description = "Main String MCP server with full business logic and data operations"
readme = "README.md"
requires-python = ">=3.11"
license = {text = "MIT"}
authors = [
    {name = "String MCP Team", email = "<EMAIL>"},
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Code Generators",
]

dependencies = [
    # Core FastAPI and server (NO MCP dependencies)
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    # Code analysis dependencies
    "networkx>=3.0",
    "tree-sitter>=0.20.0",
    "tree-sitter-python>=0.20.0",
    # Security and authentication
    "cryptography>=41.0.0",
    "python-multipart>=0.0.6",
    "passlib[bcrypt]>=1.7.4",
    "python-jose[cryptography]>=3.3.0",
    "bcrypt>=4.1.0",
    # System monitoring
    "psutil>=5.9.0",
    # File processing and validation
    "python-magic>=0.4.27",
    "chardet>=5.2.0",
    "filetype>=1.2.0",
    "python-magic-bin>=0.4.14; sys_platform == 'win32'",
    # HTTP clients and CORS
    "httpx>=0.25.0",
    "requests>=2.31.0",
    # Vector database and embeddings
    "qdrant-client>=1.14.2",
    "openai>=1.83.0",
    "numpy>=1.24.0",
    # Logging and monitoring
    "structlog>=23.2.0",
    "python-json-logger>=2.0.0",
    # Rate limiting
    "slowapi>=0.1.9",
    # Async support
    "aiofiles>=23.2.1",
    "async-timeout>=4.0.3",
    # Configuration
    "python-dotenv>=1.0.0",
    # Data processing
    "orjson>=3.9.0",
    "jsonschema>=4.20.0",
    # Security scanning
    "regex>=2023.10.3",
    "bandit>=1.7.5",
    # Utilities
    "click>=8.1.0",
    "typing-extensions>=4.8.0",
    "python-dateutil>=2.8.0",
    # Caching and performance
    "cachetools>=5.3.0",
    "redis>=5.0.0",
    # Network security
    "certifi>=2023.11.17",
    "urllib3>=2.1.0",
    # Error tracking
    "sentry-sdk[fastapi]>=1.38.0",
    "aiohttp>=3.12.7",
]

[project.scripts]
main-server = "src.main_server.server:main"
string-mcp-main = "src.main_server.server:main"

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.12.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "mypy>=1.7.0",
]

monitoring = [
    "prometheus-client>=0.19.0",
    "py-spy>=0.3.14",
    "memory-profiler>=0.61.0",
    "pympler>=0.9",
]

[project.urls]
Homepage = "https://github.com/string-mcp/main-server"
Repository = "https://github.com/string-mcp/main-server"

[tool.uv]
dev-dependencies = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.12.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "mypy>=1.7.0",
]

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
check_untyped_defs = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = ["--strict-markers", "--strict-config"]
testpaths = ["tests"]
asyncio_mode = "auto"

# String MCP Main Server - API Documentation

This document describes all HTTP API endpoints provided by the String MCP Main Server (Pure FastAPI).

## Base URL
- **Production**: `https://mcp.rabtune.com`
- **Local Development**: `http://localhost:8000`

## Authentication

All API endpoints (except health checks) require authentication via <PERSON><PERSON> token:

```
Authorization: Bearer mcp_your_api_key_here
```

## API Endpoints

### Health & Status

#### `GET /api/v1/health`
Health check endpoint for monitoring and Railway.

**Response:**
```json
{
  "status": "healthy",
  "service": "string-mcp-main-server",
  "version": "1.0.0",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Authentication & Connection Management

#### `POST /api/v1/connections`
Create a new user connection and API key.

**Request:**
```json
{
  "identifier": "<EMAIL>",
  "name": "<PERSON>"
}
```

**Response:**
```json
{
  "success": true,
  "timestamp": "2024-01-01T00:00:00.000Z",
  "data": {
    "connection": {
      "user_id": "user_123",
      "identifier": "<EMAIL>",
      "name": "John Doe",
      "api_key": "mcp_abc123...",
      "created_at": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

#### `POST /api/v1/connections/validate`
Validate an API key.

**Request:**
```json
{
  "api_key": "mcp_abc123..."
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "valid": true,
    "user": {
      "user_id": "user_123",
      "identifier": "<EMAIL>",
      "name": "John Doe",
      "created_at": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

#### `GET /api/v1/connections`
List all connections (admin endpoint).

#### `GET /api/v1/connections/stats`
Get connection statistics.

### Tool Execution

All tool endpoints follow the pattern: `POST /api/v1/tools/{tool_name}/execute`

**Request Format:**
```json
{
  "arguments": {
    "param1": "value1",
    "param2": "value2"
  }
}
```

**Response Format:**
```json
{
  "result": "JSON string containing tool results",
  "status": "success",
  "tool_name": "tool_name"
}
```

#### `POST /api/v1/tools/find_function_tool/execute`
Find a function by name in the indexed codebase.

**Arguments:**
- `function_name` (string): Name of the function to find
- `project_path` (string, optional): Project path to search in

#### `POST /api/v1/tools/find_class_tool/execute`
Find a class by name in the indexed codebase.

**Arguments:**
- `class_name` (string): Name of the class to find
- `project_path` (string, optional): Project path to search in

#### `POST /api/v1/tools/list_functions_tool/execute`
List all functions in the indexed codebase.

**Arguments:**
- `project_path` (string, optional): Project path
- `class_name` (string, optional): Class name to filter methods
- `limit` (integer, default: 50): Maximum number of functions to return

#### `POST /api/v1/tools/analyze_code_structure/execute`
Analyze code structure and extract metadata.

**Arguments:**
- `code` (string): Source code to analyze
- `language` (string, default: "python"): Programming language
- `include_metrics` (boolean, default: true): Whether to include complexity metrics

#### `POST /api/v1/tools/build_call_graph/execute`
Build call graph from source code.

**Arguments:**
- `code` (string): Source code to analyze
- `language` (string, default: "python"): Programming language
- `include_external` (boolean, default: false): Whether to include external dependencies

#### `POST /api/v1/tools/analyze_dead_code/execute`
Analyze dead code in the project.

**Arguments:**
- `project_path` (string, optional): Project path to analyze
- `entry_points` (array, optional): List of entry point functions

#### `POST /api/v1/tools/analyze_impact/execute`
Analyze the impact of changing a symbol.

**Arguments:**
- `symbol_name` (string): Name of the symbol to analyze
- `project_path` (string, optional): Project path to analyze

#### `POST /api/v1/tools/find_all_references_tool/execute`
Find all references to a symbol in the codebase.

**Arguments:**
- `symbol_name` (string): Name of the symbol to find references for
- `project_path` (string, optional): Project path to search in
- `include_definitions` (boolean, default: true): Whether to include definition location

### Vector Store Operations

#### `POST /api/v1/vector/search`
Search code in vector store using semantic similarity.

**Request:**
```json
{
  "query": "search query",
  "limit": 10,
  "filters": {
    "file_type": "python",
    "project": "my_project"
  }
}
```

**Response:**
```json
{
  "results": [
    {
      "content": "code snippet",
      "metadata": {
        "file_path": "path/to/file.py",
        "function_name": "my_function",
        "line_start": 10,
        "line_end": 20
      },
      "score": 0.95
    }
  ],
  "count": 1
}
```

### VSCode Integration

#### `POST /api/v1/vscode/process-chunks`
Process file chunks from VSCode extension.

**Request:**
```json
{
  "chunks": [
    {
      "content": "code content",
      "metadata": {
        "file_path": "path/to/file.py",
        "chunk_index": 0,
        "total_chunks": 5
      }
    }
  ]
}
```

**Response:**
```json
{
  "result": {
    "processed_chunks": 1,
    "status": "success",
    "job_id": "job_123"
  },
  "status": "success"
}
```

### Job Management

#### `GET /api/v1/jobs/{job_id}`
Get the status of a processing job.

**Response:**
```json
{
  "job_id": "job_123",
  "status": "completed",
  "progress": 100,
  "result": {
    "processed_files": 10,
    "indexed_chunks": 50
  },
  "created_at": "2024-01-01T00:00:00.000Z",
  "completed_at": "2024-01-01T00:05:00.000Z"
}
```

## Error Responses

All endpoints return standardized error responses:

```json
{
  "detail": "Error message",
  "status_code": 400
}
```

Common status codes:
- `400`: Bad Request (missing parameters, invalid data)
- `401`: Unauthorized (invalid or missing API key)
- `403`: Forbidden (access denied)
- `404`: Not Found (resource not found)
- `500`: Internal Server Error (server-side error)

## Rate Limiting

The API implements rate limiting to prevent abuse:
- **Default**: 100 requests per minute per API key
- **Tool Execution**: 20 requests per minute per API key
- **Vector Search**: 50 requests per minute per API key

## Interactive Documentation

When the server is running, you can access interactive API documentation:
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

## Example Usage

### Using curl

```bash
# Health check
curl https://mcp.rabtune.com/api/v1/health

# Create connection
curl -X POST https://mcp.rabtune.com/api/v1/connections \
  -H "Content-Type: application/json" \
  -d '{"identifier": "<EMAIL>", "name": "John Doe"}'

# Execute tool
curl -X POST https://mcp.rabtune.com/api/v1/tools/analyze_code_structure/execute \
  -H "Authorization: Bearer mcp_your_api_key" \
  -H "Content-Type: application/json" \
  -d '{"arguments": {"code": "def hello(): return \"world\"", "language": "python"}}'

# Search code
curl -X POST https://mcp.rabtune.com/api/v1/vector/search \
  -H "Authorization: Bearer mcp_your_api_key" \
  -H "Content-Type: application/json" \
  -d '{"query": "function definition", "limit": 5}'
```

### Using Python requests

```python
import requests

# Base configuration
base_url = "https://mcp.rabtune.com"
headers = {
    "Authorization": "Bearer mcp_your_api_key",
    "Content-Type": "application/json"
}

# Execute tool
response = requests.post(
    f"{base_url}/api/v1/tools/find_function_tool/execute",
    headers=headers,
    json={
        "arguments": {
            "function_name": "my_function",
            "project_path": "/path/to/project"
        }
    }
)

result = response.json()
print(result["result"])
```

#!/usr/bin/env python3
"""
Minimal MCP Server Entry Point

Lightweight startup script for the minimal MCP server.
Provides robust import handling and Railway compatibility for minimal deployments.
"""

import sys
import os
import logging

def setup_minimal_environment():
    """Setup environment for minimal MCP server deployment."""
    # Get the directory containing this script (project root)
    project_root = os.path.dirname(os.path.abspath(__file__))
    src_dir = os.path.join(project_root, 'src')
    
    # Add directories to Python path
    for path in [project_root, src_dir]:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    # Set working directory to project root
    os.chdir(project_root)

def main():
    """Main entry point for minimal MCP server with comprehensive error handling."""
    # Setup logging early
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 Starting Minimal MCP Server...")
    logger.info("Profile: Lightweight, essential tools only")
    
    # Setup environment
    setup_minimal_environment()
    
    # Log environment information
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Working directory: {os.getcwd()}")
    
    # Check for Railway environment (minimal server can also run on Railway)
    if os.getenv('RAILWAY_ENVIRONMENT'):
        logger.info(f"🚂 Railway deployment detected (minimal profile)")
        logger.info(f"Railway environment: {os.getenv('RAILWAY_ENVIRONMENT')}")
        # Note: Minimal server ignores PORT and always uses stdio
        logger.info("Note: Minimal server uses stdio transport regardless of Railway PORT")
    
    # Try multiple import strategies for minimal server
    import_strategies = [
        ("Package-style import", lambda: __import_package_style()),
        ("Module import", lambda: __import_module_style()),
        ("Direct import", lambda: __import_direct_style()),
    ]
    
    for strategy_name, import_func in import_strategies:
        try:
            logger.info(f"Attempting {strategy_name}...")
            minimal_main = import_func()
            logger.info(f"✅ {strategy_name} successful")
            
            # Run the minimal server
            minimal_main()
            return  # Success!
            
        except ImportError as e:
            logger.warning(f"❌ {strategy_name} failed: {e}")
            continue
        except Exception as e:
            logger.error(f"❌ {strategy_name} failed with unexpected error: {e}")
            continue
    
    # If all strategies failed
    logger.error("❌ All import strategies failed for minimal server")
    logger.error("Available files in current directory:")
    for item in os.listdir('.'):
        logger.error(f"  - {item}")
    
    if os.path.exists('src'):
        logger.error("Available files in src directory:")
        for item in os.listdir('src'):
            logger.error(f"  - src/{item}")
    
    logger.error("Python path:")
    for i, path in enumerate(sys.path[:5]):  # Show first 5 entries
        logger.error(f"  {i}: {path}")
    
    sys.exit(1)

def __import_package_style():
    """Try package-style import for minimal server."""
    from src.core.minimal_server import main
    return main

def __import_module_style():
    """Try module-style import for minimal server."""
    import src.core.minimal_server
    return src.core.minimal_server.main

def __import_direct_style():
    """Try direct import for minimal server."""
    from core.minimal_server import main
    return main

if __name__ == "__main__":
    main()

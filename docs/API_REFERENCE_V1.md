# 📚 **String MCP Server API Reference v1**

## **Base URL**
```
https://mcp.rabtune.com
```

## **Authentication**
All authenticated endpoints require a Bearer token:
```
Authorization: Bearer mcp_your_api_key_here
```

## **Response Format**

### **Success Response**
```json
{
  "success": true,
  "data": { /* response data */ },
  "meta": {
    "timestamp": "2025-01-XX...",
    "version": "v1",
    "request_id": "uuid"
  }
}
```

### **Error Response**
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable message",
    "details": { /* additional details */ }
  },
  "meta": {
    "timestamp": "2025-01-XX...",
    "version": "v1",
    "request_id": "uuid"
  }
}
```

---

## **🏥 Health & Monitoring**

### **GET /api/v1/health**
Health check endpoint for monitoring.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "service": "string-mcp-server",
    "path": "/api/v1/health",
    "hybrid_cache": {
      "system_health": "optimal",
      "redis_coordination": "healthy",
      "server_cache": "healthy"
    }
  }
}
```

### **GET /api/v1/status**
Comprehensive server status.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "service": "string-mcp-server",
    "version": "1.0.0",
    "endpoints": {
      "health": "/api/v1/health",
      "connections": "/api/v1/connections",
      "vscode": "/api/v1/vscode",
      "jobs": "/api/v1/jobs"
    },
    "transport": "streamable-http"
  }
}
```

### **GET /api/v1/mcp/info**
MCP server information.

**Response:**
```json
{
  "success": true,
  "data": {
    "name": "String MCP Server",
    "version": "1.0.0",
    "transport": "streamable-http",
    "capabilities": ["tools", "resources", "prompts"],
    "mount_path": "/mcp",
    "stateless": true
  }
}
```

---

## **🔗 Connection Management**

### **POST /api/v1/connections**
Create a new connection with mcp_ API key.

**Request:**
```json
{
  "identifier": "<EMAIL>",
  "name": "John Doe"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "connection": {
      "user_id": "12345",
      "identifier": "<EMAIL>",
      "name": "John Doe",
      "api_key": "mcp_...",
      "created_at": "2025-01-XX..."
    },
    "setup_instructions": {
      "vscode_settings": {
        "mcp.url": "https://mcp.rabtune.com",
        "mcp.apiKey": "mcp_...",
        "mcp.maxChunkSize": 2000
      }
    }
  }
}
```

### **POST /api/v1/connections/validate**
Validate an mcp_ API key.

**Request:**
```json
{
  "api_key": "mcp_your_api_key_here"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "valid": true,
    "connection": {
      "user_id": "12345",
      "identifier": "<EMAIL>",
      "name": "John Doe",
      "created_at": "2025-01-XX...",
      "last_used": "2025-01-XX...",
      "usage_count": 42
    }
  }
}
```

### **POST /api/v1/connections/regenerate**
Regenerate API key for existing connection.

**Request:**
```json
{
  "identifier": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "connection": {
      "user_id": "12345",
      "identifier": "<EMAIL>",
      "name": "John Doe",
      "new_api_key": "mcp_new_key_here"
    },
    "setup_instructions": {
      "vscode_settings": {
        "mcp.url": "https://mcp.rabtune.com",
        "mcp.apiKey": "mcp_new_key_here"
      }
    }
  }
}
```

### **GET /api/v1/connections**
List all connections (admin).

**Response:**
```json
{
  "success": true,
  "data": {
    "connections": [
      {
        "user_id": "12345",
        "identifier": "<EMAIL>",
        "name": "John Doe",
        "created_at": "2025-01-XX...",
        "last_used": "2025-01-XX...",
        "usage_count": 42,
        "api_key_prefix": "mcp_abcd..."
      }
    ],
    "total_count": 1
  }
}
```

### **GET /api/v1/connections/stats**
Get connection statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "stats": {
      "total_users": 10,
      "active_users": 8,
      "total_api_calls": 1500
    },
    "server_info": {
      "service": "string-mcp-server",
      "version": "1.0.0",
      "auth_type": "simple_identification"
    }
  }
}
```

---

## **💻 VSCode Integration**

### **POST /api/v1/vscode/chunks**
Receive code chunks from VSCode extension.

**Authentication:** Required (Bearer token)

**Request:**
```json
{
  "metadata": {
    "file_path": "/path/to/file.py",
    "chunk_index": 0,
    "webhook_url": "https://optional-webhook.com/callback"
  },
  "content": "def hello_world():\n    print('Hello, World!')"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "chunk_id": "uuid-here",
    "processing_time_ms": 150,
    "job_id": "job-uuid-here"
  }
}
```

**Error Codes:**
- `MISSING_FILE_PATH`: file_path is required
- `MISSING_CHUNK_INDEX`: chunk_index is required
- `MISSING_CONTENT`: content is required
- `CHUNK_TOO_LARGE`: Chunk exceeds 10KB limit
- `AUTHENTICATION_REQUIRED`: Bearer token required

### **GET /api/v1/vscode/status**
Get VSCode integration status and statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "service": "vscode-mcp-integration",
    "status": "healthy",
    "chunk_processing_stats": {
      "chunks_received": 150,
      "chunks_validated": 148,
      "files_processed": 12
    },
    "vector_processing_stats": {
      "embeddings_generated": 120,
      "chunks_stored": 118,
      "cache_hits": 45
    },
    "active_files": 5,
    "temp_files": 2,
    "hybrid_cache": {
      "enabled": true,
      "initialized": true,
      "system_health": "optimal"
    }
  }
}
```

### **POST /api/v1/vscode/cleanup**
Clean up temporary files.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "completed",
    "message": "Cleaned up 5 temporary files",
    "files_cleaned": 5
  }
}
```

---

## **⚙️ Job Management**

### **GET /api/v1/jobs**
Get all jobs for authenticated user.

**Authentication:** Required (Bearer token)

**Query Parameters:**
- `status` (optional): Filter by job status
- `limit` (optional): Maximum number of jobs (default: 50)

**Response:**
```json
{
  "success": true,
  "data": {
    "jobs": [
      {
        "job_id": "job-uuid",
        "job_type": "CHUNK_PROCESSING",
        "status": "COMPLETED",
        "created_at": "2025-01-XX...",
        "completed_at": "2025-01-XX...",
        "duration_seconds": 2.5,
        "progress_percentage": 100,
        "success": true
      }
    ],
    "total_count": 1,
    "user_id": "user-123",
    "filters": {
      "status": null,
      "limit": 50
    }
  }
}
```

### **GET /api/v1/jobs/{job_id}**
Get specific job status.

**Authentication:** Required (Bearer token)

**Response:**
```json
{
  "success": true,
  "data": {
    "job": {
      "job_id": "job-uuid",
      "job_type": "CHUNK_PROCESSING",
      "status": "COMPLETED",
      "created_at": "2025-01-XX...",
      "started_at": "2025-01-XX...",
      "completed_at": "2025-01-XX...",
      "duration_seconds": 2.5,
      "progress": {
        "current_step": 4,
        "total_steps": 4,
        "percentage": 100,
        "current_operation": "Completed",
        "estimated_completion": null
      },
      "result": {
        "success": true,
        "data": {
          "chunks_processed": 1,
          "file_path": "/path/to/file.py"
        },
        "error_message": null,
        "warnings": [],
        "metrics": {
          "processing_time_ms": 2500,
          "chunks_count": 1
        }
      },
      "metadata": {
        "file_path": "/path/to/file.py",
        "chunk_index": 0,
        "source": "vscode-extension"
      }
    }
  }
}
```

### **GET /api/v1/jobs/stats**
Get job statistics for authenticated user.

**Authentication:** Required (Bearer token)

**Response:**
```json
{
  "success": true,
  "data": {
    "global_stats": {
      "total_jobs": 1000,
      "active_jobs": 5,
      "completed_jobs": 990
    },
    "user_stats": {
      "total_jobs": 25,
      "active_jobs": 1,
      "completed_jobs": 24,
      "success_rate": 96.0
    },
    "user_id": "user-123"
  }
}
```

---

## **🔧 MCP Protocol**

### **POST /mcp/***
MCP protocol endpoints (unchanged).

**Authentication:** mcp_ API key required

These endpoints follow the MCP protocol specification and handle:
- Tool execution
- Resource access
- Prompt management

---

## **📊 HTTP Status Codes**

- **200**: Success (GET requests)
- **201**: Created (POST requests)
- **400**: Bad Request (validation errors)
- **401**: Unauthorized (authentication required)
- **403**: Forbidden (access denied)
- **404**: Not Found (resource not found)
- **409**: Conflict (resource already exists)
- **413**: Payload Too Large (chunk size exceeded)
- **500**: Internal Server Error
- **503**: Service Unavailable (health degraded)

---

## **🚨 Common Error Codes**

### **Authentication Errors**
- `AUTHENTICATION_REQUIRED`: Bearer token required
- `INVALID_API_KEY`: API key not found or invalid

### **Validation Errors**
- `MISSING_REQUIRED_FIELDS`: Required fields missing
- `INVALID_JSON`: Request body must be valid JSON
- `CHUNK_TOO_LARGE`: Chunk exceeds size limit

### **Resource Errors**
- `CONNECTION_NOT_FOUND`: Connection not found
- `JOB_NOT_FOUND`: Job not found
- `CONNECTION_ALREADY_EXISTS`: User already has API key

### **System Errors**
- `INTERNAL_SERVER_ERROR`: Unexpected server error
- `CHUNK_PROCESSING_ERROR`: Error processing chunk
- `STATUS_ERROR`: Error retrieving status

---

## **💡 Best Practices**

1. **Always check the `success` field** before accessing `data`
2. **Use error codes** for programmatic error handling
3. **Include request IDs** in support requests
4. **Handle rate limiting** gracefully
5. **Use appropriate HTTP methods** for each operation
6. **Validate responses** against expected format

---

## **🔄 Rate Limiting**

Currently no rate limiting is enforced, but it may be added in future versions. Monitor the `meta.request_id` for tracking requests.

---

## **📝 Changelog**

### **v1.0.0**
- Initial standardized API release
- Consistent response format
- Enhanced error handling
- RESTful endpoint structure
- Comprehensive job management

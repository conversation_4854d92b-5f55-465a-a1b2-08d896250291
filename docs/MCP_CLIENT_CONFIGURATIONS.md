# MCP Client Configuration Guide

## 🔧 **The Issue You Encountered**

The error message `"Server 'string-mcp-remote' must have either a command (for stdio) or url (for SSE)"` indicates that your MCP client expects one of two configuration formats:

1. **`command`** - For local stdio transport (runs a local process)
2. **`url`** - For SSE (Server-Sent Events) transport (connects to remote server)

## 📋 **Correct Configurations by Client**

### **1. <PERSON>**

```json
{
  "mcpServers": {
    "string-mcp-remote": {
      "url": "https://mcp.rabtune.com/sse",
      "env": {
        "API_KEY": "your-actual-api-key-here"
      }
    }
  }
}
```

### **2. Cursor IDE**

```json
{
  "mcpServers": {
    "string-mcp-remote": {
      "url": "https://mcp.rabtune.com/sse",
      "env": {
        "API_KEY": "your-actual-api-key-here"
      }
    }
  }
}
```

### **3. VS Code (with MCP extension)**

```json
{
  "mcpServers": {
    "string-mcp-remote": {
      "transport": {
        "type": "sse",
        "url": "https://mcp.rabtune.com/sse"
      },
      "env": {
        "API_KEY": "your-actual-api-key-here"
      }
    }
  }
}
```

### **4. Alternative WebSocket Configuration**

For clients that support WebSocket transport:

```json
{
  "mcpServers": {
    "string-mcp-remote": {
      "url": "wss://mcp.rabtune.com/ws",
      "env": {
        "API_KEY": "your-actual-api-key-here"
      }
    }
  }
}
```

## 🚀 **Updated Server Endpoints**

Your server now supports multiple transport methods:

1. **HTTP/JSON-RPC**: `https://mcp.rabtune.com/mcp` (for testing)
2. **SSE**: `https://mcp.rabtune.com/sse` (for most MCP clients)
3. **WebSocket**: `wss://mcp.rabtune.com/ws` (alternative transport)

## 📝 **Step-by-Step Setup**

### **Step 1: Generate API Key**

```bash
curl -X POST https://mcp.rabtune.com/api/v1/connections \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "your-username",
    "name": "Your Name"
  }'
```

### **Step 2: Choose Configuration**

Use the appropriate configuration for your MCP client (see above).

### **Step 3: Update Configuration File**

- **Claude Desktop**: Update `~/.claude/claude_desktop_config.json`
- **Cursor**: Update MCP settings in Cursor preferences
- **VS Code**: Update MCP extension settings

### **Step 4: Restart Client**

Restart your IDE/client to load the new configuration.

## 🧪 **Testing the Connection**

### **Test SSE Endpoint**

```bash
curl -N -H "Accept: text/event-stream" https://mcp.rabtune.com/sse
```

You should see:
```
data: {"type": "connection", "status": "connected"}

data: {"type": "keepalive", "timestamp": "2025-06-04T03:45:00.000000"}
```

### **Test WebSocket Endpoint**

Use a WebSocket client to connect to `wss://mcp.rabtune.com/ws`

## ⚠️ **Common Issues & Solutions**

### **Issue 1: "Must have either command or url"**
- **Solution**: Use the `url` field instead of `transport` object
- **Correct**: `"url": "https://mcp.rabtune.com/sse"`
- **Incorrect**: `"transport": {"type": "http", "url": "..."}`

### **Issue 2: Connection Timeout**
- **Solution**: Ensure your server is deployed and accessible
- **Test**: Visit `https://mcp.rabtune.com/health` to verify

### **Issue 3: Authentication Errors**
- **Solution**: Generate a valid API key and include it in `env`
- **Test**: Validate your key at `/api/validate-key`

### **Issue 4: CORS Errors**
- **Solution**: Server now includes proper CORS headers
- **Note**: This should be resolved with the updated server code

## 🔄 **Migration from Old Config**

**Old (Incorrect) Format:**
```json
{
  "mcpServers": {
    "string-mcp": {
      "command": "python",
      "args": ["mcp_client.py"],
      "cwd": "D:\\_Project\\string_mcp"
    }
  }
}
```

**New (Correct) Format:**
```json
{
  "mcpServers": {
    "string-mcp-remote": {
      "url": "https://mcp.rabtune.com/sse",
      "env": {
        "API_KEY": "your-actual-api-key-here"
      }
    }
  }
}
```

## 📚 **Available Tools**

Once connected, you'll have access to these MCP tools:

1. **`create_user`** - Create a new user in the system
2. **`generate_api_key`** - Generate an API key for a user
3. **`validate_api_key_tool`** - Validate an API key and return user information

## 🎯 **Next Steps**

1. **Deploy the updated server** (includes SSE and WebSocket support)
2. **Generate your API key**
3. **Use the correct configuration format** for your MCP client
4. **Test the connection** in your IDE

---

**Status**: ✅ Server updated with SSE/WebSocket support, configurations provided for all major MCP clients

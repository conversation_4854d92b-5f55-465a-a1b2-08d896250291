# 🚀 **API Endpoint Migration Guide**

## **Overview**

The String MCP server has been updated with standardized REST API endpoints following industry best practices. This guide provides the exact changes needed for the VSCode extension and other integrations.

## **🔄 Endpoint URL Changes**

### **Health & Monitoring Endpoints**

| Old Endpoint | New Endpoint | Method | Status |
|--------------|--------------|--------|--------|
| `/health` | `/api/v1/health` | GET | ✅ Updated |
| `/status` | `/api/v1/status` | GET | ✅ Updated |
| `/mcp/info` | `/api/v1/mcp/info` | GET | ✅ Updated |

### **Connection Management Endpoints**

| Old Endpoint | New Endpoint | Method | Status |
|--------------|--------------|--------|--------|
| `/api/simple/create-connection` | `/api/v1/connections` | POST | ✅ Updated |
| `/api/simple/validate-connection` | `/api/v1/connections/validate` | POST | ✅ Updated |
| `/api/simple/regenerate-key` | `/api/v1/connections/regenerate` | POST | ✅ Updated |
| `/api/simple/connections` | `/api/v1/connections` | GET | ✅ Updated |
| `/api/simple/stats` | `/api/v1/connections/stats` | GET | ✅ Updated |

### **VSCode Integration Endpoints** ⚠️ **CRITICAL FOR EXTENSION**

| Old Endpoint | New Endpoint | Method | Status |
|--------------|--------------|--------|--------|
| `/index/chunk` | `/api/v1/vscode/chunks` | POST | ✅ Updated |
| `/index/status` | `/api/v1/vscode/status` | GET | ✅ Updated |
| `/index/cleanup` | `/api/v1/vscode/cleanup` | POST | ✅ Updated |

### **Job Management Endpoints**

| Old Endpoint | New Endpoint | Method | Status |
|--------------|--------------|--------|--------|
| `/jobs/{job_id}` | `/api/v1/jobs/{job_id}` | GET | ✅ Updated |
| `/jobs` | `/api/v1/jobs` | GET | ✅ Updated |
| `/jobs/stats` | `/api/v1/jobs/stats` | GET | ✅ Updated |

### **MCP Protocol Endpoints** (No Changes)

| Endpoint | Method | Status |
|----------|--------|--------|
| `/mcp/*` | POST | ✅ No Change |

---

## **📝 Response Format Changes**

### **New Standardized Response Format**

**Success Response:**
```json
{
  "success": true,
  "data": {
    // Actual response data here
  },
  "meta": {
    "timestamp": "2025-01-XX...",
    "version": "v1",
    "request_id": "uuid"
  }
}
```

**Error Response:**
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable message",
    "details": {
      // Additional error details
    }
  },
  "meta": {
    "timestamp": "2025-01-XX...",
    "version": "v1",
    "request_id": "uuid"
  }
}
```

### **HTTP Status Code Improvements**

- **200**: Success (GET requests)
- **201**: Created (POST requests that create resources)
- **400**: Bad Request (validation errors)
- **401**: Unauthorized (authentication required)
- **403**: Forbidden (access denied)
- **404**: Not Found (resource not found)
- **409**: Conflict (resource already exists)
- **413**: Payload Too Large (chunk size exceeded)
- **500**: Internal Server Error
- **503**: Service Unavailable (health check degraded)

---

## **🔧 VSCode Extension Migration**

### **1. Update Base URL Configuration**

```typescript
// OLD
const baseUrl = 'https://mcp.rabtune.com';

// NEW - No change to base URL, only endpoints
const baseUrl = 'https://mcp.rabtune.com';
```

### **2. Update Chunk Submission Endpoint**

```typescript
// OLD
const chunkEndpoint = '/index/chunk';

// NEW
const chunkEndpoint = '/api/v1/vscode/chunks';

// Full URL
const url = `${baseUrl}/api/v1/vscode/chunks`;
```

### **3. Update Status Check Endpoint**

```typescript
// OLD
const statusEndpoint = '/index/status';

// NEW
const statusEndpoint = '/api/v1/vscode/status';

// Full URL
const url = `${baseUrl}/api/v1/vscode/status`;
```

### **4. Update Cleanup Endpoint**

```typescript
// OLD
const cleanupEndpoint = '/index/cleanup';

// NEW
const cleanupEndpoint = '/api/v1/vscode/cleanup';

// Full URL
const url = `${baseUrl}/api/v1/vscode/cleanup`;
```

### **5. Update Job Status Endpoints**

```typescript
// OLD
const jobEndpoint = `/jobs/${jobId}`;
const jobsEndpoint = '/jobs';
const jobStatsEndpoint = '/jobs/stats';

// NEW
const jobEndpoint = `/api/v1/jobs/${jobId}`;
const jobsEndpoint = '/api/v1/jobs';
const jobStatsEndpoint = '/api/v1/jobs/stats';
```

### **6. Update Response Handling**

```typescript
// OLD - Direct response access
const response = await fetch(url);
const data = await response.json();
const chunkId = data.chunk_id;

// NEW - Access data through standardized format
const response = await fetch(url);
const result = await response.json();

if (result.success) {
  const chunkId = result.data.chunk_id;
  const processingTime = result.data.processing_time_ms;
  const jobId = result.data.job_id;
} else {
  const errorCode = result.error.code;
  const errorMessage = result.error.message;
  const errorDetails = result.error.details;
}
```

### **7. Update Error Handling**

```typescript
// OLD
if (response.status !== 200) {
  const error = await response.json();
  throw new Error(error.error || 'Unknown error');
}

// NEW - Enhanced error handling
if (!response.ok) {
  const result = await response.json();
  if (result.error) {
    throw new Error(`${result.error.code}: ${result.error.message}`);
  }
  throw new Error(`HTTP ${response.status}: ${response.statusText}`);
}
```

---

## **📋 Request/Response Examples**

### **Chunk Submission Example**

**Request:**
```http
POST /api/v1/vscode/chunks
Authorization: Bearer mcp_your_api_key_here
Content-Type: application/json

{
  "metadata": {
    "file_path": "/path/to/file.py",
    "chunk_index": 0
  },
  "content": "def hello_world():\n    print('Hello, World!')"
}
```

**Response (Success):**
```json
{
  "success": true,
  "data": {
    "chunk_id": "uuid-here",
    "processing_time_ms": 150,
    "job_id": "job-uuid-here"
  },
  "meta": {
    "timestamp": "2025-01-XX...",
    "version": "v1",
    "request_id": "request-uuid"
  }
}
```

**Response (Error):**
```json
{
  "success": false,
  "error": {
    "code": "CHUNK_TOO_LARGE",
    "message": "Chunk exceeds maximum size limit (10KB)",
    "details": {
      "content_size": 15000,
      "max_size": 10000
    }
  },
  "meta": {
    "timestamp": "2025-01-XX...",
    "version": "v1",
    "request_id": "request-uuid"
  }
}
```

### **Status Check Example**

**Request:**
```http
GET /api/v1/vscode/status
```

**Response:**
```json
{
  "success": true,
  "data": {
    "service": "vscode-mcp-integration",
    "status": "healthy",
    "chunk_processing_stats": {
      "chunks_received": 150,
      "chunks_validated": 148,
      "files_processed": 12
    },
    "endpoints": {
      "POST /api/v1/vscode/chunks": "Receive code chunks from VS Code",
      "GET /api/v1/vscode/status": "Get indexing status",
      "POST /api/v1/vscode/cleanup": "Clean temporary files"
    }
  },
  "meta": {
    "timestamp": "2025-01-XX...",
    "version": "v1",
    "request_id": "request-uuid"
  }
}
```

---

## **🔐 Authentication (No Changes)**

Authentication remains the same:
- Use `Authorization: Bearer mcp_your_api_key_here` header
- Same mcp_ prefixed API keys
- Same authentication flow

---

## **⚡ Quick Migration Checklist**

### **For VSCode Extension Team:**

- [ ] Update chunk submission URL: `/index/chunk` → `/api/v1/vscode/chunks`
- [ ] Update status check URL: `/index/status` → `/api/v1/vscode/status`
- [ ] Update cleanup URL: `/index/cleanup` → `/api/v1/vscode/cleanup`
- [ ] Update job URLs: `/jobs/*` → `/api/v1/jobs/*`
- [ ] Update response parsing to use `result.data` instead of direct access
- [ ] Update error handling to use `result.error.code` and `result.error.message`
- [ ] Test all endpoints with new format
- [ ] Update any hardcoded endpoint references in configuration

### **For Web Interface Team:**

- [ ] Update connection creation URL: `/api/simple/create-connection` → `/api/v1/connections`
- [ ] Update validation URL: `/api/simple/validate-connection` → `/api/v1/connections/validate`
- [ ] Update regeneration URL: `/api/simple/regenerate-key` → `/api/v1/connections/regenerate`
- [ ] Update listing URL: `/api/simple/connections` → `/api/v1/connections`
- [ ] Update stats URL: `/api/simple/stats` → `/api/v1/connections/stats`
- [ ] Update response parsing for new format
- [ ] Test all user flows

---

## **🚨 Breaking Changes Summary**

1. **All endpoint URLs have changed** - Update all hardcoded URLs
2. **Response format is now standardized** - Access data through `result.data`
3. **Error format is enhanced** - Use `result.error.code` for error handling
4. **HTTP status codes are more specific** - Update status code handling
5. **Some endpoints return 201 instead of 200** - Update success checks

---

## **📞 Support**

If you encounter any issues during migration:
1. Check the server logs for detailed error messages
2. Verify the new endpoint URLs are correct
3. Ensure response parsing uses the new format
4. Test with a simple curl command first

**Example curl test:**
```bash
curl -X POST https://mcp.rabtune.com/api/v1/vscode/chunks \
  -H "Authorization: Bearer mcp_your_api_key" \
  -H "Content-Type: application/json" \
  -d '{"metadata":{"file_path":"test.py","chunk_index":0},"content":"print(\"test\")"}'
```

The migration is designed to be straightforward with clear error messages to guide the process.

# MCP Server Deployment Variants

This document describes the two deployment variants available for the MCP server: **Full-Featured** and **Minimal**.

## 🏗️ **Architecture Overview**

### **Shared Codebase Strategy**
Both variants share the same codebase but use different entry points and feature sets:

```
src/
├── core/                    # Minimal MCP server
│   ├── minimal_server.py    # Lightweight implementation
│   └── tools_minimal.py     # Essential tools only
├── server.py               # Full-featured server
├── tools/                  # Complete tool suite (shared)
├── cache/                  # Caching system (full only)
├── api/                    # HTTP API endpoints (full only)
└── integrations/           # VSCode integration (full only)
```

## 🚀 **Deployment Variants**

### **1. Full-Featured Server** (Default)

**Use Cases:**
- Production deployments with web interface
- VSCode extension integration
- Multi-user environments
- High-performance caching requirements
- HTTP API access needed

**Features:**
- ✅ Complete MCP protocol support
- ✅ HTTP API endpoints for connection management
- ✅ VSCode extension integration with job tracking
- ✅ Hybrid caching system (Redis + Server)
- ✅ Multi-user authentication and isolation
- ✅ Comprehensive monitoring and health checks
- ✅ Both stdio and HTTP transports

**Resource Requirements:**
- Memory: ~100-200MB
- Dependencies: Full stack (Redis, FastAPI, etc.)
- Startup time: ~2-3 seconds

**Deployment Commands:**
```bash
# Local development
python start.py
python -m src

# Railway deployment
# Use default Procfile: web: python start.py

# Package installation
pip install -e .
mcp-server-full
```

### **2. Minimal Server** (Lightweight)

**Use Cases:**
- MCP-only clients (Claude Desktop, etc.)
- Resource-constrained environments
- Simple code analysis tasks
- Development and testing
- Embedded or edge deployments

**Features:**
- ✅ Core MCP protocol support (stdio only)
- ✅ Essential code analysis tools
- ✅ Basic health and info resources
- ❌ No HTTP API endpoints
- ❌ No VSCode integration
- ❌ No caching system
- ❌ No multi-user features

**Resource Requirements:**
- Memory: ~20-50MB
- Dependencies: Minimal (MCP SDK only)
- Startup time: <1 second

**Deployment Commands:**
```bash
# Local development
python start_minimal.py
python -m src.core.minimal_server

# Railway deployment (if needed)
# Use Procfile.minimal: web: python start_minimal.py

# Package installation
pip install -e .
mcp-server-minimal
```

## 📊 **Feature Comparison**

| Feature | Full Server | Minimal Server |
|---------|-------------|----------------|
| **MCP Protocol** | ✅ | ✅ |
| **Stdio Transport** | ✅ | ✅ |
| **HTTP Transport** | ✅ | ❌ |
| **Code Analysis Tools** | ✅ Complete | ✅ Basic |
| **Search Tools** | ✅ Advanced | ✅ Simple |
| **Reference Tracking** | ✅ | ❌ |
| **VSCode Integration** | ✅ | ❌ |
| **HTTP API Endpoints** | ✅ | ❌ |
| **Authentication** | ✅ Multi-user | ❌ |
| **Caching System** | ✅ Redis+Server | ❌ |
| **Job Tracking** | ✅ | ❌ |
| **Health Monitoring** | ✅ Comprehensive | ✅ Basic |
| **Memory Usage** | ~100-200MB | ~20-50MB |
| **Startup Time** | ~2-3s | <1s |
| **Dependencies** | Full stack | Minimal |

## 🛠️ **Shared Resources**

### **Database Handling**
- **Full Server**: Uses SQLite with connection pooling and multi-user isolation
- **Minimal Server**: No database dependencies

### **Tool System**
- **Full Server**: Complete tool suite with sandbox protection
- **Minimal Server**: Essential tools with basic functionality

### **Configuration**
Both servers support the same environment variables where applicable:
- `RAILWAY_ENVIRONMENT`: Railway deployment detection
- `PORT`: HTTP port (full server only)
- `HOST`: HTTP host (full server only)

## 🚂 **Railway Deployment**

### **Full Server Deployment**
```bash
# Use default Procfile
git push railway main
```

### **Minimal Server Deployment**
```bash
# Option 1: Use Procfile.minimal
cp Procfile.minimal Procfile
git add Procfile
git commit -m "Switch to minimal server"
git push railway main

# Option 2: Set Railway start command
railway run --command "python start_minimal.py"
```

## 🔄 **Migration Between Variants**

### **From Full to Minimal**
1. Update Procfile to use `start_minimal.py`
2. Remove Redis environment variables (optional)
3. Deploy

### **From Minimal to Full**
1. Update Procfile to use `start.py`
2. Add Redis environment variables if using caching
3. Deploy

## 🎯 **Recommendations**

### **Use Full Server When:**
- Building production applications
- Need VSCode extension support
- Require HTTP API access
- Have multiple users
- Need advanced caching
- Want comprehensive monitoring

### **Use Minimal Server When:**
- Only need MCP protocol access
- Working with Claude Desktop or similar clients
- Resource constraints are important
- Simple code analysis is sufficient
- Rapid deployment is needed
- Testing MCP functionality

## 📝 **Example Configurations**

### **Claude Desktop with Minimal Server**
```json
{
  "mcpServers": {
    "string-mcp-minimal": {
      "command": "python",
      "args": ["start_minimal.py"],
      "cwd": "/path/to/string_mcp"
    }
  }
}
```

### **VSCode Extension with Full Server**
```json
{
  "mcp.url": "https://your-railway-app.railway.app",
  "mcp.apiKey": "mcp_your_api_key_here"
}
```

Both variants maintain backward compatibility with existing MCP clients while providing flexibility for different deployment scenarios.

# Manual Webhook Testing Guide

## 🎯 Quick Test Instructions

Follow these steps to manually verify the webhook job completion system is working:

### Step 1: Generate API Key

First, create a test user and get an API key:

```bash
curl -X POST https://mcp.rabtune.com/api/v1/connections \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "webhook-test-user",
    "name": "Webhook Test User"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "api_key": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "user_id": "webhook-test-user",
  "message": "Connection created successfully"
}
```

**Save the API key** - you'll need it for the next steps.

### Step 2: Set Up Simple Webhook Server

Create a simple webhook server to receive notifications. Save this as `webhook_server.py`:

```python
#!/usr/bin/env python3
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
from datetime import datetime

class WebhookHandler(BaseHTTPRequestHandler):
    def do_POST(self):
        if self.path == '/webhook/job-complete':
            # Read the webhook payload
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            try:
                notification = json.loads(post_data.decode('utf-8'))
                
                print(f"\n🎉 WEBHOOK RECEIVED at {datetime.now()}")
                print(f"Job ID: {notification.get('job_id')}")
                print(f"Status: {notification.get('status')}")
                print(f"Success: {notification.get('success')}")
                print(f"Duration: {notification.get('duration_seconds')}s")
                
                if notification.get('success'):
                    result_data = notification.get('result_data', {})
                    print(f"Chunks processed: {result_data.get('chunks_processed')}")
                    print(f"File path: {result_data.get('file_path')}")
                    
                    vector_storage = result_data.get('vector_storage', {})
                    if vector_storage:
                        print(f"Vector storage success: {vector_storage.get('storage_success')}")
                        print(f"Collection: {vector_storage.get('collection_name')}")
                else:
                    print(f"Error: {notification.get('error_message')}")
                
                print("-" * 50)
                
                # Send success response
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(b'{"received": true}')
                
            except Exception as e:
                print(f"Error processing webhook: {e}")
                self.send_response(500)
                self.end_headers()
        else:
            self.send_response(404)
            self.end_headers()
    
    def log_message(self, format, *args):
        # Suppress default logging
        pass

if __name__ == '__main__':
    server = HTTPServer(('localhost', 8080), WebhookHandler)
    print("🚀 Webhook server started on http://localhost:8080")
    print("📥 Waiting for webhook notifications...")
    print("   Endpoint: http://localhost:8080/webhook/job-complete")
    print("   Press Ctrl+C to stop")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Webhook server stopped")
        server.shutdown()
```

**Run the webhook server:**
```bash
python webhook_server.py
```

You should see:
```
🚀 Webhook server started on http://localhost:8080
📥 Waiting for webhook notifications...
   Endpoint: http://localhost:8080/webhook/job-complete
   Press Ctrl+C to stop
```

### Step 3: Submit Chunk with Webhook URL

In a **new terminal**, submit a code chunk with your webhook URL:

```bash
curl -X POST https://mcp.rabtune.com/api/v1/vscode/chunks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY_HERE" \
  -d '{
    "metadata": {
      "file_path": "/test/webhook_test.py",
      "chunk_index": 0,
      "webhook_url": "http://localhost:8080/webhook/job-complete"
    },
    "content": "import os\nimport sys\n\ndef hello_webhook():\n    \"\"\"Test function for webhook notification.\"\"\"\n    print(\"Hello from webhook test!\")\n    return \"success\"\n\nif __name__ == \"__main__\":\n    hello_webhook()",
    "webhook_url": "http://localhost:8080/webhook/job-complete",
    "metadata": {
      "language": "python",
      "test_type": "webhook_verification"
    }
  }'
```

**Replace `YOUR_API_KEY_HERE`** with the API key from Step 1.

**Expected Response:**
```json
{
  "success": true,
  "chunk_id": "uuid-here",
  "processing_time_ms": 45,
  "job_id": "job-uuid-here"
}
```

### Step 4: Verify Webhook Notification

Within a few seconds, you should see output in your webhook server terminal:

```
🎉 WEBHOOK RECEIVED at 2024-01-15 10:30:00.123456
Job ID: job-uuid-here
Status: completed
Success: True
Duration: 2.5s
Chunks processed: 1
File path: /test/webhook_test.py
Vector storage success: True
Collection: user_webhook-test-user_code
--------------------------------------------------
```

## 🔍 Troubleshooting

### Issue 1: "Invalid connection key"
**Problem:** API key is invalid or expired.
**Solution:** Generate a new API key using Step 1.

### Issue 2: No webhook notification received
**Problem:** Webhook server not reachable or job failed.
**Solutions:**
1. Check webhook server is running on port 8080
2. Verify webhook URL in the request
3. Check job status manually:
   ```bash
   curl -H "Authorization: Bearer YOUR_API_KEY" \
     https://mcp.rabtune.com/api/v1/jobs/JOB_ID_HERE
   ```

### Issue 3: Webhook server connection refused
**Problem:** Firewall or network issue.
**Solutions:**
1. Try using a different port (e.g., 8081, 3000)
2. Update webhook URL in the chunk submission
3. Check if localhost is accessible

### Issue 4: Job fails with error
**Problem:** Vector storage or processing error.
**Solution:** Check the error message in webhook notification:
```json
{
  "success": false,
  "error_message": "Specific error details here",
  "warnings": ["Any warning messages"]
}
```

## 🧪 Advanced Testing

### Test Multiple Chunks
Submit multiple chunks for the same file to test file completion:

```bash
# Chunk 1
curl -X POST https://mcp.rabtune.com/api/v1/vscode/chunks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "metadata": {
      "file_path": "/test/multi_chunk.py",
      "chunk_index": 0,
      "webhook_url": "http://localhost:8080/webhook/job-complete"
    },
    "content": "import os\nimport sys\n\nclass TestClass:"
  }'

# Chunk 2 (final chunk to trigger processing)
curl -X POST https://mcp.rabtune.com/api/v1/vscode/chunks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "metadata": {
      "file_path": "/test/multi_chunk.py",
      "chunk_index": 1,
      "webhook_url": "http://localhost:8080/webhook/job-complete"
    },
    "content": "    def test_method(self):\n        return \"test\"",
    "webhook_url": "http://localhost:8080/webhook/job-complete"
  }'
```

### Test Error Handling
Submit invalid content to test error notifications:

```bash
curl -X POST https://mcp.rabtune.com/api/v1/vscode/chunks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "metadata": {
      "file_path": "/test/error_test.py",
      "chunk_index": 0,
      "webhook_url": "http://localhost:8080/webhook/job-complete"
    },
    "content": "",
    "webhook_url": "http://localhost:8080/webhook/job-complete"
  }'
```

## ✅ Success Criteria

The webhook system is working correctly if:

1. **API key generation** succeeds
2. **Webhook server** starts and listens on port 8080
3. **Chunk submission** returns success with job_id
4. **Webhook notification** is received within 10 seconds
5. **Notification payload** contains expected job data
6. **Vector storage** reports success in the notification

## 🎯 Next Steps

Once manual testing is successful:

1. **Integrate with your VSCode extension** using the provided TypeScript examples
2. **Implement error handling** and retry logic
3. **Add user feedback** based on webhook notifications
4. **Test with real code files** from your development workflow

The webhook system is ready for production use once these manual tests pass!

# Environment Variables Guide

## 🎯 Overview

This document explains the environment variables used in the String MCP system.

## 📋 **User Configuration (Required)**

### `API_KEY`
- **Purpose**: Your personal API key for authentication
- **Required**: Yes (for users)
- **Format**: `sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`
- **Example**: `sk-1234567890abcdef1234567890abcdef`
- **How to get**: Generate from https://mcp.rabtune.com

```json
{
  "env": {
    "API_KEY": "sk-your-actual-api-key-here"
  }
}
```

## 🔧 **Server Configuration (Optional)**

These are **only needed for server deployment**, not for users:

### `API_KEY_DB_PATH`
- **Purpose**: Path where the server stores API key database
- **Default**: `/tmp/mcp_local_auth.db` (for local client)
- **Server Default**: `/data/api_keys.db` (for Railway deployment)
- **Required**: No (has sensible defaults)
- **Example**: `/path/to/api_keys.db`

### `ENABLE_API_KEY_AUTH`
- **Purpose**: Enable/disable API key authentication
- **Default**: Automatically enabled if `API_KEY` is provided
- **Values**: `true` or `false`
- **Required**: No (auto-detected)

### `PORT`
- **Purpose**: Server port (Railway deployment)
- **Default**: `8000`
- **Required**: No (Railway sets this automatically)

### `LOG_LEVEL`
- **Purpose**: Logging verbosity
- **Default**: `INFO`
- **Values**: `DEBUG`, `INFO`, `WARNING`, `ERROR`
- **Required**: No

## 🎯 **For Users: Keep It Simple**

**Users only need ONE environment variable:**

```json
{
  "mcpServers": {
    "string-mcp": {
      "command": "uv",
      "args": ["run", "string-mcp"],
      "env": {
        "API_KEY": "sk-your-actual-api-key-here"
      }
    }
  }
}
```

That's it! Everything else is handled automatically.

## 🚀 **For Developers: Full Configuration**

If you're running the server locally for development:

```bash
export API_KEY="sk-your-dev-key"
export API_KEY_DB_PATH="/path/to/dev/database.db"
export LOG_LEVEL="DEBUG"
export PORT="8000"

uv run string-mcp
```

## ❓ **FAQ**

### Q: Do I need to set `API_KEY_DB_PATH`?
**A**: No! It has sensible defaults:
- **Users**: `/tmp/mcp_local_auth.db` (temporary, auto-cleaned)
- **Server**: `/data/api_keys.db` (persistent Railway volume)

### Q: Do I need to set `ENABLE_API_KEY_AUTH`?
**A**: No! It's automatically enabled when you provide an `API_KEY`.

### Q: What if I don't provide an `API_KEY`?
**A**: The server runs in "open mode" without authentication (for development only).

### Q: Where do I get an `API_KEY`?
**A**: Generate one from the deployed service at https://mcp.rabtune.com

## 🔒 **Security Notes**

- **Never commit API keys** to version control
- **API keys are personal** - don't share them
- **Keys start with `mcp_`** - this is the only supported format
- **Local database** (`/tmp/mcp_local_auth.db`) is temporary and safe

## 🎉 **Summary**

- **Users**: Just set `API_KEY` and you're done!
- **Developers**: All variables have sensible defaults
- **Server**: Railway handles most configuration automatically

The system is designed to be **simple for users** while being **flexible for developers**.

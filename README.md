# String MCP Server

A powerful Model Context Protocol (MCP) server for Python code analysis and indexing. This server provides AI agents with comprehensive tools to understand and navigate codebases through function discovery, call graph analysis, impact analysis, and metadata extraction.

## 🌐 **Public MCP Server**

**Live Server**: `https://mcp.rabtune.com`

Connect your MCP clients (<PERSON>, <PERSON>ursor, etc.) to our public server for instant access to powerful code analysis tools without local setup.

## 🔒 **Security & Authentication**

- **API Key Authentication**: Secure access with user-specific API keys
- **Multi-User Support**: Each user gets their own isolated access
- **Usage Tracking**: Monitor API key usage and statistics
- **Rate Limiting Ready**: Built-in support for usage controls

## 🚀 **Features**

### **Code Analysis Tools**
- **Workspace Discovery**: Automatically detect and index Python projects
- **Function Search**: Find functions by name with full context and relationships
- **Call Graph Analysis**: Understand function dependencies and call chains
- **Impact Analysis**: Analyze the impact of changing or removing code
- **Reference Tracking**: Find all references to symbols across the codebase
- **Dead Code Detection**: Identify potentially unused functions and classes
- **Class Analysis**: Discover classes, methods, and inheritance hierarchies

### **MCP Integration**
- **SSE Transport**: Server-Sent Events for real-time communication
- **JSON-RPC 2.0**: Standard MCP protocol implementation
- **Cross-Origin Support**: CORS enabled for web-based MCP clients
- **Tool Discovery**: Automatic tool registration and listing

### **🔔 Real-time Webhook Notifications** ⭐ NEW!
- **Instant Job Completion Alerts**: Get notified when code processing completes
- **Complete Job Data**: Receive full results, metrics, and vector storage info
- **VSCode Extension Ready**: Perfect for real-time extension integration
- **Reliable Delivery**: Robust error handling and timeout protection
- **Easy Integration**: Simple HTTP POST to your webhook endpoint

## 🚀 **Quick Start (Public Server)**

### **1. Get Your API Key**

Visit our web interface or use the API to create a connection and generate your mcp_ API key:

```bash
# Create connection with mcp_ API key
curl -X POST https://mcp.rabtune.com/api/v1/connections \
  -H "Content-Type: application/json" \
  -d '{"identifier": "<EMAIL>", "name": "Your Name"}'
```

This will return an `mcp_` prefixed API key that works with both MCP clients and the VSCode extension.

### **2. Configure Your MCP Client**

#### **Claude Desktop**
Add to your `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "string-mcp": {
      "url": "https://mcp.rabtune.com/sse",
      "env": {
        "API_KEY": "your-generated-api-key-here"
      }
    }
  }
}
```

#### **Cursor IDE**
Add to your MCP settings:

```json
{
  "mcpServers": {
    "string-mcp": {
      "url": "https://mcp.rabtune.com/sse",
      "env": {
        "API_KEY": "your-generated-api-key-here"
      }
    }
  }
}
```

### **3. Start Using the Tools**

Once connected, you'll have access to 13 powerful code analysis tools. Try:
- `get_workspace_info_tool()` - Check workspace detection
- `find_function_tool("function_name")` - Find specific functions
- `list_functions_tool()` - Browse all functions
- `analyze_impact_tool("symbol_name")` - Analyze code changes

## 🔧 **Available MCP Tools**

### **Workspace Tools**
- **`get_workspace_info_tool()`** - Get workspace detection and configuration info
- **`discover_workspace_projects_tool(workspace_path)`** - Discover all Python projects in workspace
- **`index_workspace_with_filters_tool(workspace_path, ignore_patterns)`** - Index workspace with custom filters

### **Indexing Tools**
- **`index_project_tool(project_path)`** - Index a specific Python project
- **`index_file_tool(file_path)`** - Index a single Python file

### **Search Tools**
- **`find_function_tool(function_name, project_path?)`** - Find specific functions by name
- **`get_function_context_tool(function_name, project_path?, include_callers?, include_callees?)`** - Get detailed function context
- **`list_functions_tool(project_path?, class_name?, limit?)`** - List all functions in codebase
- **`list_classes_tool(project_path?, limit?)`** - List all classes in codebase

### **Analysis Tools**
- **`analyze_impact_tool(symbol_name, project_path?)`** - Analyze impact of changing/removing a symbol
- **`analyze_call_graph_tool(function_name?, project_path?, max_depth?)`** - Analyze function call relationships
- **`analyze_dead_code_tool(project_path?)`** - Find potentially unused/dead code

### **Reference Tools**
- **`find_all_references_tool(symbol_name, project_path?, include_definitions?)`** - Find all references to a symbol

## 🏃 **Local Development**

### **Run Server Locally**

```bash
# Install dependencies
uv install

# Run the server
python -m src.server
```

### **Environment Variables**

```bash
# API Key Authentication
ENABLE_API_KEY_AUTH=true
API_KEY_DB_PATH=/data/api_keys.db

# Server Configuration
PORT=8080
LOG_LEVEL=INFO
```

## 📊 **API Endpoints (Standardized REST API v1)**

### **Health & Monitoring**
- `GET /api/v1/health` - Health check for monitoring
- `GET /api/v1/status` - Comprehensive server status
- `GET /api/v1/mcp/info` - MCP server information

### **Connection Management (mcp_ keys only)**
- `POST /api/v1/connections` - Create connection with mcp_ API key
- `POST /api/v1/connections/validate` - Validate mcp_ API key
- `POST /api/v1/connections/regenerate` - Regenerate mcp_ API key
- `GET /api/v1/connections` - List all connections
- `GET /api/v1/connections/stats` - Get connection statistics

### **VSCode Integration**
- `POST /api/v1/vscode/chunks` - Receive code chunks from VSCode extension
- `GET /api/v1/vscode/status` - Get indexing status
- `POST /api/v1/vscode/cleanup` - Cleanup temporary files

### **Job Management**
- `GET /api/v1/jobs` - List user jobs
- `GET /api/v1/jobs/{id}` - Get specific job status
- `GET /api/v1/jobs/stats` - Get job statistics

> **📋 Migration Note:** All API endpoints have been standardized to follow REST best practices. See [API Migration Guide](docs/API_MIGRATION_GUIDE.md) for detailed migration instructions and [API Reference v1](docs/API_REFERENCE_V1.md) for complete documentation.

See [API_ENDPOINTS_FOR_FRONTEND.md](API_ENDPOINTS_FOR_FRONTEND.md) for complete API documentation.

## 🧪 **Testing**

```bash
# Test the complete MCP system (creates key + tests VSCode integration)
python scripts/test_mcp_system.py <EMAIL> "User Name"

# Test simple auth endpoints
python scripts/test_simple_auth_endpoints.py

# Test production server
python scripts/test_real_mcp_tools.py
```

## 🚀 **Deployment**

### **Railway Deployment**
The server is configured for Railway deployment with:
- SQLite database with volume mounting
- Environment variable configuration
- Health check endpoints
- CORS support

### **Docker Support**
```bash
# Build and run locally
docker build -f Dockerfile.railway -t string-mcp .
docker run -p 8080:8080 string-mcp
```

## 📚 **Documentation**

- [API Endpoints](API_ENDPOINTS_FOR_FRONTEND.md) - Complete API reference
- [MCP Client Configuration](MCP_CLIENT_CONFIGURATIONS.md) - Client setup guide
- [Connection Troubleshooting](MCP_CONNECTION_DIAGNOSIS_AND_FIXES.md) - Fix connection issues
- **[Webhook Integration Guide](docs/VSCODE_EXTENSION_WEBHOOK_INTEGRATION.md)** - VSCode extension webhook setup ⭐ NEW!
- **[Webhook Quick Reference](docs/WEBHOOK_QUICK_REFERENCE.md)** - Quick webhook implementation guide ⭐ NEW!

## 🔒 **Security & Privacy**

- **API Key Authentication**: Secure multi-user access
- **Usage Tracking**: Monitor and control API usage
- **CORS Protection**: Controlled cross-origin access
- **No Code Storage**: Server provides analysis tools only, stores no user code

## 📄 **License**

MIT License - see [LICENSE](LICENSE) for details.
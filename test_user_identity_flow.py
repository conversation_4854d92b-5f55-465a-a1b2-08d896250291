#!/usr/bin/env python3
"""
Test Script: User Identity Flow Verification

This script tests the complete API key → user identity → data isolation flow
to ensure perfect binding across all components.

Tests:
1. API key validation and user ID extraction
2. Vector store collection naming consistency
3. Redis cache key user isolation
4. Authentication flow without fallbacks
5. Cross-user data isolation verification
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_api_key_extraction():
    """Test centralized API key → user ID extraction."""
    print("\n🔑 Testing API Key → User ID Extraction")
    print("=" * 50)
    
    from src.auth import extract_user_id_from_api_key
    
    # Test cases - only mcp_ keys are supported now
    test_cases = [
        ("mcp_1234567890abcdef", "Valid mcp_ key (not in DB)"),
        ("sk-1234567890abcdef", "Invalid sk- key (no longer supported)"),
        ("invalid_key", "Invalid key format"),
        ("", "Empty key"),
        (None, "None key"),
        ("short", "Too short key"),
        ("mcp_invalid", "Invalid mcp_ key"),
    ]
    
    for api_key, description in test_cases:
        try:
            user_id = extract_user_id_from_api_key(api_key)
            status = "✅ PASS" if user_id else "❌ FAIL"
            print(f"{status} {description}: {api_key} → {user_id}")
        except Exception as e:
            print(f"❌ ERROR {description}: {e}")


async def test_vector_collection_naming():
    """Test vector store collection naming for collision resistance."""
    print("\n🗂️  Testing Vector Collection Naming")
    print("=" * 50)

    from src.vector.openai_qdrant_client import OpenAIQdrantClient

    # Create a minimal client just to test collection naming
    class MockClient:
        def _get_collection_name(self, user_id: str) -> str:
            """Copy the exact method from OpenAIQdrantClient for testing."""
            import hashlib

            # Generate deterministic hash for collision resistance
            user_hash = hashlib.sha256(user_id.encode()).hexdigest()[:12]

            # Sanitize user_id for readability (alphanumeric only)
            sanitized_id = "".join(c for c in user_id if c.isalnum())[:20]

            # Combine sanitized ID with hash for uniqueness
            if sanitized_id:
                return f"user_{sanitized_id}_{user_hash}_code"
            else:
                # Fallback for non-alphanumeric user IDs
                return f"user_{user_hash}_code"

    client = MockClient()
    
    # Test collision-prone user IDs
    test_user_ids = [
        "<EMAIL>",
        "user_domain_com",
        "user.domain.com",
        "user-domain-com",
        "user123",
        "user_123",
        "user@123",
        "<EMAIL>",
        "user_with_special_chars!@#$%",
        "简体中文用户",  # Chinese characters
    ]
    
    collection_names = set()
    for user_id in test_user_ids:
        collection_name = client._get_collection_name(user_id)
        print(f"User: {user_id[:30]:<30} → Collection: {collection_name}")
        
        # Check for collisions
        if collection_name in collection_names:
            print(f"❌ COLLISION DETECTED: {collection_name}")
        else:
            collection_names.add(collection_name)
    
    print(f"\n✅ Generated {len(collection_names)} unique collections from {len(test_user_ids)} users")


async def test_redis_key_isolation():
    """Test Redis cache key user isolation."""
    print("\n🔄 Testing Redis Key User Isolation")
    print("=" * 50)
    
    from src.cache.smart_redis_manager import SmartRedisManager
    
    manager = SmartRedisManager()
    
    # Test user isolation in different key types
    test_cases = [
        ("user1", "session", "Session key"),
        ("user2", "session", "Session key"),
        ("user1", "job123", "Job key"),
        ("user2", "job123", "Job key"),
        ("user1", "upload", "Rate limit key"),
        ("user2", "upload", "Rate limit key"),
        ("user1", "content_hash_123", "Dedup key"),
        ("user2", "content_hash_123", "Dedup key"),
    ]
    
    keys_generated = set()
    for user_id, identifier, description in test_cases:
        session_key = manager._get_user_key(manager.SESSION_PREFIX, user_id, identifier)
        job_key = manager._get_user_key(manager.JOB_PREFIX, user_id, identifier)
        rate_key = manager._get_user_key(manager.RATE_PREFIX, user_id, identifier)
        dedup_key = manager._get_user_key(manager.DEDUP_PREFIX, user_id, identifier)
        
        print(f"User: {user_id}, ID: {identifier}")
        print(f"  Session: {session_key}")
        print(f"  Job:     {job_key}")
        print(f"  Rate:    {rate_key}")
        print(f"  Dedup:   {dedup_key}")
        
        # Check for collisions
        for key in [session_key, job_key, rate_key, dedup_key]:
            if key in keys_generated:
                print(f"❌ KEY COLLISION: {key}")
            else:
                keys_generated.add(key)
        print()
    
    print(f"✅ Generated {len(keys_generated)} unique keys with proper user isolation")


async def test_authentication_security():
    """Test authentication security - no fallbacks allowed."""
    print("\n🔒 Testing Authentication Security")
    print("=" * 50)
    
    from src.auth import extract_user_id_from_api_key
    
    # These should all fail (no fallback authentication, only mcp_ keys supported)
    dangerous_cases = [
        "any_random_string_longer_than_10_chars",
        "fake_api_key_12345",
        "bearer_token_example",
        "unauthorized_access_attempt",
        "1234567890123456789",  # Just numbers
        "sk-1234567890abcdef",  # sk- keys no longer supported
        "api_key_12345",
        "token_abcdef",
    ]
    
    all_rejected = True
    for dangerous_key in dangerous_cases:
        user_id = extract_user_id_from_api_key(dangerous_key)
        if user_id:
            print(f"❌ SECURITY BREACH: {dangerous_key} → {user_id}")
            all_rejected = False
        else:
            print(f"✅ REJECTED: {dangerous_key}")
    
    if all_rejected:
        print("\n✅ All unauthorized keys properly rejected - no fallback authentication")
    else:
        print("\n❌ SECURITY VULNERABILITY: Some unauthorized keys were accepted")


async def test_complete_flow_simulation():
    """Simulate complete VSCode → Server → Vector Store flow."""
    print("\n🔄 Testing Complete Flow Simulation")
    print("=" * 50)
    
    # Simulate VSCode extension request
    print("1. VSCode Extension Request Simulation")
    
    # Create a test user first
    from src.auth import get_simple_auth_manager
    auth_manager = get_simple_auth_manager()
    
    # Create test user with unique identifier to avoid conflicts
    import time
    unique_identifier = f"test-{int(time.time())}-{hash('test') % 10000}@example.com"
    test_user = auth_manager.create_user_with_key(unique_identifier, "Test User")

    if test_user:
        print(f"✅ Created test user: {test_user.id} with key: {test_user.api_key}")
    else:
        # If creation failed, try to get existing user for testing
        print("⚠️  User creation failed, checking for existing test user...")
        existing_user = auth_manager.get_user_by_identifier("<EMAIL>")
        if existing_user:
            test_user = existing_user
            print(f"✅ Using existing test user: {test_user.id} with key: {test_user.api_key}")
        else:
            print("❌ No test user available")
            return

    # Test the complete flow continues here
    print(f"✅ Proceeding with user: {test_user.id}")

    # Test the complete flow
    from src.auth import extract_user_id_from_api_key
    from src.cache.smart_redis_manager import SmartRedisManager

    # 1. Extract user ID from API key
    user_id = extract_user_id_from_api_key(test_user.api_key)
    print(f"✅ Extracted user ID: {user_id}")

    # 2. Generate vector collection name
    class MockClient:
        def _get_collection_name(self, user_id: str) -> str:
            """Copy the exact method from OpenAIQdrantClient for testing."""
            import hashlib

            # Generate deterministic hash for collision resistance
            user_hash = hashlib.sha256(user_id.encode()).hexdigest()[:12]

            # Sanitize user_id for readability (alphanumeric only)
            sanitized_id = "".join(c for c in user_id if c.isalnum())[:20]

            # Combine sanitized ID with hash for uniqueness
            if sanitized_id:
                return f"user_{sanitized_id}_{user_hash}_code"
            else:
                # Fallback for non-alphanumeric user IDs
                return f"user_{user_hash}_code"

    vector_client = MockClient()
    collection_name = vector_client._get_collection_name(user_id)
    print(f"✅ Vector collection: {collection_name}")

    # 3. Generate Redis cache keys
    redis_manager = SmartRedisManager()
    session_key = redis_manager._get_user_key(redis_manager.SESSION_PREFIX, user_id, "session")
    job_key = redis_manager._get_user_key(redis_manager.JOB_PREFIX, user_id, "chunk_job_123")
    dedup_key = redis_manager._get_user_key(redis_manager.DEDUP_PREFIX, user_id, "content_hash_abc")

    print(f"✅ Redis session key: {session_key}")
    print(f"✅ Redis job key: {job_key}")
    print(f"✅ Redis dedup key: {dedup_key}")

    print("\n✅ Complete flow verified - user identity properly bound across all components")


async def main():
    """Run all tests."""
    print("🧪 User Identity Flow Verification Tests")
    print("=" * 60)
    
    try:
        await test_api_key_extraction()
        await test_vector_collection_naming()
        await test_redis_key_isolation()
        await test_authentication_security()
        await test_complete_flow_simulation()
        
        print("\n" + "=" * 60)
        print("🎉 All tests completed!")
        print("✅ User identity flow verification successful")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())

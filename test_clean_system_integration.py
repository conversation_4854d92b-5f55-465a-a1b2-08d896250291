#!/usr/bin/env python3
"""
🚀 Clean System Integration Test

Test the new clean, straightforward hybrid cache system!
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))


async def test_clean_system():
    """Test the clean, straightforward system."""
    print("🧪 Testing Clean & Straightforward System")
    print("=" * 50)
    
    try:
        # Test 1: Import the clean cache system
        print("📦 Testing Clean Imports...")
        from cache import (
            get_hybrid_cache,
            initialize_cache_system,
            HybridCacheSystem,
            CacheResult,
            JobStatus
        )
        print("  ✅ Clean imports successful!")
        
        # Test 2: Initialize the system
        print("\n🚀 Testing System Initialization...")
        cache_ready = await initialize_cache_system()
        print(f"  ✅ Cache system initialized: {cache_ready}")
        
        # Test 3: Get the hybrid cache
        print("\n💾 Testing Hybrid Cache...")
        cache = await get_hybrid_cache()
        print(f"  ✅ Hybrid cache ready: {cache.initialized}")
        
        # Test 4: Health check
        print("\n🏥 Testing Health Check...")
        health = await cache.health_check()
        print(f"  ✅ System health: {health['hybrid_system']}")
        print(f"  📡 Redis coordination: {health['redis_coordination']}")
        print(f"  💾 Server cache: {health['server_cache']}")
        
        # Test 5: Simple operations
        print("\n⚡ Testing Simple Operations...")
        
        # Track a user session
        session_success = await cache.track_user_session("test_user", {
            'active_jobs': 1,
            'connections': 1
        })
        print(f"  ✅ User session tracked: {session_success}")
        
        # Start a job
        job_id = await cache.start_job("test_user", "test_job", {
            'file_path': '/test/file.py',
            'chunk_count': 5
        })
        print(f"  ✅ Job started: {job_id}")
        
        # Cache an embedding
        test_embedding = [0.1] * 1536  # Fake embedding
        embed_result = await cache.cache_embedding(
            "def hello(): print('world')",
            test_embedding,
            "test_user",
            {'language': 'python'}
        )
        print(f"  ✅ Embedding cached: {embed_result.success} (source: {embed_result.source})")
        
        # Retrieve the embedding
        retrieve_result = await cache.get_cached_embedding("def hello(): print('world')")
        print(f"  ✅ Embedding retrieved: {retrieve_result.success} (source: {retrieve_result.source})")
        
        # Test 6: Performance stats
        print("\n📊 Testing Performance Stats...")
        stats = await cache.get_comprehensive_stats()
        print(f"  📈 Redis operations: {stats['redis_operations']}")
        print(f"  💾 Server operations: {stats['server_operations']}")
        print(f"  🔄 Hybrid operations: {stats['hybrid_operations']}")
        print(f"  ⚠️ Fallback operations: {stats['fallback_operations']}")
        
        performance = stats['hybrid_performance']
        print(f"  ⚡ Average operation time: {performance['avg_operation_time_ms']:.1f}ms")
        
        print("\n🎉 All Tests Passed!")
        print("✨ Clean & Straightforward System Working Perfectly!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_vscode_integration():
    """Test VSCode integration with the clean system."""
    print("\n🔧 Testing VSCode Integration")
    print("=" * 50)
    
    try:
        # Test VSCode integration imports
        from integrations.vscode import VSCodeIntegration
        from string_mcp.server import FastMCP
        
        # Create a mock server
        mcp_server = FastMCP("test")
        
        # Create VSCode integration
        vscode = VSCodeIntegration(mcp_server)
        
        # Initialize hybrid cache
        await vscode.initialize_hybrid_cache()
        
        print(f"  ✅ VSCode integration initialized: {vscode.cache_initialized}")
        
        if vscode.cache_initialized:
            print("  🚀 Hybrid cache working with VSCode!")
        else:
            print("  ⚠️ VSCode using fallback mode")
        
        return True
        
    except Exception as e:
        print(f"  ❌ VSCode integration test failed: {e}")
        return False


async def main():
    """Run all clean system tests."""
    print("🧪 Clean & Straightforward System Integration Test")
    print("🎯 Testing: Redis = Traffic Cop, Server = Heavy Lifter")
    print("=" * 60)
    
    # Test the clean system
    system_ok = await test_clean_system()
    
    # Test VSCode integration
    vscode_ok = await test_vscode_integration()
    
    print("\n" + "=" * 60)
    if system_ok and vscode_ok:
        print("🎉 ALL TESTS PASSED!")
        print("✨ Clean & Straightforward System Ready for Production!")
        print("\n💡 Benefits:")
        print("  ✅ Redis handles lightweight coordination")
        print("  ✅ Server handles heavy data storage")
        print("  ✅ Automatic fallback if Redis fails")
        print("  ✅ No more 25MB memory pressure!")
        print("  ✅ Excellent performance & monitoring")
        print("\n🚀 Your system is now CLEAN, STRAIGHTFORWARD & POWERFUL!")
    else:
        print("❌ Some tests failed - check the output above")
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    if not success:
        sys.exit(1)

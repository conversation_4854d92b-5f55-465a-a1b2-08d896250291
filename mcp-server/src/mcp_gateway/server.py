"""
MCP Gateway Server - Lightweight MCP protocol gateway.

Acts as a bridge between MCP clients and the main String MCP server,
handling MCP protocol translation and forwarding requests via HTTP API.
"""

import asyncio
import logging
import os
import sys
from typing import Any, Dict, List, Optional
from contextlib import asynccontextmanager

from mcp.server.fastmcp import FastMCP, Context
import structlog

from .config import get_config
from .api_client import MainServerAPIClient, APIError
from .tools import register_gateway_tools
from .resources import register_gateway_resources

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


@asynccontextmanager
async def gateway_lifespan(mcp_server):
    """Lifespan management for MCP Gateway."""
    config = get_config()
    
    logger.info("Starting MCP Gateway", version="1.0.0", main_server=config.main_server_url)
    
    # Initialize API client
    api_client = MainServerAPIClient(config)
    
    # Store in server context for tools to access
    mcp_server.gateway_config = config
    mcp_server.api_client = api_client
    
    try:
        # Test connection to main server
        async with api_client:
            # Simple health check
            try:
                await api_client._make_request("GET", "/api/v1/health")
                logger.info("Successfully connected to main server")
            except Exception as e:
                logger.warning("Could not connect to main server", error=str(e))
        
        yield
        
    finally:
        logger.info("Shutting down MCP Gateway")


def create_gateway_server() -> FastMCP:
    """Create and configure the MCP Gateway server."""
    config = get_config()
    
    # Create minimal FastMCP server for stdio transport
    mcp = FastMCP(
        config.gateway_name,
        lifespan=gateway_lifespan,
        # Minimal configuration for stdio-only operation
        stateless_http=False,  # No HTTP support needed
        json_response=False    # Use standard MCP responses
    )
    
    # Register tools and resources
    register_gateway_tools(mcp)
    register_gateway_resources(mcp)
    
    logger.info("MCP Gateway server created", name=config.gateway_name)
    return mcp


async def extract_api_key_from_context(ctx: Context) -> Optional[str]:
    """Extract API key from MCP context or environment."""
    # Try to get API key from context metadata
    if hasattr(ctx, 'meta') and ctx.meta:
        api_key = ctx.meta.get('api_key') or ctx.meta.get('authorization')
        if api_key:
            return api_key
    
    # Fallback to environment variable
    api_key = os.getenv('MCP_API_KEY') or os.getenv('API_KEY')
    if api_key:
        return api_key
    
    # No API key found
    logger.warning("No API key found in context or environment")
    return None


async def validate_user_context(ctx: Context) -> Optional[Dict[str, Any]]:
    """Validate user context and return user info."""
    api_key = await extract_api_key_from_context(ctx)
    if not api_key:
        return None
    
    # Get API client from server context
    if not hasattr(ctx.server, 'api_client'):
        logger.error("API client not available in server context")
        return None
    
    api_client = ctx.server.api_client
    
    try:
        user_info = await api_client.validate_api_key(api_key)
        if user_info:
            logger.info("User validated", user_id=user_info.get('id'))
            return user_info
        else:
            logger.warning("Invalid API key")
            return None
    except Exception as e:
        logger.error("Error validating user", error=str(e))
        return None


def main():
    """Main entry point for MCP Gateway."""
    # Set up logging
    config = get_config()
    logging.basicConfig(
        level=getattr(logging, config.log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create and run server
    mcp = create_gateway_server()
    
    try:
        logger.info("Starting MCP Gateway with stdio transport")
        # Always use stdio transport for MCP clients
        mcp.run()
    except KeyboardInterrupt:
        logger.info("Gateway shutdown requested")
    except Exception as e:
        logger.error("Gateway startup failed", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    main()

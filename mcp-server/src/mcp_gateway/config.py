"""
Configuration management for MCP Gateway.
"""

import os
from typing import Optional
from pydantic import BaseSettings, Field


class GatewayConfig(BaseSettings):
    """Configuration for MCP Gateway."""
    
    # Main server connection
    main_server_url: str = Field(
        default="https://mcp.rabtune.com",
        description="URL of the main String MCP server"
    )
    
    # Gateway settings
    gateway_name: str = Field(
        default="String MCP Gateway",
        description="Name of the MCP gateway server"
    )
    
    # Timeouts and retries
    request_timeout: int = Field(
        default=30,
        description="Timeout for requests to main server (seconds)"
    )
    
    max_retries: int = Field(
        default=3,
        description="Maximum number of retries for failed requests"
    )
    
    retry_delay: float = Field(
        default=1.0,
        description="Base delay between retries (seconds)"
    )
    
    # Logging
    log_level: str = Field(
        default="INFO",
        description="Logging level"
    )
    
    # Development settings
    debug: bool = Field(
        default=False,
        description="Enable debug mode"
    )
    
    class Config:
        env_prefix = "MCP_GATEWAY_"
        env_file = ".env"


def get_config() -> GatewayConfig:
    """Get gateway configuration."""
    return GatewayConfig()

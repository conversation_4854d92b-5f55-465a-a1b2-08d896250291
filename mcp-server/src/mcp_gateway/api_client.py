"""
HTTP API client for communicating with the main String MCP server.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Union
import httpx
from tenacity import retry, stop_after_attempt, wait_exponential
import orjson

from .config import GatewayConfig

logger = logging.getLogger(__name__)


class MainServerAPIClient:
    """HTTP client for communicating with the main String MCP server."""
    
    def __init__(self, config: GatewayConfig):
        self.config = config
        self.base_url = config.main_server_url.rstrip('/')
        self.timeout = httpx.Timeout(config.request_timeout)
        
        # Create persistent HTTP client
        self.client = httpx.AsyncClient(
            timeout=self.timeout,
            headers={
                "User-Agent": "MCP-Gateway/1.0.0",
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
        )
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10)
    )
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        api_key: Optional[str] = None
    ) -> Dict[str, Any]:
        """Make HTTP request to main server with retry logic."""
        
        url = f"{self.base_url}{endpoint}"
        request_headers = {}
        
        if headers:
            request_headers.update(headers)
        
        if api_key:
            request_headers["Authorization"] = f"Bearer {api_key}"
        
        try:
            if method.upper() == "GET":
                response = await self.client.get(url, headers=request_headers)
            elif method.upper() == "POST":
                json_data = orjson.dumps(data) if data else None
                response = await self.client.post(
                    url, 
                    content=json_data,
                    headers=request_headers
                )
            elif method.upper() == "PUT":
                json_data = orjson.dumps(data) if data else None
                response = await self.client.put(
                    url,
                    content=json_data,
                    headers=request_headers
                )
            elif method.upper() == "DELETE":
                response = await self.client.delete(url, headers=request_headers)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            
            # Parse JSON response
            if response.content:
                return orjson.loads(response.content)
            else:
                return {}
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error {e.response.status_code} for {method} {url}: {e}")
            # Try to parse error response
            try:
                error_data = orjson.loads(e.response.content)
                raise APIError(
                    f"Server error: {error_data.get('message', 'Unknown error')}",
                    status_code=e.response.status_code,
                    error_data=error_data
                )
            except orjson.JSONDecodeError:
                raise APIError(
                    f"HTTP {e.response.status_code}: {e.response.text}",
                    status_code=e.response.status_code
                )
        except httpx.RequestError as e:
            logger.error(f"Request error for {method} {url}: {e}")
            raise APIError(f"Connection error: {e}")
    
    # Authentication endpoints
    async def validate_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """Validate API key with main server."""
        try:
            response = await self._make_request(
                "POST",
                "/api/v1/connections/validate",
                data={"api_key": api_key}
            )
            return response.get("user")
        except APIError:
            return None
    
    # Tool execution endpoints
    async def execute_tool(
        self,
        tool_name: str,
        arguments: Dict[str, Any],
        api_key: str
    ) -> Dict[str, Any]:
        """Execute a tool on the main server."""
        return await self._make_request(
            "POST",
            f"/api/v1/tools/{tool_name}/execute",
            data={"arguments": arguments},
            api_key=api_key
        )
    
    # Resource endpoints
    async def get_resource(
        self,
        resource_uri: str,
        api_key: str
    ) -> Dict[str, Any]:
        """Get a resource from the main server."""
        return await self._make_request(
            "GET",
            f"/api/v1/resources?uri={resource_uri}",
            api_key=api_key
        )
    
    # Vector store endpoints
    async def search_code(
        self,
        query: str,
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None,
        api_key: str = None
    ) -> List[Dict[str, Any]]:
        """Search code in vector store."""
        data = {
            "query": query,
            "limit": limit
        }
        if filters:
            data["filters"] = filters
            
        response = await self._make_request(
            "POST",
            "/api/v1/vector/search",
            data=data,
            api_key=api_key
        )
        return response.get("results", [])
    
    # VSCode integration endpoints
    async def process_vscode_chunks(
        self,
        chunks: List[Dict[str, Any]],
        api_key: str
    ) -> Dict[str, Any]:
        """Process VSCode file chunks."""
        return await self._make_request(
            "POST",
            "/api/v1/vscode/process-chunks",
            data={"chunks": chunks},
            api_key=api_key
        )
    
    # Job management endpoints
    async def get_job_status(
        self,
        job_id: str,
        api_key: str
    ) -> Dict[str, Any]:
        """Get job status from main server."""
        return await self._make_request(
            "GET",
            f"/api/v1/jobs/{job_id}",
            api_key=api_key
        )


class APIError(Exception):
    """Exception raised for API errors."""
    
    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        error_data: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.status_code = status_code
        self.error_data = error_data or {}

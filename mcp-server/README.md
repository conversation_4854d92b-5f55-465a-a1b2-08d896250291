# MCP Gateway Server

A lightweight MCP protocol gateway that acts as a bridge between MCP clients and the main String MCP server.

## Features

- **Pure MCP Protocol**: stdio transport for MCP clients (<PERSON>, VSCode, etc.)
- **Lightweight**: Minimal dependencies and fast startup
- **Proxy Architecture**: Forwards all requests to main server via HTTP API
- **Authentication**: Handles API key validation through main server
- **Error Handling**: Robust error handling and retry logic

## Architecture

```
MCP Client → MCP Gateway (stdio) → Main Server (HTTP API) → Data Layer
```

## Installation

```bash
cd mcp-server
uv install
```

## Usage

### Local Development (stdio)
```bash
uv run mcp-gateway
```

### Configuration

Set environment variables:
```bash
export MCP_GATEWAY_MAIN_SERVER_URL="https://mcp.rabtune.com"
export MCP_API_KEY="mcp_your_api_key_here"
```

## MCP Client Configuration

### <PERSON>
```json
{
  "mcpServers": {
    "string-mcp": {
      "command": "uv",
      "args": ["run", "mcp-gateway"],
      "cwd": "/path/to/mcp-server",
      "env": {
        "MCP_API_KEY": "mcp_your_api_key_here"
      }
    }
  }
}
```

### Cursor IDE
```json
{
  "mcp": {
    "servers": [
      {
        "name": "string-mcp",
        "command": ["uv", "run", "mcp-gateway"],
        "cwd": "/path/to/mcp-server",
        "env": {
          "MCP_API_KEY": "mcp_your_api_key_here"
        }
      }
    ]
  }
}
```

## Available Tools

All tools from the main String MCP server are available through the gateway:

- **Search Tools**: `find_function_tool`, `find_class_tool`, `list_functions_tool`, `search_code`
- **Analysis Tools**: `analyze_code_structure`, `build_call_graph`, `analyze_dead_code`, `analyze_impact`
- **Reference Tools**: `find_all_references_tool`
- **Job Management**: `get_job_status`
- **VSCode Integration**: `process_vscode_chunks`

## Deployment

### Remote Deployment (Recommended)

The MCP Gateway is designed to be deployed as a remote service, providing global access to the String MCP system.

#### Railway Deployment
```bash
# Deploy MCP Gateway to Railway
cd mcp-server
railway login
railway link your-mcp-gateway-project
railway up
```

#### Docker Deployment
```bash
# Build and deploy with Docker
docker build -t string-mcp-gateway .
docker run -p 8080:8080 string-mcp-gateway
```

### Local Development
For development and testing:
```bash
# Run locally
uv run mcp-gateway
```

## Development

```bash
# Install dependencies
uv install

# Run tests
uv run pytest

# Format code
uv run black .
uv run isort .

# Type checking
uv run mypy .
```

## Configuration Options

| Environment Variable | Default | Description |
|---------------------|---------|-------------|
| `MCP_GATEWAY_MAIN_SERVER_URL` | `https://mcp.rabtune.com` | Main server URL |
| `MCP_GATEWAY_REQUEST_TIMEOUT` | `30` | Request timeout (seconds) |
| `MCP_GATEWAY_MAX_RETRIES` | `3` | Maximum retry attempts |
| `MCP_GATEWAY_LOG_LEVEL` | `INFO` | Logging level |
| `MCP_API_KEY` | - | API key for authentication |

## Error Handling

The gateway provides robust error handling:
- Automatic retries with exponential backoff
- Graceful degradation when main server is unavailable
- Detailed error messages for debugging
- Connection pooling for optimal performance

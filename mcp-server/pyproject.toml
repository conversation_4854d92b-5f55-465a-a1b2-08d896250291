[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "string-mcp-gateway"
version = "1.0.0"
description = "Lightweight MCP protocol gateway for String MCP system"
readme = "README.md"
requires-python = ">=3.11"
license = {text = "MIT"}
authors = [
    {name = "String MCP Team", email = "<EMAIL>"},
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Code Generators",
]

dependencies = [
    # Core MCP - minimal dependencies
    "mcp>=1.9.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    # HTTP client for API calls
    "httpx>=0.25.0",
    "aiohttp>=3.12.7",
    # Configuration and utilities
    "python-dotenv>=1.0.0",
    "orjson>=3.9.0",
    "typing-extensions>=4.8.0",
    # Async support
    "async-timeout>=4.0.3",
    # Logging
    "structlog>=23.2.0",
    # Error handling
    "tenacity>=8.2.0",
]

[project.scripts]
mcp-gateway = "src.mcp_gateway.server:main"
mcp-server = "src.mcp_gateway.server:main"

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.12.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "mypy>=1.7.0",
]

[project.urls]
Homepage = "https://github.com/string-mcp/mcp-gateway"
Repository = "https://github.com/string-mcp/mcp-gateway"

[tool.uv]
dev-dependencies = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.12.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "mypy>=1.7.0",
]

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
check_untyped_defs = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = ["--strict-markers", "--strict-config"]
testpaths = ["tests"]
asyncio_mode = "auto"

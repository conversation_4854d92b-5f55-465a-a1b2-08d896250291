#!/usr/bin/env python3
"""
Endpoint Validation Test

Tests the actual HTTP endpoints to ensure they work correctly
with the new standardized format.
"""

import asyncio
import json
import logging
import os
import subprocess
import sys
import time
import requests
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")
logger = logging.getLogger(__name__)


def start_test_server():
    """Start the MCP server for testing."""
    print("🚀 Starting test server...")
    
    # Set environment variables for testing
    env = os.environ.copy()
    env['PORT'] = '8005'  # Use different port to avoid conflicts
    env['HOST'] = '127.0.0.1'
    env['LOG_LEVEL'] = 'WARNING'  # Reduce log noise
    
    # Start server process
    cmd = [sys.executable, '-m', 'src.server']
    process = subprocess.Popen(
        cmd,
        env=env,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        cwd=Path(__file__).parent
    )
    
    # Wait for server to start
    print("⏳ Waiting for server to start...")
    time.sleep(5)
    
    # Check if server is running
    try:
        response = requests.get('http://127.0.0.1:8005/api/v1/health', timeout=5)
        if response.status_code in [200, 503]:  # 503 is acceptable for degraded health
            print("✅ Test server started successfully")
            return process
        else:
            print(f"❌ Server health check failed: {response.status_code}")
            process.terminate()
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ Server not responding: {e}")
        process.terminate()
        return None


def test_health_endpoints():
    """Test health and monitoring endpoints."""
    print("\n🏥 Testing Health & Monitoring Endpoints...")
    
    base_url = "http://127.0.0.1:8005"
    
    endpoints = [
        ("/api/v1/health", "Health check"),
        ("/api/v1/status", "Status check"),
        ("/api/v1/mcp/info", "MCP info"),
    ]
    
    results = []
    
    for endpoint, description in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            data = response.json()
            
            # Check response format
            if "success" in data and "data" in data and "meta" in data:
                print(f"✅ {description}: Correct format")
                results.append(True)
            else:
                print(f"❌ {description}: Incorrect format - {data}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ {description}: Failed - {e}")
            results.append(False)
    
    return all(results)


def test_connection_endpoints():
    """Test connection management endpoints."""
    print("\n🔗 Testing Connection Management Endpoints...")
    
    base_url = "http://127.0.0.1:8005"
    
    # Test connection creation
    try:
        create_data = {
            "identifier": "<EMAIL>",
            "name": "Endpoint Test User"
        }
        
        response = requests.post(
            f"{base_url}/api/v1/connections",
            json=create_data,
            timeout=10
        )
        
        if response.status_code == 201:
            data = response.json()
            if (data.get("success") and 
                "connection" in data.get("data", {}) and
                "api_key" in data["data"]["connection"]):
                print("✅ Connection creation: Success")
                api_key = data["data"]["connection"]["api_key"]
                
                # Test connection validation
                validate_data = {"api_key": api_key}
                validate_response = requests.post(
                    f"{base_url}/api/v1/connections/validate",
                    json=validate_data,
                    timeout=10
                )
                
                if validate_response.status_code == 200:
                    validate_data = validate_response.json()
                    if validate_data.get("success") and validate_data["data"].get("valid"):
                        print("✅ Connection validation: Success")
                        return True
                    else:
                        print(f"❌ Connection validation: Invalid response - {validate_data}")
                        return False
                else:
                    print(f"❌ Connection validation: HTTP {validate_response.status_code}")
                    return False
            else:
                print(f"❌ Connection creation: Invalid response format - {data}")
                return False
        else:
            print(f"❌ Connection creation: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Connection endpoints: Failed - {e}")
        return False


def test_vscode_endpoints():
    """Test VSCode integration endpoints."""
    print("\n💻 Testing VSCode Integration Endpoints...")
    
    base_url = "http://127.0.0.1:8005"
    
    # First create an API key for authentication
    try:
        create_data = {
            "identifier": "<EMAIL>",
            "name": "VSCode Test User"
        }
        
        response = requests.post(
            f"{base_url}/api/v1/connections",
            json=create_data,
            timeout=10
        )
        
        if response.status_code != 201:
            print("❌ Failed to create API key for VSCode test")
            return False
            
        api_key = response.json()["data"]["connection"]["api_key"]
        headers = {"Authorization": f"Bearer {api_key}"}
        
        # Test status endpoint (no auth required)
        status_response = requests.get(f"{base_url}/api/v1/vscode/status", timeout=10)
        if status_response.status_code == 200:
            status_data = status_response.json()
            if status_data.get("success"):
                print("✅ VSCode status: Success")
            else:
                print(f"❌ VSCode status: Invalid format - {status_data}")
                return False
        else:
            print(f"❌ VSCode status: HTTP {status_response.status_code}")
            return False
        
        # Test chunk endpoint (auth required)
        chunk_data = {
            "metadata": {
                "file_path": "/test/file.py",
                "chunk_index": 0
            },
            "content": "print('Hello, World!')"
        }
        
        chunk_response = requests.post(
            f"{base_url}/api/v1/vscode/chunks",
            json=chunk_data,
            headers=headers,
            timeout=15
        )
        
        if chunk_response.status_code == 201:
            chunk_data = chunk_response.json()
            if (chunk_data.get("success") and 
                "chunk_id" in chunk_data.get("data", {}) and
                "job_id" in chunk_data.get("data", {})):
                print("✅ VSCode chunk processing: Success")
                return True
            else:
                print(f"❌ VSCode chunk processing: Invalid format - {chunk_data}")
                return False
        else:
            print(f"❌ VSCode chunk processing: HTTP {chunk_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ VSCode endpoints: Failed - {e}")
        return False


def test_job_endpoints():
    """Test job management endpoints."""
    print("\n⚙️ Testing Job Management Endpoints...")
    
    base_url = "http://127.0.0.1:8005"
    
    # Create API key for authentication
    try:
        create_data = {
            "identifier": "<EMAIL>",
            "name": "Jobs Test User"
        }
        
        response = requests.post(
            f"{base_url}/api/v1/connections",
            json=create_data,
            timeout=10
        )
        
        if response.status_code != 201:
            print("❌ Failed to create API key for jobs test")
            return False
            
        api_key = response.json()["data"]["connection"]["api_key"]
        headers = {"Authorization": f"Bearer {api_key}"}
        
        # Test jobs list endpoint
        jobs_response = requests.get(
            f"{base_url}/api/v1/jobs",
            headers=headers,
            timeout=10
        )
        
        if jobs_response.status_code == 200:
            jobs_data = jobs_response.json()
            if (jobs_data.get("success") and 
                "jobs" in jobs_data.get("data", {}) and
                "total_count" in jobs_data.get("data", {})):
                print("✅ Jobs list: Success")
                
                # Test jobs stats endpoint
                stats_response = requests.get(
                    f"{base_url}/api/v1/jobs/stats",
                    headers=headers,
                    timeout=10
                )
                
                if stats_response.status_code == 200:
                    stats_data = stats_response.json()
                    if stats_data.get("success"):
                        print("✅ Jobs stats: Success")
                        return True
                    else:
                        print(f"❌ Jobs stats: Invalid format - {stats_data}")
                        return False
                else:
                    print(f"❌ Jobs stats: HTTP {stats_response.status_code}")
                    return False
            else:
                print(f"❌ Jobs list: Invalid format - {jobs_data}")
                return False
        else:
            print(f"❌ Jobs list: HTTP {jobs_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Job endpoints: Failed - {e}")
        return False


def main():
    """Run all endpoint validation tests."""
    print("🚀 Starting Endpoint Validation Tests")
    print("=" * 50)
    
    # Start test server
    server_process = start_test_server()
    if not server_process:
        print("❌ Failed to start test server")
        return False
    
    try:
        # Run tests
        tests = [
            ("Health & Monitoring", test_health_endpoints),
            ("Connection Management", test_connection_endpoints),
            ("VSCode Integration", test_vscode_endpoints),
            ("Job Management", test_job_endpoints),
        ]
        
        results = []
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name} failed with exception: {e}")
                results.append((test_name, False))
        
        # Summary
        print("\n" + "=" * 50)
        print("🏁 Endpoint Validation Summary")
        print("=" * 50)
        
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
            if result:
                passed += 1
        
        print(f"\nResults: {passed}/{total} endpoint tests passed")
        
        if passed == total:
            print("🎉 All endpoint tests PASSED! API is working correctly.")
            return True
        else:
            print("⚠️  Some endpoint tests FAILED. Review issues above.")
            return False
    
    finally:
        # Cleanup
        print("\n🧹 Cleaning up test server...")
        server_process.terminate()
        server_process.wait()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Hybrid Cache System Demo - "Smart & Practical" Approach

This demo shows how Redis handles the light stuff (coordination)
while the server handles the heavy stuff (actual data).

Perfect for your free tier Redis! 🎉
"""

import asyncio
import sys
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from cache.hybrid_cache_system import get_hybrid_cache, JobStatus


async def demo_user_session_management():
    """Demo: Redis handles user sessions (lightweight!)"""
    print("🧑‍💻 Demo: User Session Management (Redis)")
    
    cache = await get_hybrid_cache()
    
    # Track user sessions
    users = ["alice", "bob", "charlie"]
    for user in users:
        session_data = {
            'active_jobs': 2,
            'connections': 1,
            'last_action': 'code_indexing'
        }
        
        success = await cache.track_user_session(user, session_data)
        print(f"  📝 Tracked session for {user}: {'✅' if success else '❌'}")
    
    # Get active users
    active_users = await cache.get_active_users()
    print(f"  👥 Active users: {active_users}")
    
    print()


async def demo_job_coordination():
    """Demo: Redis coordinates jobs (no heavy data!)"""
    print("⚙️ Demo: Job Coordination (Redis)")
    
    cache = await get_hybrid_cache()
    
    # Start some jobs
    job_metadata = {
        'file_path': '/workspace/main.py',
        'chunk_count': 15,
        'priority': 1
    }
    
    job_id = await cache.start_job("alice", "code_indexing", job_metadata)
    print(f"  🚀 Started job: {job_id}")
    
    # Update job status
    await cache.update_job_status(job_id, JobStatus.PROCESSING)
    print(f"  📊 Updated job status: PROCESSING")
    
    # Complete job
    result_summary = {
        'chunks_processed': 15,
        'embeddings_generated': 15,
        'processing_time_ms': 2500,
        'success': True
    }
    await cache.update_job_status(job_id, JobStatus.COMPLETED, result_summary)
    print(f"  ✅ Job completed with summary")
    
    # Get user jobs
    user_jobs = await cache.get_user_jobs("alice")
    print(f"  📋 Alice's jobs: {len(user_jobs)} found")
    
    print()


async def demo_embedding_caching():
    """Demo: Server handles embeddings (heavy data!)"""
    print("🧠 Demo: Embedding Caching (Server)")
    
    cache = await get_hybrid_cache()
    
    # Cache some embeddings
    test_code_chunks = [
        "def hello_world(): print('Hello!')",
        "class DataProcessor: pass",
        "import numpy as np",
        "def hello_world(): print('Hello!')",  # Duplicate!
    ]
    
    embeddings = []
    for i, chunk in enumerate(test_code_chunks):
        # Simulate embedding (normally from OpenAI)
        fake_embedding = [0.1 + i * 0.1] * 1536  # 1536-dim vector
        
        result = await cache.cache_embedding(
            chunk, fake_embedding, "alice", 
            metadata={'chunk_type': 'function', 'language': 'python'}
        )
        
        status = "✅" if result.success else "❌"
        source = result.source
        time_ms = result.processing_time_ms
        
        print(f"  {status} Cached embedding #{i+1} ({source}, {time_ms:.1f}ms)")
        
        if result.source == "duplicate_detected":
            print(f"    🔄 Duplicate detected - Redis saved us from reprocessing!")
    
    # Retrieve embeddings
    print(f"\n  📥 Retrieving embeddings:")
    for i, chunk in enumerate(test_code_chunks[:2]):  # Just first 2
        result = await cache.get_cached_embedding(chunk)
        
        status = "✅" if result.success else "❌"
        time_ms = result.processing_time_ms
        
        print(f"    {status} Retrieved embedding #{i+1} ({time_ms:.1f}ms)")
    
    print()


async def demo_chunk_caching():
    """Demo: Server handles chunk content (heavy data!)"""
    print("📄 Demo: Chunk Caching (Server)")
    
    cache = await get_hybrid_cache()
    
    # Cache some chunks
    test_chunks = [
        {
            'file_path': '/workspace/main.py',
            'chunk_index': 0,
            'content': 'def main():\n    print("Hello, World!")\n    return 0',
            'metadata': {'function_count': 1, 'line_count': 3}
        },
        {
            'file_path': '/workspace/utils.py', 
            'chunk_index': 0,
            'content': 'import os\nimport sys\n\ndef get_env(key):\n    return os.getenv(key)',
            'metadata': {'function_count': 1, 'import_count': 2}
        }
    ]
    
    for i, chunk_data in enumerate(test_chunks):
        result = await cache.cache_chunk(
            chunk_data['file_path'],
            chunk_data['chunk_index'],
            chunk_data['content'],
            chunk_data['metadata'],
            "alice"
        )
        
        status = "✅" if result.success else "❌"
        time_ms = result.processing_time_ms
        
        print(f"  {status} Cached chunk #{i+1} ({time_ms:.1f}ms)")
    
    # Retrieve chunks
    print(f"\n  📥 Retrieving chunks:")
    for chunk_data in test_chunks:
        result = await cache.get_cached_chunk(
            chunk_data['file_path'],
            chunk_data['chunk_index']
        )
        
        status = "✅" if result.success else "❌"
        time_ms = result.processing_time_ms
        
        print(f"    {status} Retrieved {chunk_data['file_path']} ({time_ms:.1f}ms)")
    
    print()


async def demo_batch_operations():
    """Demo: Efficient batch operations"""
    print("⚡ Demo: Batch Operations (Hybrid)")
    
    cache = await get_hybrid_cache()
    
    # Batch cache embeddings
    content_embedding_pairs = [
        ("function add(a, b) { return a + b; }", [0.2] * 1536),
        ("const PI = 3.14159;", [0.3] * 1536),
        ("class Calculator { }", [0.4] * 1536),
        ("function add(a, b) { return a + b; }", [0.2] * 1536),  # Duplicate!
    ]
    
    result = await cache.batch_cache_embeddings(content_embedding_pairs, "bob")
    
    if result.success:
        data = result.data
        cached_count = data['cached_count']
        duplicates_filtered = data['duplicates_filtered']
        time_ms = result.processing_time_ms
        
        print(f"  ⚡ Batch cached {cached_count} embeddings ({time_ms:.1f}ms)")
        print(f"    🔄 Filtered {duplicates_filtered} duplicates (Redis coordination)")
    else:
        print(f"  ❌ Batch caching failed")
    
    print()


async def demo_performance_stats():
    """Demo: Performance monitoring"""
    print("📊 Demo: Performance Statistics")
    
    cache = await get_hybrid_cache()
    
    # Get comprehensive stats
    stats = await cache.get_comprehensive_stats()
    
    print(f"  🔧 Redis Operations: {stats['redis_operations']}")
    print(f"  💾 Server Operations: {stats['server_operations']}")
    print(f"  🔄 Hybrid Operations: {stats['hybrid_operations']}")
    print(f"  ⚠️ Fallback Operations: {stats['fallback_operations']}")
    
    hybrid_perf = stats['hybrid_performance']
    print(f"\n  📈 Performance Breakdown:")
    print(f"    Redis: {hybrid_perf['redis_percentage']:.1f}%")
    print(f"    Server: {hybrid_perf['server_percentage']:.1f}%")
    print(f"    Hybrid: {hybrid_perf['hybrid_percentage']:.1f}%")
    print(f"    Avg Operation: {hybrid_perf['avg_operation_time_ms']:.1f}ms")
    
    # Health check
    health = await cache.health_check()
    print(f"\n  🏥 System Health: {health['hybrid_system']}")
    print(f"    Redis Coordination: {health['redis_coordination']}")
    print(f"    Server Cache: {health['server_cache']}")
    
    print()


async def main():
    """Run the hybrid cache demo."""
    print("🎉 Hybrid Cache System Demo - Smart & Practical!")
    print("=" * 60)
    print("Redis = Traffic Cop (sessions, jobs, coordination)")
    print("Server = Heavy Lifter (embeddings, chunks, data)")
    print("=" * 60)
    
    try:
        await demo_user_session_management()
        await demo_job_coordination()
        await demo_embedding_caching()
        await demo_chunk_caching()
        await demo_batch_operations()
        await demo_performance_stats()
        
        print("🎉 Demo Complete!")
        print("\n💡 Key Benefits:")
        print("  ✅ Redis stays under 25MB (lightweight coordination)")
        print("  ✅ Server handles heavy data (unlimited storage)")
        print("  ✅ Automatic fallback (works even if Redis fails)")
        print("  ✅ Smart deduplication (Redis prevents reprocessing)")
        print("  ✅ Excellent performance (right tool for right job)")
        
        print("\n🚀 Ready for Production!")
        print("  - Free tier Redis works perfectly for coordination")
        print("  - Server cache scales with your Railway resources")
        print("  - No more memory limit headaches!")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())

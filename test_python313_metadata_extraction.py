#!/usr/bin/env python3
"""
Test Python 3.13 Enhanced Metadata Extraction System

This script tests the enhanced Python metadata extraction system
with Python 3.13 grammar features support.
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from metadata_extraction import extract_chunk_metadata, get_metadata_extractor_factory


def test_python313_type_aliases():
    """Test extraction of Python 3.13 type aliases."""
    print("\n🔧 Testing Python 3.13 Type Aliases")
    
    test_cases = [
        {
            'name': 'Simple type alias',
            'content': '''type Vector = list[float]
type Matrix = list[Vector]

def process_matrix(m: Matrix) -> Vector:
    return m[0]''',
            'expected_type': 'type_alias',
            'expected_aliases': ['Vector', 'Matrix'],
            'expected_function': 'process_matrix'
        },
        {
            'name': 'Type alias with type parameters',
            'content': '''type GenericAlias[T] = dict[str, T]
type OptionalAlias[T] = T | None

class DataProcessor:
    def process[T](self, data: GenericAlias[T]) -> OptionalAlias[T]:
        return data.get('result')''',
            'expected_type': 'type_alias',
            'expected_aliases': ['GenericAlias', 'OptionalAlias'],
            'expected_class': 'DataProcessor'
        },
        {
            'name': 'Type alias with defaults (Python 3.13)',
            'content': '''type DefaultDict[K, V = str] = dict[K, V]
type OptionalList[T = int] = list[T] | None

def create_data() -> DefaultDict[str]:
    return {}''',
            'expected_type': 'type_alias',
            'expected_aliases': ['DefaultDict', 'OptionalList'],
            'expected_features': ['type_alias', 'type_parameter_defaults']
        }
    ]
    
    for test_case in test_cases:
        print(f"\n  Testing: {test_case['name']}")
        
        result = extract_chunk_metadata(test_case['content'], 'test.py')
        
        print(f"    Chunk type: {result.chunk_type}")
        print(f"    Type aliases: {getattr(result, 'type_aliases', [])}")
        print(f"    Python 3.13 features: {getattr(result, 'python313_features', [])}")
        print(f"    Extraction method: {result.extraction_method}")
        print(f"    Confidence: {result.confidence}")
        
        # Verify expectations
        if result.chunk_type == test_case['expected_type']:
            print(f"    ✅ Chunk type correct")
        else:
            print(f"    ❌ Expected '{test_case['expected_type']}', got '{result.chunk_type}'")


def test_python313_pattern_matching():
    """Test extraction with enhanced pattern matching."""
    print("\n🎯 Testing Python 3.13 Pattern Matching")
    
    test_cases = [
        {
            'name': 'Basic match statement',
            'content': '''def process_value(value):
    match value:
        case int() if value > 0:
            return "positive integer"
        case str() if len(value) > 0:
            return "non-empty string"
        case _:
            return "other"''',
            'expected_type': 'function',
            'expected_function': 'process_value',
            'expected_features': ['pattern_matching']
        },
        {
            'name': 'Complex pattern matching',
            'content': '''class DataProcessor:
    def analyze(self, data):
        match data:
            case {"type": "user", "id": user_id, **rest}:
                return self.process_user(user_id, rest)
            case {"type": "order", "items": [*items]} if len(items) > 0:
                return self.process_order(items)
            case _:
                raise ValueError("Unknown data type")''',
            'expected_type': 'class',
            'expected_class': 'DataProcessor',
            'expected_features': ['pattern_matching']
        }
    ]
    
    for test_case in test_cases:
        print(f"\n  Testing: {test_case['name']}")
        
        result = extract_chunk_metadata(test_case['content'], 'test.py')
        
        print(f"    Chunk type: {result.chunk_type}")
        print(f"    Function name: {result.function_name}")
        print(f"    Class name: {result.class_name}")
        print(f"    Python 3.13 features: {getattr(result, 'python313_features', [])}")
        print(f"    Complexity score: {result.complexity_score}")
        
        # Verify expectations
        if result.chunk_type == test_case['expected_type']:
            print(f"    ✅ Chunk type correct")
        else:
            print(f"    ❌ Expected '{test_case['expected_type']}', got '{result.chunk_type}'")


def test_python313_exception_groups():
    """Test extraction with exception groups (except*)."""
    print("\n⚡ Testing Python 3.13 Exception Groups")
    
    test_cases = [
        {
            'name': 'Exception groups with except*',
            'content': '''async def process_multiple_tasks():
    try:
        async with asyncio.TaskGroup() as tg:
            task1 = tg.create_task(fetch_data(1))
            task2 = tg.create_task(fetch_data(2))
    except* ValueError as eg:
        for error in eg.exceptions:
            print(f"ValueError: {error}")
    except* ConnectionError as eg:
        for error in eg.exceptions:
            print(f"ConnectionError: {error}")''',
            'expected_type': 'function',
            'expected_function': 'process_multiple_tasks',
            'expected_features': ['exception_groups']
        }
    ]
    
    for test_case in test_cases:
        print(f"\n  Testing: {test_case['name']}")
        
        result = extract_chunk_metadata(test_case['content'], 'test.py')
        
        print(f"    Function name: {result.function_name}")
        print(f"    Python 3.13 features: {getattr(result, 'python313_features', [])}")
        print(f"    Extraction method: {result.extraction_method}")
        
        # Verify expectations
        if result.function_name == test_case['expected_function']:
            print(f"    ✅ Function name correct")
        else:
            print(f"    ❌ Expected '{test_case['expected_function']}', got '{result.function_name}'")


def test_enhanced_complexity_calculation():
    """Test enhanced complexity calculation with Python 3.13 features."""
    print("\n📊 Testing Enhanced Complexity Calculation")
    
    test_cases = [
        {
            'name': 'Complex Python 3.13 code',
            'content': '''type ProcessorType[T = str] = dict[str, T]

class DataProcessor[T]:
    def __init__(self, config: ProcessorType[T]):
        self.config = config
    
    async def process(self, data: list[T]) -> T | None:
        match data:
            case [first, *rest] if len(rest) > 0:
                try:
                    result = await self._process_item(first)
                    return result
                except* ValueError as eg:
                    for error in eg.exceptions:
                        print(f"Error: {error}")
                except* ConnectionError:
                    return None
            case [single]:
                return single
            case _:
                return None
    
    def format_result(self, value: T) -> str:
        return f"Result: {value!r}"''',
            'expected_min_complexity': 15  # Should be fairly complex
        }
    ]
    
    for test_case in test_cases:
        print(f"\n  Testing: {test_case['name']}")
        
        result = extract_chunk_metadata(test_case['content'], 'test.py')
        
        print(f"    Complexity score: {result.complexity_score}")
        print(f"    Python 3.13 features: {getattr(result, 'python313_features', [])}")
        print(f"    Functions found: {result.functions_found}")
        print(f"    Classes found: {result.classes_found}")
        print(f"    Type aliases: {getattr(result, 'type_aliases', [])}")
        
        if result.complexity_score >= test_case['expected_min_complexity']:
            print(f"    ✅ Complexity calculation enhanced")
        else:
            print(f"    ❌ Expected complexity >= {test_case['expected_min_complexity']}, got {result.complexity_score}")


def test_backward_compatibility():
    """Test that existing functionality still works correctly."""
    print("\n🔄 Testing Backward Compatibility")
    
    test_cases = [
        {
            'name': 'Classic Python function',
            'content': '''def hello_world(name: str) -> str:
    """Say hello to someone."""
    return f"Hello, {name}!"''',
            'expected_function': 'hello_world',
            'expected_type': 'function'
        },
        {
            'name': 'Classic Python class',
            'content': '''class DataProcessor:
    """Process data files."""
    
    def __init__(self, config):
        self.config = config
    
    def process(self, data):
        return data.strip()''',
            'expected_class': 'DataProcessor',
            'expected_type': 'class'
        }
    ]
    
    for test_case in test_cases:
        print(f"\n  Testing: {test_case['name']}")
        
        result = extract_chunk_metadata(test_case['content'], 'test.py')
        
        print(f"    Function name: {result.function_name}")
        print(f"    Class name: {result.class_name}")
        print(f"    Chunk type: {result.chunk_type}")
        print(f"    Extraction method: {result.extraction_method}")
        print(f"    Confidence: {result.confidence}")
        
        # Verify backward compatibility
        function_ok = result.function_name == test_case.get('expected_function', '')
        class_ok = result.class_name == test_case.get('expected_class', '')
        type_ok = result.chunk_type == test_case['expected_type']

        # For classes, we expect the first function to be found, but class takes priority
        if test_case['expected_type'] == 'class':
            function_ok = len(result.functions_found) > 0  # Just check functions are found

        if function_ok and class_ok and type_ok:
            print(f"    ✅ Backward compatibility maintained")
        else:
            print(f"    ❌ Backward compatibility issue detected")
            print(f"        Function: expected '{test_case.get('expected_function', '')}', got '{result.function_name}'")
            print(f"        Class: expected '{test_case.get('expected_class', '')}', got '{result.class_name}'")
            print(f"        Type: expected '{test_case['expected_type']}', got '{result.chunk_type}'")


def main():
    """Run all Python 3.13 enhancement tests."""
    print("🧪 Testing Python 3.13 Enhanced Metadata Extraction System")
    print("=" * 60)
    
    try:
        test_python313_type_aliases()
        test_python313_pattern_matching()
        test_python313_exception_groups()
        test_enhanced_complexity_calculation()
        test_backward_compatibility()
        
        print("\n" + "=" * 60)
        print("✅ All Python 3.13 enhancement tests completed!")
        print("🚀 The enhanced system supports Python 3.13 features while maintaining backward compatibility")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
